/**
 * Performance Optimization System
 * Provides caching, lazy loading, and performance monitoring
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';

// Cache implementation
class PerformanceCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  private maxSize = 100;

  set(key: string, data: any, ttl: number = 300000): void { // 5 minutes default
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  get(key: string): any | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    // Check if entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }

  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

// Global cache instance
export const performanceCache = new PerformanceCache();

// Performance monitoring
export class PerformanceMonitor {
  private metrics: Map<string, number[]> = new Map();

  startTiming(label: string): () => number {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      this.recordMetric(label, duration);
      return duration;
    };
  }

  recordMetric(label: string, value: number): void {
    if (!this.metrics.has(label)) {
      this.metrics.set(label, []);
    }
    
    const values = this.metrics.get(label)!;
    values.push(value);
    
    // Keep only last 100 measurements
    if (values.length > 100) {
      values.shift();
    }
  }

  getMetrics(label: string): { avg: number; min: number; max: number; count: number } | null {
    const values = this.metrics.get(label);
    if (!values || values.length === 0) return null;

    return {
      avg: values.reduce((a, b) => a + b, 0) / values.length,
      min: Math.min(...values),
      max: Math.max(...values),
      count: values.length
    };
  }

  getAllMetrics(): Record<string, { avg: number; min: number; max: number; count: number }> {
    const result: Record<string, any> = {};
    
    for (const [label] of this.metrics) {
      const metrics = this.getMetrics(label);
      if (metrics) {
        result[label] = metrics;
      }
    }
    
    return result;
  }

  clear(): void {
    this.metrics.clear();
  }
}

// Global performance monitor
export const performanceMonitor = new PerformanceMonitor();

// React hooks for performance optimization

/**
 * Hook for caching API responses
 */
export function useCachedFetch<T>(
  url: string,
  options: RequestInit = {},
  ttl: number = 300000
): { data: T | null; loading: boolean; error: Error | null; refetch: () => void } {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const cacheKey = `fetch_${url}_${JSON.stringify(options)}`;

  const fetchData = useCallback(async () => {
    // Check cache first
    const cachedData = performanceCache.get(cacheKey);
    if (cachedData) {
      setData(cachedData);
      setLoading(false);
      return;
    }

    const endTiming = performanceMonitor.startTiming(`fetch_${url}`);

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(url, options);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      // Cache the result
      performanceCache.set(cacheKey, result, ttl);
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'));
    } finally {
      setLoading(false);
      endTiming();
    }
  }, [url, options, ttl, cacheKey]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const refetch = useCallback(() => {
    performanceCache.clear();
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch };
}

/**
 * Hook for debouncing values
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * Hook for throttling function calls
 */
export function useThrottle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): T {
  const [lastCall, setLastCall] = useState(0);

  return useCallback((...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      setLastCall(now);
      return func(...args);
    }
  }, [func, delay, lastCall]) as T;
}

/**
 * Hook for lazy loading components
 */
export function useLazyLoad(threshold: number = 0.1): {
  ref: React.RefObject<HTMLElement>;
  isVisible: boolean;
} {
  const [isVisible, setIsVisible] = useState(false);
  const ref = React.useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [threshold]);

  return { ref, isVisible };
}

/**
 * Hook for memoizing expensive calculations
 */
export function useExpensiveCalculation<T>(
  calculation: () => T,
  dependencies: React.DependencyList
): T {
  return useMemo(() => {
    const endTiming = performanceMonitor.startTiming('expensive_calculation');
    const result = calculation();
    endTiming();
    return result;
  }, dependencies);
}

/**
 * Hook for virtual scrolling
 */
export function useVirtualScroll<T>(
  items: T[],
  itemHeight: number,
  containerHeight: number
): {
  visibleItems: T[];
  startIndex: number;
  endIndex: number;
  totalHeight: number;
  offsetY: number;
} {
  const [scrollTop, setScrollTop] = useState(0);

  const startIndex = Math.floor(scrollTop / itemHeight);
  const endIndex = Math.min(
    startIndex + Math.ceil(containerHeight / itemHeight) + 1,
    items.length - 1
  );

  const visibleItems = items.slice(startIndex, endIndex + 1);
  const totalHeight = items.length * itemHeight;
  const offsetY = startIndex * itemHeight;

  return {
    visibleItems,
    startIndex,
    endIndex,
    totalHeight,
    offsetY
  };
}

/**
 * Performance optimization utilities
 */
export const performanceUtils = {
  // Preload images
  preloadImage: (src: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve();
      img.onerror = reject;
      img.src = src;
    });
  },

  // Preload multiple images
  preloadImages: async (srcs: string[]): Promise<void> => {
    await Promise.all(srcs.map(src => performanceUtils.preloadImage(src)));
  },

  // Measure component render time
  measureRender: <T extends React.ComponentType<any>>(
    Component: T,
    displayName?: string
  ): T => {
    const MeasuredComponent = (props: React.ComponentProps<T>) => {
      const endTiming = performanceMonitor.startTiming(
        `render_${displayName || Component.displayName || Component.name}`
      );

      useEffect(() => {
        endTiming();
      });

      return React.createElement(Component, props);
    };

    MeasuredComponent.displayName = `Measured(${displayName || Component.displayName || Component.name})`;
    return MeasuredComponent as T;
  },

  // Bundle size analyzer
  analyzeBundleSize: (): void => {
    if (typeof window !== 'undefined' && 'performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      console.log('📊 Bundle Performance Analysis:');
      console.log(`- DOM Content Loaded: ${navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart}ms`);
      console.log(`- Load Complete: ${navigation.loadEventEnd - navigation.loadEventStart}ms`);
      console.log(`- Total Page Load: ${navigation.loadEventEnd - navigation.fetchStart}ms`);
      
      // Analyze resource loading
      const resources = performance.getEntriesByType('resource');
      const jsResources = resources.filter(r => r.name.includes('.js'));
      const cssResources = resources.filter(r => r.name.includes('.css'));
      
      console.log(`- JavaScript files: ${jsResources.length}`);
      console.log(`- CSS files: ${cssResources.length}`);
      console.log(`- Total resources: ${resources.length}`);
    }
  },

  // Memory usage monitoring
  getMemoryUsage: (): { used: number; total: number; percentage: number } | null => {
    if (typeof window !== 'undefined' && 'memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
        percentage: Math.round((memory.usedJSHeapSize / memory.totalJSHeapSize) * 100)
      };
    }
    return null;
  },

  // Clear all performance data
  clearAll: (): void => {
    performanceCache.clear();
    performanceMonitor.clear();
  }
};

// Performance context for React
export const PerformanceContext = React.createContext<{
  cache: PerformanceCache;
  monitor: PerformanceMonitor;
  utils: typeof performanceUtils;
}>({
  cache: performanceCache,
  monitor: performanceMonitor,
  utils: performanceUtils
});

export function usePerformance() {
  return React.useContext(PerformanceContext);
}
