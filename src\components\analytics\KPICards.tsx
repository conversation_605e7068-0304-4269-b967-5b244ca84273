import React from 'react';
import { AnalyticsKPI } from '@/lib/analytics/types';

interface KPICardsProps {
  kpis: AnalyticsKPI[];
  loading?: boolean;
}

export default function KPICards({ kpis, loading = false }: KPICardsProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {[1, 2, 3, 4].map((index) => (
          <div key={index} className="bg-white rounded-lg shadow p-6 animate-pulse">
            <div className="flex items-center justify-between mb-4">
              <div className="w-8 h-8 bg-gray-200 rounded"></div>
              <div className="w-12 h-8 bg-gray-200 rounded"></div>
            </div>
            <div className="w-20 h-4 bg-gray-200 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  const formatValue = (kpi: AnalyticsKPI): string => {
    if (kpi.label.includes('Time')) {
      return kpi.value > 0 ? `${kpi.value}m` : '0m';
    }
    return kpi.value.toLocaleString();
  };

  const getTrendColor = (direction?: 'up' | 'down' | 'stable'): string => {
    switch (direction) {
      case 'up': return 'text-green-600';
      case 'down': return 'text-red-600';
      case 'stable': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  };

  const getTrendIcon = (direction?: 'up' | 'down' | 'stable'): string => {
    switch (direction) {
      case 'up': return '↗️';
      case 'down': return '↘️';
      case 'stable': return '➡️';
      default: return '';
    }
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {kpis.map((kpi, index) => (
        <div key={index} className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
          <div className="flex items-center justify-between mb-4">
            <span className="text-2xl">{kpi.icon}</span>
            <span className="text-3xl font-light text-gray-900">
              {formatValue(kpi)}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <h3 className="text-gray-600 text-sm font-medium">{kpi.label}</h3>
            
            {kpi.trend && (
              <div className={`flex items-center text-xs ${getTrendColor(kpi.trend.direction)}`}>
                <span className="mr-1">{getTrendIcon(kpi.trend.direction)}</span>
                <span>{kpi.trend.percentage}%</span>
              </div>
            )}
          </div>
          
          {kpi.trend && (
            <p className="text-xs text-gray-500 mt-1">
              vs {kpi.trend.period}
            </p>
          )}
        </div>
      ))}
    </div>
  );
}
