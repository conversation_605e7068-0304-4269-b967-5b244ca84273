'use client';

import { useAuthenticationStatus, useUserData } from '@nhost/nextjs';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import Link from 'next/link';
import Button from '@/components/ui/Button';
import AssignmentList from '@/components/assignments/AssignmentList';

export default function Dashboard() {
  const { isAuthenticated, isLoading } = useAuthenticationStatus();
  const user = useUserData();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/sign-in');
    }
  }, [isLoading, isAuthenticated, router]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p>Loading...</p>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect in the useEffect
  }

  // Mock data for dashboard
  const stats = [
    { label: 'Courses', value: 5, icon: '📚' },
    { label: 'Assignments', value: 12, icon: '📝' },
    { label: 'Submissions', value: 87, icon: '📤' },
    { label: 'Assessments', value: 76, icon: '🧠' },
  ];

  const recentActivity = [
    { id: 1, type: 'submission', title: 'Essay on Climate Change', student: 'Alex Johnson', date: '2 hours ago' },
    { id: 2, type: 'assessment', title: 'Python Programming Assignment', student: 'Maria Garcia', date: '5 hours ago' },
    { id: 3, type: 'submission', title: 'Art History Project', student: 'James Wilson', date: '1 day ago' },
    { id: 4, type: 'assessment', title: 'Chemistry Lab Report', student: 'Emma Davis', date: '2 days ago' },
  ];

  return (
    <DashboardLayout>
      <div className="mb-8">
        <h1 className="text-3xl font-light mb-2">Dashboard</h1>
        <p className="text-gray-600">Welcome back, {user?.displayName || user?.email}</p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <span className="text-2xl">{stat.icon}</span>
              <span className="text-3xl font-light">{stat.value}</span>
            </div>
            <h3 className="text-gray-600">{stat.label}</h3>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow p-6 mb-8">
        <h2 className="text-xl font-medium mb-4">Quick Actions</h2>
        <div className="flex flex-wrap gap-4">
          <Link href="/analytics">
            <Button variant="primary" className="py-2 px-4">
              📈 View Analytics
            </Button>
          </Link>
          <Link href="/assignments">
            <Button variant="secondary" className="py-2 px-4">
              📝 View Assignments
            </Button>
          </Link>
          <Link href="/assignments/manage">
            <Button variant="outline" className="py-2 px-4">
              ➕ Create Assignment
            </Button>
          </Link>
          <Link href="/student-upload">
            <Button variant="outline" className="py-2 px-4">
              📁 Student Upload
            </Button>
          </Link>
          <Link href="/test-phase4">
            <Button variant="outline" className="py-2 px-4">
              🚀 Phase 4 Demo
            </Button>
          </Link>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg shadow p-6 mb-8">
        <h2 className="text-xl font-medium mb-4">Recent Activity</h2>
        <div className="divide-y">
          {recentActivity.map((activity) => (
            <div key={activity.id} className="py-4 flex items-center justify-between">
              <div>
                <h3 className="font-medium">{activity.title}</h3>
                <p className="text-sm text-gray-600">
                  {activity.type === 'submission' ? 'Submitted by' : 'Assessed for'} {activity.student}
                </p>
              </div>
              <div className="text-right">
                <span className="text-sm text-gray-600">{activity.date}</span>
                <Link href={`/${activity.type}s/${activity.id}`} className="block text-accent text-sm hover:underline">
                  View {activity.type}
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Assignments */}
      <div className="mb-8">
        <AssignmentList />
      </div>
    </DashboardLayout>
  );
}
