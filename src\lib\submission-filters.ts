/**
 * Submission Filtering Infrastructure
 *
 * Smart session-based filtering system to prevent confusion from old assessments
 * while preserving bulk assessment workflow functionality.
 */

export interface StudentSubmission {
  id: string;
  assignment_id: string;
  student_name: string;
  file_path: string;
  file_type: string;
  processing_status: string;
  upload_date: string;
  // Legacy fields for backward compatibility (not in actual database)
  student_id?: string;
  status?: string;
  created_at?: string;
  updated_at?: string;
  submitted_at?: string;
}

/**
 * Session-based filtering modes
 */
export type FilterMode = 'current-session' | 'recent' | 'all';

export interface SessionFilterOptions {
  mode: FilterMode;
  assignmentId?: string;
}

export interface SubmissionFilterOptions {
  /** Filter by assignment ID */
  assignmentId?: string;
  /** Only show submissions with specific statuses */
  statusFilter?: string[];
  /** Maximum number of submissions to return */
  limit?: number;
  /** Sort order */
  sortBy?: 'upload_date' | 'student_name' | 'processing_status';
  sortOrder?: 'asc' | 'desc';
}

export interface FilterResult<T = StudentSubmission> {
  /** Filtered submissions */
  submissions: T[];
  /** Total submissions before filtering */
  totalCount: number;
  /** Number of submissions filtered out */
  filteredCount: number;
  /** Breakdown of what was filtered */
  filterBreakdown: {
    wrongAssignment: number;
    statusFiltered: number;
  };
}

/**
 * Core submission filtering function
 * Used across all pages that display student submissions
 */
export function filterSubmissions<T extends StudentSubmission>(
  submissions: T[],
  options: SubmissionFilterOptions = {}
): FilterResult<T> {
  const {
    assignmentId,
    statusFilter,
    limit,
    sortBy = 'upload_date',
    sortOrder = 'desc'
  } = options;

  const totalCount = submissions.length;
  let filtered = [...submissions];
  
  const filterBreakdown = {
    wrongAssignment: 0,
    statusFiltered: 0
  };

  // 1. Filter by assignment ID - Re-enabled with proper logic
  if (assignmentId) {
    const beforeCount = filtered.length;
    filtered = filtered.filter(sub => sub.assignment_id === assignmentId);
    filterBreakdown.wrongAssignment = beforeCount - filtered.length;
  }

  // 3. Filter by status - Use actual database field
  if (statusFilter && statusFilter.length > 0) {
    const beforeCount = filtered.length;
    filtered = filtered.filter(sub => {
      // Use the actual 'processing_status' field from database
      const currentStatus = sub.processing_status || 'draft';
      return statusFilter.includes(currentStatus);
    });
    filterBreakdown.statusFiltered = beforeCount - filtered.length;
  }

  // Sort submissions - removed time-based sorting

  // 5. Apply limit
  if (limit && limit > 0) {
    filtered = filtered.slice(0, limit);
  }

  return {
    submissions: filtered,
    totalCount,
    filteredCount: totalCount - filtered.length,
    filterBreakdown
  };
}

/**
 * Smart session-based filtering for submissions
 */
export function filterSubmissionsBySession(
  submissions: StudentSubmission[],
  options: SessionFilterOptions
): StudentSubmission[] {
  const { assignmentId } = options;

  let filtered = [...submissions];

  // Always filter by assignment if provided
  if (assignmentId) {
    filtered = filtered.filter(sub => sub.assignment_id === assignmentId);
  }

  return filtered;
}

/**
 * Generate user-friendly filter summary text
 */
export function getFilterSummary(result: FilterResult): string | null {
  const { filteredCount, filterBreakdown } = result;
  
  if (filteredCount === 0) {
    return null;
  }

  const parts: string[] = [];
  
  if (filterBreakdown.wrongAssignment > 0) {
    parts.push(`${filterBreakdown.wrongAssignment} submission${filterBreakdown.wrongAssignment !== 1 ? 's' : ''} from other assignments`);
  }
  
  if (filterBreakdown.statusFiltered > 0) {
    parts.push(`${filterBreakdown.statusFiltered} submission${filterBreakdown.statusFiltered !== 1 ? 's' : ''} with different status`);
  }

  if (parts.length === 0) {
    return `Filtered out ${filteredCount} submission${filteredCount !== 1 ? 's' : ''}`;
  }

  return `🗂️ Filtered out ${parts.join(', ')}`;
}

/**
 * Hook for React components to use submission filtering
 */
export function useSubmissionFilter<T extends StudentSubmission>(
  submissions: T[],
  options: SubmissionFilterOptions = {}
) {
  const result = filterSubmissions(submissions, options);
  const summary = getFilterSummary(result);

  console.log('useSubmissionFilter result:', {
    inputCount: submissions.length,
    outputCount: result.submissions.length,
    options,
    breakdown: result.filterBreakdown,
    inputSubmissions: submissions.map(s => ({ id: s.id, student_name: s.student_name, processing_status: s.processing_status, upload_date: s.upload_date, assignment_id: s.assignment_id })),
    outputSubmissions: result.submissions.map(s => ({ id: s.id, student_name: s.student_name, processing_status: s.processing_status, upload_date: s.upload_date, assignment_id: s.assignment_id }))
  });

  return {
    ...result,
    summary,
    hasFiltered: result.filteredCount > 0
  };
}

/**
 * GraphQL query fragments for consistent submission fetching
 * Based on actual database schema from Hasura metadata
 */
export const SUBMISSION_FIELDS = `
  id
  assignment_id
  student_name
  file_path
  file_type
  processing_status
  upload_date
`;

/**
 * Default filter options for different contexts
 */
export const FILTER_PRESETS = {
  /** For assignment-specific pages */
  ASSIGNMENT_FOCUSED: {
    sortOrder: 'desc' as const
  },

  /** For bulk assessment workflows */
  BULK_ASSESSMENT: {
    statusFilter: ['submitted', 'draft'], // Real database values
    sortOrder: 'asc' as const
  },

  /** For recent activity views */
  RECENT_ACTIVITY: {
    limit: 10,
    sortOrder: 'desc' as const
  },

  /** For administrative views (show everything) */
  ADMIN_VIEW: {
    sortOrder: 'desc' as const
  }
} as const;
