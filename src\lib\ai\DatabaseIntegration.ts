import { gql } from '@apollo/client';
import { AssessmentResult, CriteriaFeedback, Submission } from '@/types/ai-assessment';

// GraphQL mutation for saving AI assessment results to the ACTUAL assessment_results table
export const CREATE_ASSESSMENT_RESULT = gql`
  mutation CreateAssessmentResult(
    $submissionId: uuid!
    $overallGrade: String
    $generalComments: String
    $exportedPdfPath: String
    $reviewed: Boolean
  ) {
    insert_assessment_results_one(object: {
      submission_id: $submissionId
      overall_grade: $overallGrade
      general_comments: $generalComments
      exported_pdf_path: $exportedPdfPath
      reviewed: $reviewed
    }) {
      id
      submission_id
      overall_grade
      general_comments
      created_at
    }
  }
`;

// GraphQL mutation for saving per-criteria feedback
export const CREATE_PER_CRITERION_FEEDBACK = gql`
  mutation CreatePerCriterionFeedback(
    $assessmentResultId: uuid!
    $criterionId: uuid!
    $score: String
    $feedbackText: String
  ) {
    insert_per_criterion_feedback_one(object: {
      assessment_result_id: $assessmentResultId
      criterion_id: $criterionId
      score: $score
      feedback_text: $feedbackText
    }) {
      id
      assessment_result_id
      criterion_id
      score
      feedback_text
    }
  }
`;

// GraphQL mutation for bulk inserting per-criteria feedback
export const CREATE_BULK_PER_CRITERION_FEEDBACK = gql`
  mutation CreateBulkPerCriterionFeedback($feedbacks: [per_criterion_feedback_insert_input!]!) {
    insert_per_criterion_feedback(objects: $feedbacks) {
      affected_rows
      returning {
        id
        assessment_result_id
        criterion_id
        score
        feedback_text
      }
    }
  }
`;

// GraphQL mutation for creating assessment criteria
export const CREATE_ASSESSMENT_CRITERIA = gql`
  mutation CreateAssessmentCriteria($criteria: [assessment_criteria_insert_input!]!) {
    insert_assessment_criteria(objects: $criteria) {
      affected_rows
      returning {
        id
        assignment_id
        criterion_name
        description
        weight
        order
      }
    }
  }
`;

// GraphQL query to get existing assessment criteria for an assignment
export const GET_ASSIGNMENT_CRITERIA = gql`
  query GetAssignmentCriteria($assignment_id: uuid!) {
    assessment_criteria(
      where: { assignment_id: { _eq: $assignment_id } }
      order_by: { order: asc }
    ) {
      id
      assignment_id
      criterion_name
      description
      weight
      order
    }
  }
`;



export const GET_SUBMISSION_WITH_CRITERIA = gql`
  query GetSubmissionWithCriteria($submissionId: uuid!) {
    student_submissions_by_pk(id: $submissionId) {
      id
      student_name
      file_path
      file_type
      assignment {
        id
        title
        description
        assessment_criteria {
          id
          assignment_id
          criterion_name
          description
          weight
          order
        }
      }
    }
  }
`;

/**
 * Database Integration Service
 * Handles saving AI assessment results to the existing database schema
 */
export class DatabaseIntegration {
  private graphqlClient: any;

  constructor(graphqlClient: any) {
    this.graphqlClient = graphqlClient;
  }

  /**
   * Save AI assessment results to the ACTUAL assessment_results table
   */
  async saveAssessmentResults(
    submissionId: string,
    assessmentResult: AssessmentResult,
    criteriaMapping: { [aiCriteriaId: string]: string } // Maps AI criteria IDs to DB criteria IDs
  ): Promise<void> {
    try {
      console.log(`💾 Saving assessment results for submission ${submissionId}`);
      console.log(`🔗 Assignment ID linked: ${assessmentResult.assignmentId}`);

      // Format the complete assessment feedback as general comments
      const generalComments = this.formatGeneralComments(assessmentResult);

      // Create assessment result in the ACTUAL assessment_results table
      const { data: assessmentData } = await this.graphqlClient.mutate({
        mutation: CREATE_ASSESSMENT_RESULT,
        variables: {
          submissionId,
          overallGrade: assessmentResult.overallGrade,
          generalComments: generalComments,
          exportedPdfPath: null, // Will be set later if PDF export is implemented
          reviewed: false // Teacher hasn't reviewed yet
        }
      });

      if (!assessmentData?.insert_assessment_results_one) {
        throw new Error('Failed to create assessment result');
      }

      const assessmentResultId = assessmentData.insert_assessment_results_one.id;
      console.log(`✅ Assessment result created: ${assessmentResultId}`);

      // Save individual criteria feedback to per_criterion_feedback table
      if (assessmentResult.detailedFeedback && assessmentResult.detailedFeedback.length > 0) {
        console.log(`💾 Saving ${assessmentResult.detailedFeedback.length} per-criteria feedbacks...`);

        // Debug: Log AI feedback and mapping
        console.log('🤖 AI Feedback Details:');
        assessmentResult.detailedFeedback.forEach((feedback: CriteriaFeedback, index: number) => {
          console.log(`  ${index + 1}. AI Criteria ID: "${feedback.criteriaId}", Name: "${feedback.criteriaName}", Score: ${feedback.score}`);
        });

        console.log('🗺️ Available Mapping:');
        Object.entries(criteriaMapping).forEach(([templateId, dbId]) => {
          console.log(`  Template ID "${templateId}" -> Database ID "${dbId}"`);
        });

        // Prepare bulk feedback data
        const feedbackData = assessmentResult.detailedFeedback.map((feedback: CriteriaFeedback, index: number) => {
          console.log(`🔍 Processing feedback ${index + 1}: AI ID "${feedback.criteriaId}"`);

          const criterionId = criteriaMapping[feedback.criteriaId];
          if (!criterionId) {
            console.warn(`⚠️ No criterion mapping found for AI criteria ID: "${feedback.criteriaId}" (Name: "${feedback.criteriaName}")`);
            console.warn(`Available mapping keys:`, Object.keys(criteriaMapping));
            return null;
          }

          console.log(`✅ Mapped AI criteria "${feedback.criteriaId}" -> DB criteria "${criterionId}"`);
          return {
            assessment_result_id: assessmentResultId,
            criterion_id: criterionId,
            score: feedback.score?.toString() || null,
            feedback_text: feedback.feedback || null
          };
        }).filter(Boolean); // Remove null entries

        console.log(`📊 Valid feedback entries: ${feedbackData.length}/${assessmentResult.detailedFeedback.length}`);

        if (feedbackData.length > 0) {
          try {
            const feedbackResult = await this.graphqlClient.mutate({
              mutation: CREATE_BULK_PER_CRITERION_FEEDBACK,
              variables: {
                feedbacks: feedbackData
              }
            });

            const savedCount = feedbackResult.data?.insert_per_criterion_feedback?.affected_rows || 0;
            console.log(`✅ Saved ${savedCount} per-criteria feedbacks successfully`);

            // Log details of saved feedbacks
            feedbackResult.data?.insert_per_criterion_feedback?.returning?.forEach((feedback: any) => {
              console.log(`📊 Criterion feedback saved: ID ${feedback.id}, Score: ${feedback.score}`);
            });

          } catch (feedbackError) {
            console.error('❌ Failed to save per-criteria feedback:', feedbackError);
            // Don't throw error here - main assessment was saved successfully
            console.warn('⚠️ Continuing without per-criteria feedback...');
          }
        } else {
          console.warn('⚠️ No valid criteria mappings found - skipping per-criteria feedback');
        }
      } else {
        console.log('ℹ️ No detailed feedback provided by AI - skipping per-criteria feedback');
      }

      console.log(`✅ Assessment results saved successfully`);
      console.log(`🔗 Linked to Assignment: ${assessmentResult.assignmentId}`);
      console.log(`📊 Overall grade: ${assessmentResult.overallGrade}`);
      console.log(`📝 Processed ${assessmentResult.detailedFeedback.length} criteria assessments`);
      console.log(`🤖 AI Provider: ${assessmentResult.aiProvider} (${assessmentResult.modelUsed})`);

    } catch (error) {
      console.error('❌ Failed to save assessment results:', error);
      throw new Error(`Database save failed: ${error}`);
    }
  }

  /**
   * Get all submissions for an assignment (for bulk processing)
   */
  async getSubmissionsByAssignment(assignmentId: string): Promise<Submission[]> {
    try {
      console.log(`📋 Getting submissions for assignment: ${assignmentId}`);

      const GET_ASSIGNMENT_SUBMISSIONS = gql`
        query GetAssignmentSubmissions($assignmentId: uuid!) {
          student_submissions(where: { assignment_id: { _eq: $assignmentId } }) {
            id
            student_name
            file_path
            file_type
            upload_date
            assignment {
              id
              title
              description
              assessment_criteria {
                id
                criterion_name
                description
                weight
              }
            }
            assessment_results {
              id
              overall_grade
            }
          }
        }
      `;

      const { data } = await this.graphqlClient.query({
        query: GET_ASSIGNMENT_SUBMISSIONS,
        variables: { assignmentId }
      });

      const submissions = data?.student_submissions || [];
      console.log(`📊 Found ${submissions.length} submissions for assignment`);

      // Convert to Submission format
      const formattedSubmissions: Submission[] = await Promise.all(
        submissions.map(async (submission: any) => ({
          id: submission.id,
          studentName: submission.student_name,
          fileName: this.extractFileName(submission.file_path),
          fileType: submission.file_type,
          content: await this.getFileContent(submission.file_path),
          uploadDate: new Date(submission.upload_date),
          assignment: {
            id: submission.assignment?.id || '',
            title: submission.assignment?.title || '',
            description: submission.assignment?.description || ''
          },
          criteria: submission.assignment?.assessment_criteria?.map((criterion: any) => ({
            id: criterion.id,
            name: criterion.criterion_name,
            description: criterion.description,
            weight: criterion.weight,
            maxScore: 100
          })) || [],
          assessment_results: submission.assessment_results
        }))
      );

      return formattedSubmissions;

    } catch (error) {
      console.error('❌ Failed to get assignment submissions:', error);
      throw error;
    }
  }

  /**
   * Get submission data with criteria for AI processing
   */
  async getSubmissionForAssessment(submissionId: string): Promise<any> {
    try {
      const { data } = await this.graphqlClient.query({
        query: GET_SUBMISSION_WITH_CRITERIA,
        variables: { submissionId }
      });

      const submission = data?.student_submissions_by_pk;
      if (!submission) {
        throw new Error(`Submission ${submissionId} not found`);
      }

      return {
        id: submission.id,
        studentName: submission.student_name,
        fileName: this.extractFileName(submission.file_path),
        fileType: submission.file_type,
        content: await this.getFileContent(submission.file_path),
        uploadDate: new Date(),
        assignment: {
          id: submission.assignment?.id || '',
          title: submission.assignment?.title || '',
          description: submission.assignment?.description || ''
        },
        criteria: submission.assignment?.assessment_criteria?.map((criterion: any) => ({
          id: criterion.id,
          name: criterion.criterion_name,
          description: criterion.description,
          weight: criterion.weight,
          maxScore: 100 // Default max score, can be configured later
        })) || []
      };

    } catch (error) {
      console.error('❌ Failed to get submission data:', error);
      throw error;
    }
  }

  /**
   * Ensure assessment criteria exist in database for an assignment
   */
  async ensureAssessmentCriteria(
    assignmentId: string,
    templateCriteria: any[]
  ): Promise<void> {
    try {
      console.log(`🔍 Checking existing criteria for assignment: ${assignmentId}`);

      // Check if criteria already exist
      const { data } = await this.graphqlClient.query({
        query: GET_ASSIGNMENT_CRITERIA,
        variables: { assignment_id: assignmentId },
        fetchPolicy: 'no-cache' // Force fresh data
      });

      console.log('🔍 Raw GraphQL response:', JSON.stringify(data, null, 2));
      const existingCriteria = data?.assessment_criteria || [];
      console.log(`📊 Found ${existingCriteria.length} existing criteria`);

      if (existingCriteria.length > 0) {
        console.log(`✅ Found ${existingCriteria.length} existing criteria, skipping creation`);
        existingCriteria.forEach((criterion: any, index: number) => {
          console.log(`  ${index + 1}. "${criterion.criterion_name}" (ID: ${criterion.id})`);
        });
        return;
      }

      console.log(`📝 Creating ${templateCriteria.length} assessment criteria...`);

      // Create criteria from template
      const criteriaToCreate = templateCriteria.map((criterion, index) => ({
        assignment_id: assignmentId,
        criterion_name: criterion.name,
        description: criterion.description,
        weight: criterion.weight,
        order: index + 1
      }));

      console.log('📋 Criteria to create:', JSON.stringify(criteriaToCreate, null, 2));

      const result = await this.graphqlClient.mutate({
        mutation: CREATE_ASSESSMENT_CRITERIA,
        variables: { criteria: criteriaToCreate }
      });

      console.log('🔍 Create criteria result:', JSON.stringify(result.data, null, 2));
      const createdCount = result.data?.insert_assessment_criteria?.affected_rows || 0;
      console.log(`✅ Created ${createdCount} assessment criteria successfully`);

      if (createdCount > 0) {
        result.data?.insert_assessment_criteria?.returning?.forEach((criterion: any, index: number) => {
          console.log(`  ${index + 1}. Created "${criterion.criterion_name}" (ID: ${criterion.id})`);
        });
      }

    } catch (error) {
      console.error('❌ Failed to ensure assessment criteria:', error);
      throw error;
    }
  }

  /**
   * Create criteria mapping from template criteria to database criteria
   */
  async createCriteriaMappingFromTemplate(
    assignmentId: string,
    templateCriteria: any[]
  ): Promise<{ [templateCriteriaId: string]: string }> {
    try {
      console.log(`🔍 Creating criteria mapping for assignment: ${assignmentId}`);
      console.log(`📝 Template criteria:`, templateCriteria.map(c => ({ id: c.id, name: c.name })));

      // Get database criteria for this assignment
      const { data } = await this.graphqlClient.query({
        query: GET_ASSIGNMENT_CRITERIA,
        variables: { assignment_id: assignmentId }
      });

      const dbCriteria = data?.assessment_criteria || [];
      console.log(`🗄️ Database criteria:`, dbCriteria.map((c: any) => ({ id: c.id, name: c.criterion_name })));

      const mapping: { [templateCriteriaId: string]: string } = {};

      templateCriteria.forEach((templateCriterion, index) => {
        console.log(`🔍 Mapping template criterion: "${templateCriterion.name}" (ID: ${templateCriterion.id})`);

        // Try to match by name first
        const dbMatch = dbCriteria.find((db: any) =>
          db.criterion_name.toLowerCase() === templateCriterion.name.toLowerCase()
        );

        if (dbMatch) {
          mapping[templateCriterion.id] = dbMatch.id;
          console.log(`✅ Exact name match: "${templateCriterion.name}" -> ${dbMatch.id}`);
        } else if (dbCriteria[index]) {
          // Fallback to index-based matching
          mapping[templateCriterion.id] = dbCriteria[index].id;
          console.log(`⚠️ Index-based match: "${templateCriterion.name}" -> ${dbCriteria[index].id} (${dbCriteria[index].criterion_name})`);
        } else {
          console.error(`❌ No mapping found for template criteria: ${templateCriterion.name}`);
          console.error(`Available DB criteria names:`, dbCriteria.map((c: any) => c.criterion_name));
        }
      });

      console.log(`🗺️ Final mapping:`, mapping);
      console.log(`📊 Mapped ${Object.keys(mapping).length}/${templateCriteria.length} criteria`);
      return mapping;

    } catch (error) {
      console.error('❌ Failed to create criteria mapping:', error);
      throw error;
    }
  }

  /**
   * Create criteria mapping between AI results and database (legacy method)
   */
  createCriteriaMapping(
    aiCriteria: CriteriaFeedback[],
    dbCriteria: any[]
  ): { [aiCriteriaId: string]: string } {
    const mapping: { [aiCriteriaId: string]: string } = {};

    aiCriteria.forEach((aiCriterion, index) => {
      // Try to match by criterion_name first
      const dbMatch = dbCriteria.find(db =>
        db.criterion_name.toLowerCase() === aiCriterion.criteriaName.toLowerCase()
      );

      if (dbMatch) {
        mapping[aiCriterion.criteriaId] = dbMatch.id;
      } else if (dbCriteria[index]) {
        // Fallback to index-based matching
        mapping[aiCriterion.criteriaId] = dbCriteria[index].id;
        console.warn(`Using index-based mapping for criteria: ${aiCriterion.criteriaName}`);
      } else {
        console.error(`No mapping found for AI criteria: ${aiCriterion.criteriaName}`);
      }
    });

    return mapping;
  }

  /**
   * Format general comments from AI assessment for the assessment_results table
   */
  private formatGeneralComments(assessment: AssessmentResult): string {
    let comments = assessment.feedbackSummary;

    if (assessment.strengths.length > 0) {
      comments += '\n\n**Strengths:**\n' + assessment.strengths.map(s => `• ${s}`).join('\n');
    }

    if (assessment.areasForImprovement.length > 0) {
      comments += '\n\n**Areas for Improvement:**\n' + assessment.areasForImprovement.map(a => `• ${a}`).join('\n');
    }

    if (assessment.suggestions.length > 0) {
      comments += '\n\n**Suggestions:**\n' + assessment.suggestions.map(s => `• ${s}`).join('\n');
    }

    comments += `\n\n*Assessment generated by ${assessment.aiProvider} (${assessment.modelUsed}) with ${(assessment.confidenceScore * 100).toFixed(0)}% confidence*`;

    return comments;
  }



  /**
   * Extract filename from file path
   */
  private extractFileName(filePath: string): string {
    if (!filePath) return 'Unknown File';
    return filePath.split('/').pop() || filePath;
  }

  /**
   * Get file content for AI processing
   */
  private async getFileContent(filePath: string): Promise<string> {
    try {
      console.log(`📄 Reading file content from: ${filePath}`);

      // Try to fetch the actual file content from Nhost storage
      const nhostStorageUrl = `${process.env.NEXT_PUBLIC_NHOST_STORAGE_URL}/files/${filePath}`;

      try {
        const response = await fetch(nhostStorageUrl);
        if (response.ok) {
          const content = await response.text();
          console.log(`✅ Successfully read file content (${content.length} characters)`);
          return content;
        } else {
          console.warn(`⚠️ Could not fetch file from storage: ${response.status}`);
        }
      } catch (fetchError) {
        console.warn(`⚠️ Error fetching file from storage:`, fetchError);
      }

      // Fallback: Return a placeholder indicating the file type and name
      const fileName = this.extractFileName(filePath);
      const fileExtension = fileName.split('.').pop()?.toLowerCase();

      return `[File Content Placeholder]

File: ${fileName}
Type: ${fileExtension}

Note: This is a placeholder for the actual file content. In a production environment,
the AI assessment would analyze the real content of the uploaded file: ${filePath}

For demonstration purposes, this placeholder indicates that the file was successfully
uploaded and is ready for AI assessment processing.`;

    } catch (error) {
      console.error(`❌ Error reading file content:`, error);
      return `Error reading file content from ${filePath}. Using fallback content for assessment.`;
    }
  }
}
