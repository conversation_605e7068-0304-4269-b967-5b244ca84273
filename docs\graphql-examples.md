# GraphQL Query and Mutation Examples

This document provides example GraphQL queries and mutations for your AI Grading and Assessment SaaS platform. You can use these as a starting point for implementing your frontend components.

## Authentication Queries

### Get Current User

```graphql
query GetCurrentUser {
  user {
    id
    email
    displayName
    avatarUrl
    metadata
  }
}
```

## Organization Queries and Mutations

### Get User's Organizations

```graphql
query GetUserOrganizations {
  organizations(where: {
    organization_members: {
      user_id: { _eq: "X-Hasura-User-Id" }
    }
  }) {
    id
    name
    description
    logo_url
    created_at
    organization_members {
      role
    }
  }
}
```

### Create Organization

```graphql
mutation CreateOrganization($name: String!, $description: String, $logo_url: String) {
  insert_organizations_one(object: {
    name: $name,
    description: $description,
    logo_url: $logo_url,
    organization_members: {
      data: {
        user_id: "X-Hasura-User-Id",
        role: "admin"
      }
    }
  }) {
    id
    name
    created_at
  }
}
```

### Update Organization

```graphql
mutation UpdateOrganization($id: uuid!, $name: String!, $description: String, $logo_url: String) {
  update_organizations_by_pk(
    pk_columns: { id: $id },
    _set: {
      name: $name,
      description: $description,
      logo_url: $logo_url
    }
  ) {
    id
    name
    description
    logo_url
    updated_at
  }
}
```

### Delete Organization

```graphql
mutation DeleteOrganization($id: uuid!) {
  delete_organizations_by_pk(id: $id) {
    id
  }
}
```

## Course Queries and Mutations

### Get Organization Courses

```graphql
query GetOrganizationCourses($organization_id: uuid!) {
  courses(where: { organization_id: { _eq: $organization_id } }) {
    id
    name
    description
    start_date
    end_date
    created_at
  }
}
```

### Create Course

```graphql
mutation CreateCourse($organization_id: uuid!, $name: String!, $description: String, $start_date: date, $end_date: date) {
  insert_courses_one(object: {
    organization_id: $organization_id,
    name: $name,
    description: $description,
    start_date: $start_date,
    end_date: $end_date
  }) {
    id
    name
    created_at
  }
}
```

### Update Course

```graphql
mutation UpdateCourse($id: uuid!, $name: String!, $description: String, $start_date: date, $end_date: date) {
  update_courses_by_pk(
    pk_columns: { id: $id },
    _set: {
      name: $name,
      description: $description,
      start_date: $start_date,
      end_date: $end_date
    }
  ) {
    id
    name
    description
    start_date
    end_date
    updated_at
  }
}
```

### Delete Course

```graphql
mutation DeleteCourse($id: uuid!) {
  delete_courses_by_pk(id: $id) {
    id
  }
}
```

## Assignment Queries and Mutations

### Get Course Assignments

```graphql
query GetCourseAssignments($course_id: uuid!) {
  assignments(where: { course_id: { _eq: $course_id } }) {
    id
    title
    description
    due_date
    max_score
    assignment_type
    created_at
    assessment_criteria {
      id
      name
      description
      weight
      order_index
    }
  }
}
```

### Create Assignment

```graphql
mutation CreateAssignment(
  $course_id: uuid!,
  $title: String!,
  $description: String,
  $due_date: timestamptz,
  $max_score: Int,
  $assignment_type: String!,
  $criteria: [assessment_criteria_insert_input!]!
) {
  insert_assignments_one(object: {
    course_id: $course_id,
    title: $title,
    description: $description,
    due_date: $due_date,
    max_score: $max_score,
    assignment_type: $assignment_type,
    assessment_criteria: {
      data: $criteria
    }
  }) {
    id
    title
    created_at
    assessment_criteria {
      id
      name
      weight
    }
  }
}
```

### Update Assignment

```graphql
mutation UpdateAssignment(
  $id: uuid!,
  $title: String!,
  $description: String,
  $due_date: timestamptz,
  $max_score: Int,
  $assignment_type: String!
) {
  update_assignments_by_pk(
    pk_columns: { id: $id },
    _set: {
      title: $title,
      description: $description,
      due_date: $due_date,
      max_score: $max_score,
      assignment_type: $assignment_type
    }
  ) {
    id
    title
    description
    due_date
    max_score
    assignment_type
    updated_at
  }
}
```

### Delete Assignment

```graphql
mutation DeleteAssignment($id: uuid!) {
  delete_assignments_by_pk(id: $id) {
    id
  }
}
```

## Submission Queries and Mutations

### Get Assignment Submissions

```graphql
query GetAssignmentSubmissions($assignment_id: uuid!) {
  student_submissions(where: { assignment_id: { _eq: $assignment_id } }) {
    id
    student_id
    status
    submitted_at
    student {
      displayName
      email
    }
    submission_files {
      id
      file_name
      file_type
      file {
        id
        name
        size
        mimeType
        bucketId
      }
    }
    ai_assessments {
      id
      overall_score
      status
      processed_at
      criteria_assessments {
        id
        criteria_id
        score
        feedback
        criteria {
          name
          weight
        }
      }
    }
  }
}
```

### Create Submission

```graphql
mutation CreateSubmission($assignment_id: uuid!, $file_ids: [submission_files_insert_input!]!) {
  insert_student_submissions_one(object: {
    assignment_id: $assignment_id,
    student_id: "X-Hasura-User-Id",
    status: "submitted",
    submitted_at: "now()",
    submission_files: {
      data: $file_ids
    }
  }) {
    id
    status
    submitted_at
    submission_files {
      id
      file_id
      file_name
    }
  }
}
```

### Update Submission Status

```graphql
mutation UpdateSubmissionStatus($id: uuid!, $status: String!) {
  update_student_submissions_by_pk(
    pk_columns: { id: $id },
    _set: {
      status: $status
    }
  ) {
    id
    status
    updated_at
  }
}
```

## Assessment Queries and Mutations

### Create AI Assessment

```graphql
mutation CreateAIAssessment(
  $submission_id: uuid!,
  $overall_score: numeric,
  $overall_feedback: String,
  $criteria_assessments: [criteria_assessments_insert_input!]!
) {
  insert_ai_assessments_one(object: {
    submission_id: $submission_id,
    overall_score: $overall_score,
    overall_feedback: $overall_feedback,
    status: "completed",
    processed_at: "now()",
    criteria_assessments: {
      data: $criteria_assessments
    }
  }) {
    id
    overall_score
    status
    processed_at
    criteria_assessments {
      id
      criteria_id
      score
    }
  }
}
```

### Update AI Assessment

```graphql
mutation UpdateAIAssessment(
  $id: uuid!,
  $overall_score: numeric,
  $overall_feedback: String
) {
  update_ai_assessments_by_pk(
    pk_columns: { id: $id },
    _set: {
      overall_score: $overall_score,
      overall_feedback: $overall_feedback
    }
  ) {
    id
    overall_score
    overall_feedback
    updated_at
  }
}
```

### Create Teacher Review

```graphql
mutation CreateTeacherReview(
  $ai_assessment_id: uuid!,
  $overall_score: numeric,
  $overall_feedback: String,
  $status: String!
) {
  insert_teacher_reviews_one(object: {
    ai_assessment_id: $ai_assessment_id,
    teacher_id: "X-Hasura-User-Id",
    overall_score: $overall_score,
    overall_feedback: $overall_feedback,
    status: $status,
    published_at: $status == "published" ? "now()" : null
  }) {
    id
    overall_score
    status
    published_at
  }
}
```

## Using These Queries in Your Application

To use these queries in your application:

1. Create a new file for each query/mutation in your project
2. Use the GraphQL Codegen to generate TypeScript types for these queries
3. Use the generated hooks in your components

Example:

```typescript
// src/lib/graphql/queries/getUserOrganizations.ts
import { gql } from '@apollo/client';

export const GET_USER_ORGANIZATIONS = gql`
  query GetUserOrganizations {
    organizations(where: {
      organization_members: {
        user_id: { _eq: "X-Hasura-User-Id" }
      }
    }) {
      id
      name
      description
      logo_url
      created_at
      organization_members {
        role
      }
    }
  }
`;
```

Then in your component:

```tsx
import { useGetUserOrganizationsQuery } from '@/lib/gql/graphql';

function OrganizationsList() {
  const { data, loading, error } = useGetUserOrganizationsQuery();

  if (loading) return <p>Loading...</p>;
  if (error) return <p>Error: {error.message}</p>;

  return (
    <div>
      {data?.organizations.map(org => (
        <div key={org.id}>
          <h2>{org.name}</h2>
          <p>{org.description}</p>
        </div>
      ))}
    </div>
  );
}
```
