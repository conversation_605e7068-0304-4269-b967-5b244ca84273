# Page Planning for AI Grading and Assessment SaaS

This document outlines the recommended pages and components to build for the AI Grading and Assessment SaaS platform, based on the database schema.

## User Flows

### Teacher Flow
1. Sign up/Sign in
2. Create/join an organization
3. Create courses
4. Create assignments with assessment criteria
5. View student submissions
6. Review AI-generated assessments
7. Provide additional feedback
8. Export results

### Student Flow
1. Sign up/Sign in
2. Join courses (via invitation or code)
3. View assignments
4. Submit work
5. View assessment results

## Page Structure

### Authentication Pages
- `/auth/sign-up` - User registration
- `/auth/sign-in` - User login
- `/auth/reset-password` - Password reset

### Dashboard Pages
- `/dashboard` - Main dashboard with overview of courses, assignments, and recent activity
- `/organizations` - List of organizations the user belongs to
- `/organizations/[id]` - Organization details and management

### Course Pages
- `/courses` - List of all courses
- `/courses/new` - Create a new course
- `/courses/[id]` - Course details and management
- `/courses/[id]/students` - Manage students in a course
- `/courses/[id]/assignments` - List of assignments for a course

### Assignment Pages
- `/assignments/new` - Create a new assignment
- `/assignments/[id]` - Assignment details and management
- `/assignments/[id]/criteria` - Manage assessment criteria
- `/assignments/[id]/submissions` - View all submissions for an assignment

### Submission Pages
- `/submissions/new` - Create a new submission
- `/submissions/[id]` - View a specific submission
- `/submissions/[id]/assessment` - View AI assessment and teacher review

### Assessment Pages
- `/assessments/[id]` - View assessment details
- `/assessments/[id]/review` - Teacher review interface

### Settings Pages
- `/settings/profile` - User profile settings
- `/settings/organization` - Organization settings
- `/settings/integrations` - Third-party integrations

## Component Planning

### Layout Components
- `MainLayout` - Main application layout with navigation
- `AuthLayout` - Layout for authentication pages
- `DashboardLayout` - Layout for dashboard pages

### Authentication Components
- `SignUpForm` - User registration form
- `SignInForm` - User login form
- `ResetPasswordForm` - Password reset form

### Organization Components
- `OrganizationList` - List of organizations
- `OrganizationCard` - Card displaying organization information
- `OrganizationForm` - Form for creating/editing organizations
- `MemberManagement` - Interface for managing organization members

### Course Components
- `CourseList` - List of courses
- `CourseCard` - Card displaying course information
- `CourseForm` - Form for creating/editing courses
- `StudentManagement` - Interface for managing students in a course

### Assignment Components
- `AssignmentList` - List of assignments
- `AssignmentCard` - Card displaying assignment information
- `AssignmentForm` - Form for creating/editing assignments
- `CriteriaBuilder` - Interface for building assessment criteria
- `SubmissionList` - List of submissions for an assignment

### Submission Components
- `SubmissionForm` - Form for submitting work
- `FileUploader` - Component for uploading files
- `SubmissionViewer` - Component for viewing submission details

### Assessment Components
- `AssessmentViewer` - Component for viewing assessment details
- `CriteriaAssessment` - Component for viewing/editing criteria assessments
- `FeedbackEditor` - Rich text editor for providing feedback
- `ScoreInput` - Component for inputting scores

### Export Components
- `ExportOptions` - Interface for configuring export options
- `PDFPreview` - Preview of exported PDF

## Page Implementation Priority

1. **Authentication Pages** - Essential for user access
2. **Dashboard & Organization Pages** - Core navigation and management
3. **Course & Assignment Creation** - Enable teachers to set up their classes
4. **Submission Interface** - Allow students to submit work
5. **Assessment Viewing** - Display AI-generated assessments
6. **Teacher Review Interface** - Enable teachers to review and modify assessments
7. **Export Functionality** - Allow exporting of results
8. **Settings Pages** - Configure user and organization settings

## Technical Considerations

### State Management
- Use React Context for authentication state
- Use Apollo Client for GraphQL data fetching and caching
- Consider using React Query for REST API calls

### Form Handling
- Use Formik or React Hook Form for form state management
- Use Yup or Zod for form validation

### File Uploads
- Use Nhost Storage for file uploads
- Implement progress indicators for large file uploads
- Support batch uploads for multiple files

### Real-time Updates
- Use GraphQL subscriptions for real-time updates on submissions and assessments

### PDF Generation
- Use a library like jsPDF or react-pdf for generating PDF exports
- Implement customizable templates for different export formats

## Next Steps

1. Set up the project structure with the recommended pages and components
2. Implement authentication flow using Nhost
3. Create the dashboard and organization management interfaces
4. Implement course and assignment creation
5. Build the submission interface
6. Develop the assessment viewing and review interfaces
7. Add export functionality
8. Implement settings pages
