import { format, subDays, parseISO, differenceInMinutes } from 'date-fns';
import {
  AnalyticsKPI,
  GradeDistribution,
  AssessmentTrend,
  ProcessingEfficiency,
  RecentActivity,
  SubjectPerformance,
  AnalyticsDashboardData
} from './types';

// Grade color mapping
const GRADE_COLORS = {
  'A': '#10B981', // green-500
  'B': '#3B82F6', // blue-500
  'C': '#F59E0B', // amber-500
  'D': '#EF4444', // red-500
  'F': '#6B7280', // gray-500
  'N/A': '#9CA3AF' // gray-400
};

// Extract subject from assignment title/description
export function extractSubject(title: string, description?: string): string {
  const text = `${title} ${description || ''}`.toLowerCase();
  
  // Subject keywords mapping
  const subjects = {
    'mathematics': ['math', 'algebra', 'geometry', 'calculus', 'statistics'],
    'science': ['science', 'physics', 'chemistry', 'biology', 'lab'],
    'literature': ['literature', 'english', 'reading', 'writing', 'essay'],
    'history': ['history', 'social studies', 'civilization', 'war', 'revolution'],
    'art': ['art', 'drawing', 'painting', 'design', 'creative'],
    'geography': ['geography', 'maps', 'countries', 'continents', 'climate'],
    'computer science': ['programming', 'coding', 'computer', 'software', 'algorithm']
  };

  for (const [subject, keywords] of Object.entries(subjects)) {
    if (keywords.some(keyword => text.includes(keyword))) {
      return subject;
    }
  }
  
  return 'other';
}

// Calculate KPIs from raw data
export function calculateKPIs(data: any): AnalyticsKPI[] {
  const assignments = data?.assignments || [];
  const totalAssignments = assignments.length;
  
  const totalSubmissions = assignments.reduce((sum: number, assignment: any) => 
    sum + (assignment.student_submissions?.length || 0), 0
  );
  
  const totalAssessments = assignments.reduce((sum: number, assignment: any) => 
    sum + assignment.student_submissions?.filter((sub: any) => sub.assessment_results?.length > 0).length || 0, 0
  );

  // Calculate average processing time
  let totalProcessingTime = 0;
  let processedCount = 0;
  
  assignments.forEach((assignment: any) => {
    assignment.student_submissions?.forEach((submission: any) => {
      if (submission.assessment_results?.length > 0) {
        const uploadTime = parseISO(submission.upload_date);
        const assessmentTime = parseISO(submission.assessment_results[0].created_at);
        const processingMinutes = differenceInMinutes(assessmentTime, uploadTime);
        totalProcessingTime += processingMinutes;
        processedCount++;
      }
    });
  });

  const avgProcessingTime = processedCount > 0 ? Math.round(totalProcessingTime / processedCount) : 0;

  return [
    {
      label: 'Total Assignments',
      value: totalAssignments,
      icon: '📝'
    },
    {
      label: 'Total Submissions',
      value: totalSubmissions,
      icon: '📤'
    },
    {
      label: 'Assessments Completed',
      value: totalAssessments,
      icon: '🧠'
    },
    {
      label: 'Avg Processing Time',
      value: avgProcessingTime,
      icon: '⏱️'
    }
  ];
}

// Calculate grade distribution
export function calculateGradeDistribution(assessmentResults: any[]): GradeDistribution[] {
  const gradeCounts: { [key: string]: number } = {};
  const total = assessmentResults.length;

  // Count grades
  assessmentResults.forEach(result => {
    const grade = result.overall_grade || 'N/A';
    gradeCounts[grade] = (gradeCounts[grade] || 0) + 1;
  });

  // Convert to distribution format
  return Object.entries(gradeCounts).map(([grade, count]) => ({
    grade,
    count,
    percentage: Math.round((count / total) * 100),
    color: GRADE_COLORS[grade as keyof typeof GRADE_COLORS] || GRADE_COLORS['N/A']
  }));
}

// Calculate assessment trends over time
export function calculateAssessmentTrends(submissions: any[], days: number = 30): AssessmentTrend[] {
  const trends: { [key: string]: { assessments: number; submissions: number } } = {};
  const startDate = subDays(new Date(), days);

  // Initialize all days
  for (let i = 0; i < days; i++) {
    const date = format(subDays(new Date(), i), 'yyyy-MM-dd');
    trends[date] = { assessments: 0, submissions: 0 };
  }

  // Count submissions and assessments by date
  submissions.forEach(submission => {
    const submissionDate = format(parseISO(submission.upload_date), 'yyyy-MM-dd');
    if (trends[submissionDate]) {
      trends[submissionDate].submissions++;
      
      if (submission.assessment_results?.length > 0) {
        trends[submissionDate].assessments++;
      }
    }
  });

  // Convert to array format for charts
  return Object.entries(trends)
    .map(([date, data]) => ({
      date: format(parseISO(date), 'MMM dd'),
      assessments: data.assessments,
      submissions: data.submissions
    }))
    .reverse(); // Most recent first
}

// Calculate processing efficiency by assignment
export function calculateProcessingEfficiency(assignments: any[]): ProcessingEfficiency[] {
  return assignments.map(assignment => {
    const submissions = assignment.student_submissions || [];
    const processedSubmissions = submissions.filter((sub: any) => sub.assessment_results?.length > 0);
    
    let totalProcessingTime = 0;
    processedSubmissions.forEach((submission: any) => {
      const uploadTime = parseISO(submission.upload_date);
      const assessmentTime = parseISO(submission.assessment_results[0].created_at);
      totalProcessingTime += differenceInMinutes(assessmentTime, uploadTime);
    });

    const averageProcessingTime = processedSubmissions.length > 0 
      ? Math.round(totalProcessingTime / processedSubmissions.length) 
      : 0;

    const completionRate = submissions.length > 0 
      ? Math.round((processedSubmissions.length / submissions.length) * 100)
      : 0;

    return {
      assignmentTitle: assignment.title,
      averageProcessingTime,
      submissionCount: submissions.length,
      completionRate
    };
  }).filter(efficiency => efficiency.submissionCount > 0);
}

// Calculate recent activity feed
export function calculateRecentActivity(assessmentResults: any[], submissions: any[]): RecentActivity[] {
  const activities: RecentActivity[] = [];

  // Add assessment activities
  assessmentResults.forEach(result => {
    activities.push({
      id: result.id,
      type: 'assessment',
      title: `Assessment completed for ${result.student_submission.assignment.title}`,
      studentName: result.student_submission.student_name,
      assignmentTitle: result.student_submission.assignment.title,
      timestamp: parseISO(result.created_at),
      status: 'completed',
      grade: result.overall_grade
    });
  });

  // Add pending submission activities
  submissions.forEach(submission => {
    activities.push({
      id: submission.id,
      type: 'submission',
      title: `New submission for ${submission.assignment.title}`,
      studentName: submission.student_name,
      assignmentTitle: submission.assignment.title,
      timestamp: parseISO(submission.upload_date),
      status: submission.processing_status || 'pending'
    });
  });

  // Sort by timestamp (most recent first) and limit to 10
  return activities
    .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
    .slice(0, 10);
}

// Calculate subject performance
export function calculateSubjectPerformance(assignments: any[]): SubjectPerformance[] {
  const subjectData: { [key: string]: { grades: string[]; submissions: number; assessments: number; students: Set<string> } } = {};

  assignments.forEach(assignment => {
    const subject = extractSubject(assignment.title, assignment.description);
    
    if (!subjectData[subject]) {
      subjectData[subject] = { grades: [], submissions: 0, assessments: 0, students: new Set() };
    }

    assignment.student_submissions?.forEach((submission: any) => {
      subjectData[subject].submissions++;
      subjectData[subject].students.add(submission.student_name);
      
      if (submission.assessment_results?.length > 0) {
        subjectData[subject].assessments++;
        const grade = submission.assessment_results[0].overall_grade;
        if (grade) {
          subjectData[subject].grades.push(grade);
        }
      }
    });
  });

  return Object.entries(subjectData).map(([subject, data]) => {
    // Calculate average grade (simplified - could be more sophisticated)
    const gradeValues = { 'A': 4, 'B': 3, 'C': 2, 'D': 1, 'F': 0 };
    const validGrades = data.grades.filter(grade => grade in gradeValues);
    const averageValue = validGrades.length > 0 
      ? validGrades.reduce((sum, grade) => sum + gradeValues[grade as keyof typeof gradeValues], 0) / validGrades.length
      : 0;
    
    const averageGrade = averageValue >= 3.5 ? 'A' : averageValue >= 2.5 ? 'B' : averageValue >= 1.5 ? 'C' : averageValue >= 0.5 ? 'D' : 'F';

    return {
      subject: subject.charAt(0).toUpperCase() + subject.slice(1),
      averageGrade,
      submissionCount: data.submissions,
      assessmentCount: data.assessments,
      topPerformers: Array.from(data.students).slice(0, 3) // Top 3 students by participation
    };
  }).filter(performance => performance.submissionCount > 0);
}
