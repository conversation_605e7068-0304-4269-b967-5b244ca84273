'use client';

import { useState, useEffect } from 'react';
import { useAuthenticationStatus, useUserData } from '@nhost/nextjs';
import { useRouter } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import AssignmentForm from '@/components/assignments/AssignmentForm';
import AssignmentList from '@/components/assignments/AssignmentList';

export default function AssignmentManagementPage() {
  const { isAuthenticated, isLoading } = useAuthenticationStatus();
  const user = useUserData();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'create' | 'list'>('list');

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/sign-in');
    }
  }, [isLoading, isAuthenticated, router]);

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[50vh]">
          <p>Loading...</p>
        </div>
      </DashboardLayout>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect in the useEffect
  }

  return (
    <DashboardLayout>
      <div className="mb-8">
        <h1 className="text-3xl font-light mb-2">Assignment Management</h1>
        <p className="text-gray-600">
          Create and manage your assignments.
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="mb-8 border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('list')}
            className={`py-3 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'list'
                ? 'border-accent text-accent'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            View Assignments
          </button>
          <button
            onClick={() => setActiveTab('create')}
            className={`py-3 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'create'
                ? 'border-accent text-accent'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Create Assignment
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === 'create' ? (
          <div className="max-w-3xl">
            <AssignmentForm />
          </div>
        ) : (
          <AssignmentList />
        )}
      </div>
    </DashboardLayout>
  );
}
