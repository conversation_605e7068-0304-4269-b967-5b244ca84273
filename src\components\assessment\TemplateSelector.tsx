import React, { useState, useEffect } from 'react';

interface TemplateSelectorProps {
  selectedTemplate: string | null;
  onTemplateSelect: (templateId: string) => void;
}

const subjects = [
  { id: 'recent', name: 'Recent', icon: '🕒' },
  { id: 'mathematics', name: 'Mathematics', icon: '📊' },
  { id: 'english', name: 'English', icon: '📝' },
  { id: 'science', name: 'Science', icon: '🔬' },
  { id: 'history', name: 'History', icon: '📚' },
  { id: 'geography', name: 'Geography', icon: '🌍' },
  { id: 'art', name: 'Art', icon: '🎨' },
  { id: 'literature', name: 'Literature', icon: '📖' },
  { id: 'computer', name: 'Computing', icon: '💻' }
];

const templates = {
  recent: [
    {
      id: 'reading-comprehension',
      name: 'Reading Comprehension',
      description: 'For assessing reading comprehension and analysis skills',
      selected: true
    }
  ],
  english: [
    {
      id: 'essay-analysis',
      name: 'Essay Analysis',
      description: 'Template for evaluating essays with emphasis on structure, argumentation, and language'
    },
    {
      id: 'creative-writing',
      name: 'Creative Writing',
      description: 'For assessing creative writing with focus on narrative, style, and creativity'
    },
    {
      id: 'reading-comprehension',
      name: 'Reading Comprehension',
      description: 'For assessing reading comprehension and analysis skills'
    }
  ],
  mathematics: [
    {
      id: 'problem-solving',
      name: 'Problem Solving',
      description: 'Template for mathematical problem-solving and reasoning assessments'
    },
    {
      id: 'mathematical-modeling',
      name: 'Mathematical Modeling',
      description: 'Assessment of real-world mathematical applications and modeling skills'
    }
  ],
  science: [
    {
      id: 'lab-report',
      name: 'Lab Report',
      description: 'Template for scientific lab report and experimental procedure evaluations'
    },
    {
      id: 'scientific-inquiry',
      name: 'Scientific Inquiry',
      description: 'Assessment of hypothesis formation, data analysis, and scientific reasoning'
    }
  ],
  history: [
    {
      id: 'historical-analysis',
      name: 'Historical Analysis',
      description: 'Template for analyzing historical documents, events, and primary sources'
    },
    {
      id: 'historical-research',
      name: 'Historical Research',
      description: 'Assessment of historical research methods and evidence evaluation'
    }
  ],
  geography: [
    {
      id: 'map-analysis',
      name: 'Map Analysis',
      description: 'Template for geographic map reading and spatial analysis skills'
    },
    {
      id: 'regional-study',
      name: 'Regional Study',
      description: 'Assessment of regional geography knowledge and cultural understanding'
    }
  ],
  art: [
    {
      id: 'visual-analysis',
      name: 'Visual Analysis',
      description: 'Template for analyzing artistic works, techniques, and visual elements'
    },
    {
      id: 'creative-portfolio',
      name: 'Creative Portfolio',
      description: 'Assessment of original artistic work and creative process'
    }
  ],
  literature: [
    {
      id: 'literary-analysis',
      name: 'Literary Analysis',
      description: 'Template for analyzing literary works, themes, and literary devices'
    },
    {
      id: 'comparative-literature',
      name: 'Comparative Literature',
      description: 'Assessment comparing multiple literary works or authors'
    }
  ],
  computer: [
    {
      id: 'programming-project',
      name: 'Programming Project',
      description: 'Template for coding assignments and software development projects'
    },
    {
      id: 'algorithm-design',
      name: 'Algorithm Design',
      description: 'Assessment of computational thinking and algorithm development skills'
    }
  ]
};

export default function TemplateSelector({ selectedTemplate, onTemplateSelect }: TemplateSelectorProps) {
  const [activeSubject, setActiveSubject] = useState('english');

  // Find which subject contains the selected template
  useEffect(() => {
    if (selectedTemplate) {
      console.log('🔍 Finding subject for template:', selectedTemplate);

      for (const [subjectId, subjectTemplates] of Object.entries(templates)) {
        if (subjectTemplates.some(template => template.id === selectedTemplate)) {
          console.log('✅ Found template in subject:', subjectId);
          setActiveSubject(subjectId);
          break;
        }
      }
    }
  }, [selectedTemplate]);

  const currentTemplates = templates[activeSubject as keyof typeof templates] || [];

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-text mb-2">Assessment Template</h2>
        <p className="text-gray-600">Select a template to structure your assessment criteria and feedback</p>
      </div>

      {/* Subject Tabs */}
      <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg">
        {subjects.map((subject) => (
          <button
            key={subject.id}
            onClick={() => setActiveSubject(subject.id)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeSubject === subject.id
                ? 'bg-white text-accent shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <span>{subject.icon}</span>
            <span>{subject.name}</span>
          </button>
        ))}
      </div>

      {/* Template Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {currentTemplates.map((template) => (
          <div
            key={template.id}
            className={`border rounded-lg p-4 cursor-pointer transition-all ${
              selectedTemplate === template.id
                ? 'border-orange-500 bg-orange-50 ring-2 ring-orange-200'
                : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
            }`}
            onClick={() => onTemplateSelect(template.id)}
          >
            <div className="flex justify-between items-start mb-3">
              <h3 className="font-medium text-text">{template.name}</h3>
              {selectedTemplate === template.id && (
                <div className="bg-orange-500 text-white px-2 py-1 rounded text-xs font-medium">
                  Selected
                </div>
              )}
            </div>
            <p className="text-sm text-gray-600 mb-4">{template.description}</p>
            <div className="flex space-x-2">
              <button className="text-sm text-gray-500 hover:text-gray-700">
                Preview
              </button>
              <button
                className="text-sm text-accent hover:text-[#9A7649]"
                onClick={(e) => {
                  e.stopPropagation();
                  onTemplateSelect(template.id);
                }}
              >
                Select
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
