import { NextRequest, NextResponse } from 'next/server';
import { withSecurity, securityConfigs } from '@/lib/middleware/security';
import { withRateLimit } from '@/lib/middleware/rateLimiter';
import { withAuth, authConfigs } from '@/lib/middleware/auth';
import { 
  getInfrastructureStatus, 
  getSecurityAlerts, 
  getComplianceStatus 
} from '@/lib/security/infrastructure-monitor';

// Infrastructure security status handler
const infrastructureSecurityHandler = async (request: NextRequest) => {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'status';

    switch (type) {
      case 'status':
        const status = await getInfrastructureStatus();
        return NextResponse.json({
          success: true,
          data: status
        });

      case 'alerts':
        const alerts = getSecurityAlerts();
        return NextResponse.json({
          success: true,
          data: alerts
        });

      case 'compliance':
        const compliance = getComplianceStatus();
        return NextResponse.json({
          success: true,
          data: compliance
        });

      default:
        return NextResponse.json(
          { error: 'Invalid type parameter. Use: status, alerts, or compliance' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Failed to fetch infrastructure security data:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch infrastructure security data',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    );
  }
};

// Apply security middleware, authentication, and rate limiting
export const GET = withSecurity(
  withAuth(
    withRateLimit(infrastructureSecurityHandler, 'general'),
    authConfigs.authenticated // Require authentication
  ),
  {
    ...securityConfigs.general,
    allowedMethods: ['GET', 'OPTIONS'],
    requireAuth: true
  }
);

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
