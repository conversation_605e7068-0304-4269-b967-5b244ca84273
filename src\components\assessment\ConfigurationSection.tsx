import { useEffect } from 'react';

interface Configuration {
  aiModel: 'standard' | 'comprehensive';
  priority: 'standard' | 'high' | 'urgent';
  feedbackLevel: 'standard' | 'detailed' | 'brief';
  assessmentType: 'letter' | 'numeric' | 'passfail' | 'holistic' | 'rubric';
  feedbackTemplate: string;
  features: {
    plagiarismCheck: boolean;
    grammarCheck: boolean;
    contentAnalysis: boolean;
    citationCheck: boolean;
    codeAnalysis: boolean;
  };
}

interface ConfigurationSectionProps {
  configuration: Configuration;
  onUpdate: (configuration: Configuration) => void;
  onEstimatedTimeChange: (time: number) => void;
}

export default function ConfigurationSection({ 
  configuration, 
  onUpdate, 
  onEstimatedTimeChange 
}: ConfigurationSectionProps) {

  // Calculate estimated time based on configuration
  useEffect(() => {
    let baseTime = 3; // Base 3 minutes
    
    if (configuration.aiModel === 'comprehensive') baseTime += 1.5;
    if (configuration.priority === 'high') baseTime -= 0.5;
    if (configuration.priority === 'urgent') baseTime -= 1;
    if (configuration.feedbackLevel === 'detailed') baseTime += 1;
    if (configuration.features.plagiarismCheck) baseTime += 0.5;
    if (configuration.features.grammarCheck) baseTime += 0.3;
    if (configuration.features.contentAnalysis) baseTime += 0.7;
    
    onEstimatedTimeChange(Math.max(baseTime, 1.5)); // Minimum 1.5 minutes
  }, [configuration, onEstimatedTimeChange]);

  const handleConfigChange = (key: keyof Configuration, value: any) => {
    onUpdate({ ...configuration, [key]: value });
  };

  const handleFeatureToggle = (feature: keyof Configuration['features']) => {
    onUpdate({
      ...configuration,
      features: {
        ...configuration.features,
        [feature]: !configuration.features[feature]
      }
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 className="text-xl font-semibold text-text mb-6">Configuration</h2>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {/* AI Model */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            AI Model
          </label>
          <select
            value={configuration.aiModel}
            onChange={(e) => handleConfigChange('aiModel', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent"
          >
            <option value="standard">Standard</option>
            <option value="comprehensive">Comprehensive</option>
          </select>
        </div>

        {/* Priority */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Priority
          </label>
          <select
            value={configuration.priority}
            onChange={(e) => handleConfigChange('priority', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent"
          >
            <option value="standard">Standard</option>
            <option value="high">High Priority</option>
            <option value="urgent">Urgent</option>
          </select>
        </div>

        {/* Feedback Level */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Feedback Level
          </label>
          <select
            value={configuration.feedbackLevel}
            onChange={(e) => handleConfigChange('feedbackLevel', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent"
          >
            <option value="brief">Brief</option>
            <option value="standard">Standard</option>
            <option value="detailed">Detailed</option>
          </select>
        </div>
      </div>

      {/* Assessment Type */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Assessment Type
        </label>
        <select
          value={configuration.assessmentType}
          onChange={(e) => handleConfigChange('assessmentType', e.target.value)}
          className="w-full max-w-xs px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent"
        >
          <option value="letter">Letter Grade (A-F)</option>
          <option value="numeric">Numeric Grade (0-100)</option>
          <option value="passfail">Pass/Fail</option>
          <option value="holistic">Holistic Assessment</option>
          <option value="rubric">Rubric-Based</option>
        </select>
      </div>

      {/* Feedback Template */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Feedback Template
        </label>
        <div className="flex items-center justify-between">
          <span className="text-gray-900">{configuration.feedbackTemplate}</span>
          <button
            onClick={() => {
              // TODO: Implement template change
              console.log('Change feedback template');
            }}
            className="text-sm text-accent hover:text-[#9A7649] font-medium transition-colors"
          >
            Change Template
          </button>
        </div>
      </div>

      {/* Enabled Features */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Enabled Features</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {Object.entries(configuration.features).map(([key, enabled]) => (
            <label key={key} className="flex items-center space-x-3 cursor-pointer">
              <div className="relative">
                <input
                  type="checkbox"
                  checked={enabled}
                  onChange={() => handleFeatureToggle(key as keyof Configuration['features'])}
                  className="sr-only"
                />
                <div className={`w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${
                  enabled 
                    ? 'bg-green-500 border-green-500' 
                    : 'border-gray-300 bg-white'
                }`}>
                  {enabled && (
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
              </div>
              <span className="text-sm text-gray-700 capitalize">
                {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
              </span>
            </label>
          ))}
        </div>
      </div>
    </div>
  );
}
