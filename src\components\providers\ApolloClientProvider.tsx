'use client';

import { ReactNode, useState, useEffect } from 'react';
import { ApolloProvider } from '@apollo/client';
import { getClientApolloClient } from '@/lib/apollo-client';

export function ApolloClientProvider({ children }: { children: ReactNode }) {
  const [apolloClient, setApolloClient] = useState<any>(null);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    // Ensure we're mounted before initializing
    setMounted(true);

    // Small delay to ensure all providers are ready
    const timer = setTimeout(() => {
      try {
        const client = getClientApolloClient();
        setApolloClient(client);
      } catch (error) {
        console.error('Failed to initialize Apollo Client:', error);
      }
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  // Don't render until mounted and Apollo Client is initialized
  if (!mounted || !apolloClient) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-gray-600">Loading...</div>
      </div>
    );
  }

  return <ApolloProvider client={apolloClient}>{children}</ApolloProvider>;
}
