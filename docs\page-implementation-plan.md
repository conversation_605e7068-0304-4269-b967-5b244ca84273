# Page Implementation Plan for AI Grading and Assessment SaaS

This document outlines a practical implementation plan for building the pages of your AI Grading and Assessment SaaS platform, based on your existing database schema.

## Phase 1: Authentication and User Management

### Authentication Pages

1. **Sign In Page** (`/auth/sign-in`)
   - Components:
     - SignInForm
     - AuthLayout
   - Functionality:
     - Email/password authentication
     - Social login (if configured in Nhost)
     - Password reset link

2. **Sign Up Page** (`/auth/sign-up`)
   - Components:
     - SignUpForm
     - AuthLayout
   - Functionality:
     - Email/password registration
     - Social registration
     - Terms of service acceptance

3. **Password Reset Page** (`/auth/reset-password`)
   - Components:
     - ResetPasswordForm
     - AuthLayout
   - Functionality:
     - Request password reset
     - Set new password

### User Profile Pages

1. **Profile Page** (`/profile`)
   - Components:
     - ProfileForm
     - AvatarUploader
     - MainLayout
   - Functionality:
     - View/edit user information
     - Change password
     - Upload profile picture

## Phase 2: Organization and Course Management

### Organization Pages

1. **Organizations List** (`/organizations`)
   - Components:
     - OrganizationList
     - OrganizationCard
     - CreateOrganizationButton
     - MainLayout
   - Functionality:
     - View all organizations user belongs to
     - Create new organization
     - Join existing organization

2. **Organization Details** (`/organizations/[id]`)
   - Components:
     - OrganizationHeader
     - MembersList
     - CoursesList
     - MainLayout
   - Functionality:
     - View organization details
     - Manage organization members
     - View courses in organization

3. **Organization Settings** (`/organizations/[id]/settings`)
   - Components:
     - OrganizationForm
     - DeleteOrganizationButton
     - MainLayout
   - Functionality:
     - Edit organization details
     - Delete organization

### Course Pages

1. **Courses List** (`/courses`)
   - Components:
     - CourseList
     - CourseCard
     - CreateCourseButton
     - MainLayout
   - Functionality:
     - View all courses user has access to
     - Filter courses by organization
     - Create new course

2. **Course Details** (`/courses/[id]`)
   - Components:
     - CourseHeader
     - AssignmentsList
     - StudentsList
     - MainLayout
   - Functionality:
     - View course details
     - Manage assignments
     - Manage students

3. **Course Settings** (`/courses/[id]/settings`)
   - Components:
     - CourseForm
     - DeleteCourseButton
     - MainLayout
   - Functionality:
     - Edit course details
     - Delete course

## Phase 3: Assignment Management

### Assignment Pages

1. **Create Assignment** (`/courses/[id]/assignments/new`)
   - Components:
     - AssignmentForm
     - CriteriaBuilder
     - MainLayout
   - Functionality:
     - Create new assignment
     - Define assessment criteria
     - Set due date and other parameters

2. **Assignment Details** (`/assignments/[id]`)
   - Components:
     - AssignmentHeader
     - CriteriaList
     - SubmissionsList
     - MainLayout
   - Functionality:
     - View assignment details
     - View assessment criteria
     - View submissions

3. **Edit Assignment** (`/assignments/[id]/edit`)
   - Components:
     - AssignmentForm
     - CriteriaBuilder
     - MainLayout
   - Functionality:
     - Edit assignment details
     - Modify assessment criteria

## Phase 4: Submission and Assessment

### Submission Pages

1. **Create Submission** (`/assignments/[id]/submit`)
   - Components:
     - SubmissionForm
     - FileUploader
     - MainLayout
   - Functionality:
     - Upload submission files
     - Add submission notes
     - Submit for assessment

2. **Submission Details** (`/submissions/[id]`)
   - Components:
     - SubmissionHeader
     - FileViewer
     - AssessmentViewer
     - MainLayout
   - Functionality:
     - View submission details
     - View uploaded files
     - View assessment results

### Assessment Pages

1. **Assessment Details** (`/assessments/[id]`)
   - Components:
     - AssessmentHeader
     - CriteriaAssessments
     - OverallFeedback
     - MainLayout
   - Functionality:
     - View AI-generated assessment
     - View scores for each criterion
     - View overall feedback

2. **Teacher Review** (`/assessments/[id]/review`)
   - Components:
     - AssessmentEditor
     - CriteriaScoreEditor
     - FeedbackEditor
     - MainLayout
   - Functionality:
     - Review AI-generated assessment
     - Modify scores and feedback
     - Publish final assessment

## Phase 5: Reporting and Exports

### Reporting Pages

1. **Assignment Report** (`/assignments/[id]/report`)
   - Components:
     - AssignmentStats
     - SubmissionTable
     - ExportOptions
     - MainLayout
   - Functionality:
     - View statistics for assignment
     - View all submissions and scores
     - Export results

2. **Course Report** (`/courses/[id]/report`)
   - Components:
     - CourseStats
     - AssignmentTable
     - StudentPerformanceTable
     - MainLayout
   - Functionality:
     - View statistics for course
     - View performance by assignment
     - View performance by student

### Export Pages

1. **Export Configuration** (`/exports/new`)
   - Components:
     - ExportForm
     - TemplateSelector
     - MainLayout
   - Functionality:
     - Select what to export
     - Configure export format
     - Generate export

## Implementation Strategy

### 1. Set Up Project Structure

```
src/
├── app/
│   ├── auth/
│   │   ├── sign-in/
│   │   ├── sign-up/
│   │   └── reset-password/
│   ├── dashboard/
│   ├── organizations/
│   ├── courses/
│   ├── assignments/
│   ├── submissions/
│   ├── assessments/
│   └── exports/
├── components/
│   ├── auth/
│   ├── layout/
│   ├── organizations/
│   ├── courses/
│   ├── assignments/
│   ├── submissions/
│   ├── assessments/
│   └── exports/
├── lib/
│   ├── gql/
│   ├── hooks/
│   └── utils/
```

### 2. Implement Core Components

1. Start with layouts and navigation
2. Implement authentication components
3. Build reusable UI components (forms, cards, tables)

### 3. Implement Pages in Phases

Follow the phases outlined above, starting with authentication and user management, then moving to organization and course management, and so on.

### 4. Testing Strategy

1. Unit test core components and utilities
2. Integration test key user flows
3. End-to-end test critical paths (authentication, submission, assessment)

## Next Steps

1. Set up permissions in Hasura for your tables
2. Run GraphQL Codegen to generate TypeScript types
3. Implement the authentication pages
4. Build the dashboard and organization management pages
5. Continue with the remaining phases as outlined above
