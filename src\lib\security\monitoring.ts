// Security monitoring and logging utilities

interface SecurityEvent {
  type: 'rate_limit' | 'auth_failure' | 'suspicious_upload' | 'api_error' | 'cors_violation';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  metadata?: Record<string, any>;
  timestamp: Date;
  userAgent?: string;
  ip?: string;
  userId?: string;
}

class SecurityMonitor {
  private events: SecurityEvent[] = [];
  private readonly maxEvents = 1000; // Keep last 1000 events in memory

  // Log a security event
  logEvent(event: Omit<SecurityEvent, 'timestamp'>) {
    const fullEvent: SecurityEvent = {
      ...event,
      timestamp: new Date()
    };

    this.events.push(fullEvent);

    // Keep only the most recent events
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents);
    }

    // Log to console with appropriate level
    const logLevel = this.getLogLevel(event.severity);
    console[logLevel](`🔒 Security Event [${event.type}]:`, {
      severity: event.severity,
      message: event.message,
      metadata: event.metadata,
      timestamp: fullEvent.timestamp.toISOString()
    });

    // In production, send critical events to monitoring service
    if (process.env.NODE_ENV === 'production' && event.severity === 'critical') {
      this.sendToMonitoringService(fullEvent);
    }
  }

  // Get recent events for analysis
  getRecentEvents(minutes: number = 60): SecurityEvent[] {
    const cutoff = new Date(Date.now() - minutes * 60 * 1000);
    return this.events.filter(event => event.timestamp > cutoff);
  }

  // Get events by type
  getEventsByType(type: SecurityEvent['type'], minutes: number = 60): SecurityEvent[] {
    return this.getRecentEvents(minutes).filter(event => event.type === type);
  }

  // Check for suspicious patterns
  detectSuspiciousActivity(): {
    rateLimitAbuse: boolean;
    authFailureSpike: boolean;
    suspiciousUploads: boolean;
  } {
    const recentEvents = this.getRecentEvents(10); // Last 10 minutes

    return {
      rateLimitAbuse: this.getEventsByType('rate_limit', 10).length > 20,
      authFailureSpike: this.getEventsByType('auth_failure', 10).length > 10,
      suspiciousUploads: this.getEventsByType('suspicious_upload', 10).length > 5
    };
  }

  private getLogLevel(severity: SecurityEvent['severity']): 'log' | 'warn' | 'error' {
    switch (severity) {
      case 'low': return 'log';
      case 'medium': return 'warn';
      case 'high':
      case 'critical': return 'error';
      default: return 'log';
    }
  }

  private async sendToMonitoringService(event: SecurityEvent) {
    // In a real implementation, send to your monitoring service
    // Examples: Sentry, DataDog, New Relic, etc.
    try {
      // Example implementation:
      // await fetch('/api/security-alerts', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(event)
      // });
      
      console.error('🚨 CRITICAL SECURITY EVENT:', event);
    } catch (error) {
      console.error('Failed to send security event to monitoring service:', error);
    }
  }
}

// Singleton instance
export const securityMonitor = new SecurityMonitor();

// Helper functions for common security events

export function logRateLimitViolation(ip: string, endpoint: string, userAgent?: string) {
  securityMonitor.logEvent({
    type: 'rate_limit',
    severity: 'medium',
    message: `Rate limit exceeded for ${endpoint}`,
    metadata: { ip, endpoint, userAgent },
    ip,
    userAgent
  });
}

export function logAuthFailure(ip: string, email?: string, reason?: string, userAgent?: string) {
  securityMonitor.logEvent({
    type: 'auth_failure',
    severity: 'medium',
    message: `Authentication failed: ${reason || 'Invalid credentials'}`,
    metadata: { ip, email, reason, userAgent },
    ip,
    userAgent
  });
}

export function logSuspiciousUpload(
  userId: string, 
  fileName: string, 
  fileSize: number, 
  reason: string,
  ip?: string
) {
  securityMonitor.logEvent({
    type: 'suspicious_upload',
    severity: 'high',
    message: `Suspicious file upload: ${reason}`,
    metadata: { userId, fileName, fileSize, reason, ip },
    userId,
    ip
  });
}

export function logAPIError(
  endpoint: string, 
  error: string, 
  userId?: string, 
  ip?: string
) {
  securityMonitor.logEvent({
    type: 'api_error',
    severity: 'low',
    message: `API error on ${endpoint}: ${error}`,
    metadata: { endpoint, error, userId, ip },
    userId,
    ip
  });
}

export function logCORSViolation(origin: string, endpoint: string, ip?: string) {
  securityMonitor.logEvent({
    type: 'cors_violation',
    severity: 'high',
    message: `CORS violation from ${origin} on ${endpoint}`,
    metadata: { origin, endpoint, ip },
    ip
  });
}

// Security metrics for monitoring dashboard
export function getSecurityMetrics() {
  const recentEvents = securityMonitor.getRecentEvents(60); // Last hour
  const suspiciousActivity = securityMonitor.detectSuspiciousActivity();

  return {
    totalEvents: recentEvents.length,
    eventsByType: {
      rateLimitViolations: securityMonitor.getEventsByType('rate_limit', 60).length,
      authFailures: securityMonitor.getEventsByType('auth_failure', 60).length,
      suspiciousUploads: securityMonitor.getEventsByType('suspicious_upload', 60).length,
      apiErrors: securityMonitor.getEventsByType('api_error', 60).length,
      corsViolations: securityMonitor.getEventsByType('cors_violation', 60).length
    },
    suspiciousActivity,
    lastUpdated: new Date().toISOString()
  };
}

// Middleware to extract client information
export function getClientInfo(request: Request) {
  const forwarded = request.headers.get('x-forwarded-for');
  const ip = forwarded ? forwarded.split(',')[0] : 
             request.headers.get('x-real-ip') || 
             'unknown';
  
  const userAgent = request.headers.get('user-agent') || 'unknown';
  
  return { ip, userAgent };
}

// File security validation
export function validateFileSecurityRisk(file: File): {
  isRisky: boolean;
  reasons: string[];
} {
  const reasons: string[] = [];
  
  // Check file size (files over 100MB are suspicious)
  if (file.size > 100 * 1024 * 1024) {
    reasons.push('File size exceeds 100MB');
  }
  
  // Check for executable file extensions
  const dangerousExtensions = [
    '.exe', '.bat', '.cmd', '.scr', '.pif', '.com', '.vbs', 
    '.jar', '.app', '.deb', '.rpm', '.dmg', '.pkg'
  ];
  
  const fileName = file.name.toLowerCase();
  if (dangerousExtensions.some(ext => fileName.endsWith(ext))) {
    reasons.push('Potentially executable file type');
  }
  
  // Check for suspicious file names
  const suspiciousPatterns = [
    /virus/i, /malware/i, /trojan/i, /backdoor/i,
    /keylog/i, /rootkit/i, /exploit/i
  ];
  
  if (suspiciousPatterns.some(pattern => pattern.test(fileName))) {
    reasons.push('Suspicious file name pattern');
  }
  
  // Check MIME type vs extension mismatch
  const expectedMimeTypes: Record<string, string[]> = {
    '.pdf': ['application/pdf'],
    '.jpg': ['image/jpeg'],
    '.png': ['image/png'],
    '.txt': ['text/plain'],
    '.docx': ['application/vnd.openxmlformats-officedocument.wordprocessingml.document']
  };
  
  for (const [ext, mimes] of Object.entries(expectedMimeTypes)) {
    if (fileName.endsWith(ext) && !mimes.includes(file.type)) {
      reasons.push('MIME type does not match file extension');
      break;
    }
  }
  
  return {
    isRisky: reasons.length > 0,
    reasons
  };
}
