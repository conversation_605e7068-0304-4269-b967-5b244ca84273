# Assessment Creation Filter System

## Objective
Implement a filtering system that prevents past student work uploads from appearing when instructors create new assessments in a Next.js frontend with Nhost backend.

## Requirements

### Backend (Nhost)
- Ensure each assessment has a unique identifier (`assessment_id`)
- Student work uploads should be linked to specific assessments through foreign key relationships
- Implement proper database schemas that separate assessments by:
  - Assessment ID
  - Creation timestamp
  - Status (draft, active, completed, archived)

### Frontend (Next.js)
- When rendering the "Create New Assessment" interface:
  - Only show empty/template fields
  - Filter out any queries that might pull in existing student submissions
  - Ensure state management doesn't carry over data from previous assessment views

### Specific Implementation Guidelines

1. **Query Filtering**
   ```sql
   -- Example: Only fetch data for current assessment being created
   SELECT * FROM student_submissions 
   WHERE assessment_id = $current_assessment_id
   AND assessment_status = 'active'
   ```

2. **State Management**
   - Clear all previous assessment data when initializing new assessment creation
   - Use separate state containers for "create" vs "view/edit" modes
   - Implement proper cleanup on component unmount

3. **Component Isolation**
   - Separate components for assessment creation vs assessment management
   - Ensure props don't accidentally pass previous assessment data to creation forms
   - Use conditional rendering based on assessment creation context

4. **Data Validation**
   - Validate that new assessment forms start with empty/default values
   - Implement checks to prevent accidental data leakage from previous assessments
   - Add guards against displaying cached student work from other assessments

## Key Considerations
- Maintain strict data separation between assessments
- Ensure proper cleanup of form state and cached data
- Implement role-based access controls
- Consider pagination and lazy loading for large datasets
- Add proper error handling for edge cases

## Success Criteria
- New assessment creation shows only empty forms
- No previous student submissions visible during creation
- Clean separation between assessment creation and management workflows
- Proper data isolation maintained throughout the user journey