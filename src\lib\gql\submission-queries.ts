/**
 * Centralized GraphQL queries for student submissions and assignments
 *
 * All queries include proper filtering to prevent old test data from appearing
 * in production workflows.
 */

import { gql } from '@apollo/client';
import { SUBMISSION_FIELDS } from '@/lib/submission-filters';

/**
 * Get submissions for a specific assignment with automatic filtering
 * This is the primary query for assignment-specific workflows
 */
export const GET_ASSIGNMENT_SUBMISSIONS = gql`
  query GetAssignmentSubmissions($assignment_id: uuid!) {
    student_submissions(
      where: { 
        assignment_id: { _eq: $assignment_id }
        # Additional filtering will be applied client-side via submission-filters.ts
      }
    ) {
      ${SUBMISSION_FIELDS}
      assignment {
        id
        title
        description
        assessment_criteria {
          id
          criterion_name
          description
          weight
        }
      }
    }
  }
`;

/**
 * Get all submissions (with client-side filtering applied)
 * Used when no specific assignment is selected
 */
export const GET_ALL_SUBMISSIONS = gql`
  query GetAllSubmissions {
    student_submissions {
      ${SUBMISSION_FIELDS}
      assignment {
        id
        title
        description
      }
    }
  }
`;

/**
 * Get recent submissions
 * For dashboard and activity views
 */
export const GET_RECENT_SUBMISSIONS = gql`
  query GetRecentSubmissions {
    student_submissions(
      limit: 20
    ) {
      ${SUBMISSION_FIELDS}
      assignment {
        id
        title
      }
    }
  }
`;

/**
 * Get submissions with assessment results
 * For results and analytics pages
 */
export const GET_SUBMISSIONS_WITH_RESULTS = gql`
  query GetSubmissionsWithResults($assignment_id: uuid) {
    student_submissions(
      where: { 
        assignment_id: { _eq: $assignment_id }
        # Client-side filtering will be applied
      }
    ) {
      ${SUBMISSION_FIELDS}
      assignment {
        id
        title
        description
      }
      assessment_results {
        id
        overall_grade
        general_comments
        reviewed
        created_at
        per_criterion_feedbacks {
          id
          criterion_id
          score
          feedback_text
          assessment_criterium {
            criterion_name
          }
        }
      }
    }
  }
`;

/**
 * Get single submission details
 * For individual assessment workflows
 */
export const GET_SUBMISSION_DETAILS = gql`
  query GetSubmissionDetails($submission_id: uuid!) {
    student_submissions_by_pk(id: $submission_id) {
      ${SUBMISSION_FIELDS}
      assignment {
        id
        title
        description
        assessment_criteria {
          id
          criterion_name
          description
          weight
        }
      }
      assessment_results {
        id
        overall_grade
        general_comments
        exported_pdf_path
        reviewed
        created_at
        per_criterion_feedbacks {
          id
          criterion_id
          score
          feedback_text
          assessment_criterium {
            criterion_name
          }
        }
      }
    }
  }
`;

/**
 * Mutation to create a new submission
 */
export const CREATE_SUBMISSION = gql`
  mutation CreateSubmission(
    $assignment_id: uuid!
    $student_name: String!
    $status: String!
    $file_path: String
    $file_type: String
    $upload_date: timestamptz
  ) {
    insert_student_submissions_one(object: {
      assignment_id: $assignment_id
      student_name: $student_name
      status: $status
      file_path: $file_path
      file_type: $file_type
      upload_date: $upload_date
    }) {
      ${SUBMISSION_FIELDS}
    }
  }
`;

/**
 * Mutation to delete a submission
 */
export const DELETE_SUBMISSION = gql`
  mutation DeleteSubmission($id: uuid!) {
    delete_student_submissions_by_pk(id: $id) {
      id
    }
  }
`;

/**
 * Mutation to clear all submissions for an assignment (Clean Slate)
 */
export const CLEAR_ASSIGNMENT_SUBMISSIONS = gql`
  mutation ClearAssignmentSubmissions($assignment_id: uuid!) {
    delete_student_submissions(where: { assignment_id: { _eq: $assignment_id } }) {
      affected_rows
      returning {
        id
        student_name
      }
    }
  }
`;

/**
 * Mutation to archive submissions instead of deleting (safer option)
 */
export const ARCHIVE_ASSIGNMENT_SUBMISSIONS = gql`
  mutation ArchiveAssignmentSubmissions($assignment_id: uuid!) {
    update_student_submissions(
      where: { assignment_id: { _eq: $assignment_id } }
      _set: { processing_status: "archived" }
    ) {
      affected_rows
      returning {
        id
        student_name
        processing_status
      }
    }
  }
`;

/**
 * Mutation to update submission status
 */
export const UPDATE_SUBMISSION_STATUS = gql`
  mutation UpdateSubmissionStatus($id: uuid!, $status: String!) {
    update_student_submissions_by_pk(
      pk_columns: { id: $id }
      _set: { processing_status: $status }
    ) {
      id
      processing_status
    }
  }
`;

/**
 * Query for admin/debugging purposes (shows all data without filtering)
 * Should only be used in development or admin interfaces
 */
export const GET_ALL_SUBMISSIONS_ADMIN = gql`
  query GetAllSubmissionsAdmin {
    student_submissions {
      ${SUBMISSION_FIELDS}
      assignment {
        id
        title
        description
      }
      assessment_results {
        id
        overall_grade
        created_at
      }
    }
  }
`;

/**
 * ASSIGNMENT QUERIES AND MUTATIONS
 * Following the same pattern as submission queries to ensure allowlist compatibility
 */

/**
 * Get user's assignments (matches working query from student-upload)
 */
export const GET_USER_ASSIGNMENTS = gql`
  query GetAssignments($user_id: uuid!) {
    assignments(
      where: { user_id: { _eq: $user_id } }
      order_by: { created_at: desc }
    ) {
      id
      title
      description
    }
  }
`;

/**
 * Create a new assignment (simplified to match working patterns)
 */
export const CREATE_ASSIGNMENT = gql`
  mutation CreateAssignment(
    $title: String!
    $description: String!
    $user_id: uuid!
  ) {
    insert_assignments_one(object: {
      title: $title
      description: $description
      user_id: $user_id
    }) {
      id
      title
      description
    }
  }
`;

/**
 * Get assignment by ID
 */
export const GET_ASSIGNMENT_BY_ID = gql`
  query GetAssignmentById($id: uuid!) {
    assignments_by_pk(id: $id) {
      id
      title
      description
      status
      created_at
      user_id
    }
  }
`;

/**
 * Utility function to get the appropriate query based on context
 */
export function getSubmissionQuery(context: 'assignment' | 'all' | 'recent' | 'with-results' | 'admin') {
  switch (context) {
    case 'assignment':
      return GET_ASSIGNMENT_SUBMISSIONS;
    case 'all':
      return GET_ALL_SUBMISSIONS;
    case 'recent':
      return GET_RECENT_SUBMISSIONS;
    case 'with-results':
      return GET_SUBMISSIONS_WITH_RESULTS;
    case 'admin':
      return GET_ALL_SUBMISSIONS_ADMIN;
    default:
      return GET_ALL_SUBMISSIONS;
  }
}
