import { NextRequest, NextResponse } from 'next/server';
import { getClientInfo, logAPIError, logCORSViolation } from '@/lib/security/monitoring';
import { getHTTPSSecurityHeaders, withHTTPSEnforcement } from '@/lib/security/https-security';
import { validateEnvironmentMiddleware } from '@/lib/security/secret-management';
import { logSecurityEvent } from '@/lib/security/audit-logging';

// Enhanced security headers with HTTPS support
function getEnhancedSecurityHeaders(): Record<string, string> {
  const baseHeaders = {
    // Prevent clickjacking
    'X-Frame-Options': 'DENY',

    // Prevent MIME type sniffing
    'X-Content-Type-Options': 'nosniff',

    // Enable XSS protection
    'X-XSS-Protection': '1; mode=block',

    // Referrer policy
    'Referrer-Policy': 'strict-origin-when-cross-origin',

    // Content Security Policy
    'Content-Security-Policy': [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval'", // Needed for Next.js
      "style-src 'self' 'unsafe-inline'", // Needed for Tailwind
      "img-src 'self' data: https:",
      "font-src 'self' data:",
      "connect-src 'self' https://*.nhost.run https://api.openai.com https://api.anthropic.com",
      "frame-ancestors 'none'",
      "base-uri 'self'",
      "form-action 'self'"
    ].join('; '),

    // Permissions policy
    'Permissions-Policy': [
      'camera=()',
      'microphone=()',
      'geolocation=()',
      'payment=()',
      'usb=()'
    ].join(', ')
  };

  // Add HTTPS security headers
  const httpsHeaders = getHTTPSSecurityHeaders();

  return { ...baseHeaders, ...httpsHeaders };
}

// Allowed origins for CORS
const ALLOWED_ORIGINS = [
  'http://localhost:3000',
  'https://localhost:3000',
  process.env.NEXT_PUBLIC_SITE_URL,
  process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : null,
  // Add your Netlify domains
  'https://gradeflow.netlify.app',
  'https://main--gradeflow.netlify.app',
  'https://ess-gradeflow.netlify.app',
  'https://deploy-preview-*--gradeflow.netlify.app', // For preview deployments
  // Add any other Netlify subdomains you might be using
  process.env.NETLIFY_URL ? `https://${process.env.NETLIFY_URL}` : null,
  process.env.DEPLOY_PRIME_URL ? process.env.DEPLOY_PRIME_URL : null
].filter(Boolean) as string[];

// Function to check if origin is allowed (supports wildcards)
function isOriginAllowed(origin: string): boolean {
  // Check exact matches first
  if (ALLOWED_ORIGINS.includes(origin)) {
    return true;
  }

  // Check wildcard patterns for Netlify
  const netlifyPatterns = [
    /^https:\/\/.*\.netlify\.app$/,
    /^https:\/\/deploy-preview-\d+--.*\.netlify\.app$/,
    /^https:\/\/.*--.*\.netlify\.app$/
  ];

  for (const pattern of netlifyPatterns) {
    if (pattern.test(origin)) {
      return true;
    }
  }

  // Check for Vercel patterns
  const vercelPatterns = [
    /^https:\/\/.*\.vercel\.app$/,
    /^https:\/\/.*-.*\.vercel\.app$/
  ];

  for (const pattern of vercelPatterns) {
    if (pattern.test(origin)) {
      return true;
    }
  }

  return false;
}

// Request size limits by endpoint type
const SIZE_LIMITS = {
  upload: 50 * 1024 * 1024, // 50MB for file uploads
  assessment: 1024 * 1024,   // 1MB for assessment data
  general: 100 * 1024,       // 100KB for general API calls
  auth: 10 * 1024            // 10KB for auth requests
};

export interface SecurityConfig {
  enableCORS?: boolean;
  enableSizeLimit?: boolean;
  sizeLimit?: number;
  allowedMethods?: string[];
  requireAuth?: boolean;
}

// Enhanced security middleware
export function withSecurity(
  handler: (request: NextRequest) => Promise<NextResponse>,
  config: SecurityConfig = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const { ip, userAgent } = getClientInfo(request);
    
    try {
      // 1. Check request size
      if (config.enableSizeLimit !== false) {
        const contentLength = request.headers.get('content-length');
        if (contentLength) {
          const size = parseInt(contentLength, 10);
          const limit = config.sizeLimit || SIZE_LIMITS.general;
          
          if (size > limit) {
            logAPIError(
              request.url,
              `Request size ${size} exceeds limit ${limit}`,
              undefined,
              ip
            );
            
            return NextResponse.json(
              { error: 'Request too large' },
              { status: 413 }
            );
          }
        }
      }

      // 2. CORS validation - Temporarily disabled for debugging
      // if (config.enableCORS !== false) {
      //   const origin = request.headers.get('origin');

      //   if (origin && !isOriginAllowed(origin)) {
      //     logCORSViolation(origin, request.url, ip);

      //     return NextResponse.json(
      //       { error: 'CORS policy violation' },
      //       { status: 403 }
      //     );
      //   }
      // }

      // 3. Method validation
      if (config.allowedMethods) {
        if (!config.allowedMethods.includes(request.method)) {
          return NextResponse.json(
            { error: 'Method not allowed' },
            { status: 405 }
          );
        }
      }

      // 4. Authentication check (if required)
      if (config.requireAuth) {
        const authHeader = request.headers.get('authorization');
        const userAuth = request.headers.get('x-user-authenticated');

        // Check if user is authenticated (from middleware or direct auth header)
        if (!authHeader && userAuth !== 'true') {
          return NextResponse.json(
            { error: 'Authentication required' },
            { status: 401 }
          );
        }
      }

      // 5. Execute the handler
      const response = await handler(request);

      // 6. Add enhanced security headers to response
      const securityHeaders = getEnhancedSecurityHeaders();
      Object.entries(securityHeaders).forEach(([key, value]) => {
        response.headers.set(key, value);
      });

      // 7. Add CORS headers - Allow all origins for now
      const origin = request.headers.get('origin');
      response.headers.set('Access-Control-Allow-Origin', origin || '*');
      response.headers.set('Access-Control-Allow-Credentials', 'true');
      response.headers.set(
        'Access-Control-Allow-Methods',
        'GET, POST, PUT, DELETE, OPTIONS'
      );
      response.headers.set(
        'Access-Control-Allow-Headers',
        'Content-Type, Authorization, X-Requested-With'
      );

      return response;

    } catch (error) {
      logAPIError(
        request.url,
        error instanceof Error ? error.message : 'Unknown security error',
        undefined,
        ip
      );

      console.error('Security middleware error:', error);
      
      const response = NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );

      // Still add enhanced security headers even on error
      const securityHeaders = getEnhancedSecurityHeaders();
      Object.entries(securityHeaders).forEach(([key, value]) => {
        response.headers.set(key, value);
      });

      return response;
    }
  };
}

// Specific security configurations for different endpoint types
export const securityConfigs = {
  upload: {
    enableSizeLimit: true,
    sizeLimit: SIZE_LIMITS.upload,
    allowedMethods: ['POST', 'OPTIONS'] as string[],
    requireAuth: true
  },

  assessment: {
    enableSizeLimit: true,
    sizeLimit: SIZE_LIMITS.assessment,
    allowedMethods: ['POST', 'OPTIONS'] as string[],
    requireAuth: true
  },

  general: {
    enableSizeLimit: true,
    sizeLimit: SIZE_LIMITS.general,
    allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'] as string[],
    requireAuth: true
  },

  auth: {
    enableSizeLimit: true,
    sizeLimit: SIZE_LIMITS.auth,
    allowedMethods: ['POST', 'OPTIONS'] as string[],
    requireAuth: false
  },

  public: {
    enableSizeLimit: true,
    sizeLimit: SIZE_LIMITS.general,
    allowedMethods: ['GET', 'OPTIONS'] as string[],
    requireAuth: false
  }
};

// Helper to validate request body structure
export function validateRequestStructure(
  body: any,
  requiredFields: string[],
  optionalFields: string[] = []
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!body || typeof body !== 'object') {
    return { isValid: false, errors: ['Request body must be a valid JSON object'] };
  }

  // Check required fields
  for (const field of requiredFields) {
    if (!(field in body) || body[field] === undefined || body[field] === null) {
      errors.push(`Missing required field: ${field}`);
    }
  }

  // Check for unexpected fields
  const allowedFields = [...requiredFields, ...optionalFields];
  const bodyFields = Object.keys(body);
  const unexpectedFields = bodyFields.filter(field => !allowedFields.includes(field));
  
  if (unexpectedFields.length > 0) {
    errors.push(`Unexpected fields: ${unexpectedFields.join(', ')}`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

// Enhanced file validation
export function validateFileUpload(file: File): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // File size validation
  if (file.size > SIZE_LIMITS.upload) {
    errors.push(`File size ${Math.round(file.size / 1024 / 1024)}MB exceeds maximum ${Math.round(SIZE_LIMITS.upload / 1024 / 1024)}MB`);
  }

  // File type validation
  const allowedTypes = [
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'image/png',
    'image/jpeg',
    'application/zip'
  ];

  if (!allowedTypes.includes(file.type)) {
    errors.push(`File type ${file.type} is not allowed`);
  }

  // File name validation
  const fileName = file.name.toLowerCase();
  
  // Check for dangerous extensions
  const dangerousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.js', '.vbs'];
  if (dangerousExtensions.some(ext => fileName.endsWith(ext))) {
    errors.push('File type not allowed for security reasons');
  }

  // Check for suspicious patterns
  const suspiciousPatterns = [/script/i, /virus/i, /malware/i];
  if (suspiciousPatterns.some(pattern => pattern.test(fileName))) {
    warnings.push('File name contains suspicious patterns');
  }

  // File name length
  if (file.name.length > 255) {
    errors.push('File name too long (maximum 255 characters)');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}
