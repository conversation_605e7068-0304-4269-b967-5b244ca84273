/**
 * Hook for managing assignment reset functionality
 * Provides clean slate capability for assignment reuse
 * Uses API routes to bypass GraphQL allowlist restrictions
 */

import { useState } from 'react';

export interface AssignmentResetOptions {
  assignmentId: string;
  onSuccess?: (clearedCount: number) => void;
  onError?: (error: Error) => void;
}

export interface AssignmentResetState {
  isResetting: boolean;
  lastResetCount: number | null;
  error: string | null;
}

export function useAssignmentReset() {
  const [state, setState] = useState<AssignmentResetState>({
    isResetting: false,
    lastResetCount: null,
    error: null
  });

  const resetAssignment = async (options: AssignmentResetOptions) => {
    const { assignmentId, onSuccess, onError } = options;

    setState(prev => ({ ...prev, isResetting: true, error: null }));

    try {
      console.log(`🔄 Starting assignment reset (delete) for ${assignmentId}`);

      // Use API route to delete submissions (only option now)
      const response = await fetch('/api/assignment-reset', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          assignmentId,
          resetType: 'delete'
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        const clearedCount = result.data?.clearedCount || 0;
        const clearedStudents = result.data?.clearedStudents || [];

        console.log(`✅ Deleted ${clearedCount} submissions for clean slate`);
        clearedStudents.forEach((studentName: string) => {
          console.log(`  - Deleted submission for: ${studentName}`);
        });

        setState(prev => ({
          ...prev,
          isResetting: false,
          lastResetCount: clearedCount
        }));

        onSuccess?.(clearedCount);
      } else {
        throw new Error(result.error || 'Reset operation failed');
      }

    } catch (error) {
      console.error('❌ Failed to reset assignment:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

      setState(prev => ({
        ...prev,
        isResetting: false,
        error: errorMessage
      }));

      onError?.(error instanceof Error ? error : new Error(errorMessage));
    }
  };

  const clearError = () => {
    setState(prev => ({ ...prev, error: null }));
  };

  return {
    ...state,
    resetAssignment,
    clearError
  };
}

/**
 * Utility function to confirm assignment reset with user
 */
export function confirmAssignmentReset(
  assignmentTitle: string,
  submissionCount: number
): boolean {
  const message = `Are you sure you want to permanently delete all ${submissionCount} submissions for "${assignmentTitle}"?

This will provide a clean slate for the assignment.

⚠️ This action cannot be undone!`;

  return window.confirm(message);
}

/**
 * Hook for getting submission count for an assignment
 */
export function useSubmissionCount(assignmentId: string | null) {
  // This would typically use a GraphQL query to get the count
  // For now, we'll return a placeholder that can be enhanced
  return {
    count: 0, // This should be populated from actual query
    loading: false
  };
}
