import { NextRequest, NextResponse } from 'next/server';
import { getServerApolloClient } from '@/lib/apollo-client';
import { gql } from '@apollo/client';

// GraphQL query for single assessment results using admin client (bypasses allowlist)
const GET_ASSESSMENT_RESULTS_ADMIN = gql`
  query GetAssessmentResultsAdmin($submissionId: uuid!) {
    assessment_results(where: { submission_id: { _eq: $submissionId } }) {
      id
      overall_grade
      general_comments
      exported_pdf_path
      reviewed
      created_at
      student_submission {
        id
        student_name
        assignment {
          id
          title
          description
        }
      }
      per_criterion_feedbacks {
        id
        criterion_id
        score
        feedback_text
        assessment_criterium {
          criterion_name
        }
      }
    }
  }
`;

// GraphQL query for bulk assessment results using admin client (bypasses allowlist)
const GET_BULK_ASSESSMENT_RESULTS_ADMIN = gql`
  query GetBulkAssessmentResultsAdmin($assignmentId: uuid!) {
    assessment_results(
      where: {
        student_submission: { assignment_id: { _eq: $assignmentId } }
      }
      order_by: { created_at: desc }
    ) {
      id
      overall_grade
      general_comments
      exported_pdf_path
      reviewed
      created_at
      student_submission {
        id
        student_name
        assignment {
          id
          title
          description
        }
      }
      per_criterion_feedbacks {
        id
        criterion_id
        score
        feedback_text
        assessment_criterium {
          criterion_name
        }
      }
    }
  }
`;

// GraphQL query for submission details using admin client
const GET_SUBMISSION_DETAILS_ADMIN = gql`
  query GetSubmissionDetailsAdmin($submissionId: uuid!) {
    student_submissions_by_pk(id: $submissionId) {
      id
      student_name
      processing_status
      assignment {
        id
        title
        description
      }
    }
  }
`;

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const submissionId = searchParams.get('submissionId');
    const assignmentId = searchParams.get('assignmentId');

    if (!submissionId && !assignmentId) {
      return NextResponse.json(
        { error: 'Either submissionId or assignmentId parameter is required' },
        { status: 400 }
      );
    }

    console.log('🔍 Fetching assessment results:', { submissionId, assignmentId });

    // Use server-side Apollo client with admin secret
    const apolloClient = getServerApolloClient();

    if (assignmentId) {
      // Bulk assessment results for an assignment
      const result = await apolloClient.query({
        query: GET_BULK_ASSESSMENT_RESULTS_ADMIN,
        variables: {
          assignmentId: assignmentId,
        },
      });

      console.log('✅ Successfully fetched bulk assessment results:', result.data.assessment_results.length);

      return NextResponse.json({
        assessment_results: result.data.assessment_results,
        count: result.data.assessment_results.length
      });

    } else if (submissionId) {
      // Single assessment result for a submission
      const result = await apolloClient.query({
        query: GET_ASSESSMENT_RESULTS_ADMIN,
        variables: {
          submissionId: submissionId,
        },
      });

      console.log('✅ Successfully fetched assessment results:', result.data.assessment_results.length);

      // If no assessment results found, try to get submission details
      if (result.data.assessment_results.length === 0) {
        console.log('🔍 No assessment results found, fetching submission details...');
        
        const submissionResult = await apolloClient.query({
          query: GET_SUBMISSION_DETAILS_ADMIN,
          variables: {
            submissionId: submissionId,
          },
        });

        return NextResponse.json({
          assessment_results: [],
          submission: submissionResult.data.student_submissions_by_pk,
          count: 0
        });
      }

      // Debug per-criteria feedback specifically
      if (result.data.assessment_results?.length > 0) {
        const assessment = result.data.assessment_results[0];
        console.log('🔍 Per-criteria feedback debug:');
        console.log(`  - Assessment ID: ${assessment.id}`);
        console.log(`  - Per-criteria count: ${assessment.per_criterion_feedbacks?.length || 0}`);

        if (assessment.per_criterion_feedbacks?.length > 0) {
          console.log('  - Per-criteria details:');
          assessment.per_criterion_feedbacks.forEach((feedback: any, index: number) => {
            console.log(`    ${index + 1}. Criterion: ${feedback.assessment_criterium?.criterion_name || 'Unknown'}`);
            console.log(`       Score: ${feedback.score || 'N/A'}`);
            console.log(`       Feedback: ${feedback.feedback_text ? feedback.feedback_text.substring(0, 50) + '...' : 'None'}`);
          });
        } else {
          console.log('  - ⚠️ No per-criteria feedback found in API response');
        }
      }

      return NextResponse.json({
        assessment_results: result.data.assessment_results,
        count: result.data.assessment_results.length
      });
    }

  } catch (error: any) {
    console.error('❌ API error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
