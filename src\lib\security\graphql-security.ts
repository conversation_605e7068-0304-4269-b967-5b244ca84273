/**
 * GraphQL Security System
 * Provides query depth limiting, complexity analysis, and introspection control
 */

export interface GraphQLSecurityConfig {
  maxDepth: number;
  maxComplexity: number;
  enableIntrospection: boolean;
  enableQueryWhitelist: boolean;
  maxAliases: number;
  maxDirectives: number;
}

export interface QueryAnalysisResult {
  isValid: boolean;
  depth: number;
  complexity: number;
  aliases: number;
  directives: number;
  errors: string[];
  warnings: string[];
}

// Default GraphQL security configuration
export const DEFAULT_GRAPHQL_SECURITY_CONFIG: GraphQLSecurityConfig = {
  maxDepth: 10,
  maxComplexity: 1000,
  enableIntrospection: process.env.NODE_ENV === 'development',
  enableQueryWhitelist: process.env.NODE_ENV === 'production',
  maxAliases: 15,
  maxDirectives: 10
};

// Dangerous GraphQL patterns
const DANGEROUS_PATTERNS = [
  /\b__schema\b/gi,
  /\b__type\b/gi,
  /\bintrospection\b/gi,
  /\bmutation\s*\{.*\}/gi,
  /\bsubscription\s*\{.*\}/gi
];

// Query complexity weights for different field types
const COMPLEXITY_WEIGHTS = {
  scalar: 1,
  object: 2,
  list: 5,
  connection: 10,
  mutation: 15,
  subscription: 20
};

/**
 * Parse GraphQL query to extract basic structure
 */
export function parseGraphQLQuery(query: string): {
  operationType: 'query' | 'mutation' | 'subscription' | 'unknown';
  fields: string[];
  aliases: string[];
  directives: string[];
} {
  const operationType = query.match(/^\s*(query|mutation|subscription)/i)?.[1]?.toLowerCase() as any || 'unknown';
  
  // Extract field names (simplified parsing)
  const fieldMatches = query.match(/\b[a-zA-Z_][a-zA-Z0-9_]*\s*(?:\([^)]*\))?\s*\{/g) || [];
  const fields = fieldMatches.map(match => match.replace(/\s*\{.*/, '').trim());
  
  // Extract aliases
  const aliasMatches = query.match(/\b[a-zA-Z_][a-zA-Z0-9_]*\s*:\s*[a-zA-Z_][a-zA-Z0-9_]*/g) || [];
  const aliases = aliasMatches.map(match => match.split(':')[0].trim());
  
  // Extract directives
  const directiveMatches = query.match(/@[a-zA-Z_][a-zA-Z0-9_]*(?:\([^)]*\))?/g) || [];
  const directives = directiveMatches.map(match => match.replace(/\([^)]*\)/, ''));
  
  return { operationType, fields, aliases, directives };
}

/**
 * Calculate query depth
 */
export function calculateQueryDepth(query: string): number {
  let maxDepth = 0;
  let currentDepth = 0;
  
  for (const char of query) {
    if (char === '{') {
      currentDepth++;
      maxDepth = Math.max(maxDepth, currentDepth);
    } else if (char === '}') {
      currentDepth--;
    }
  }
  
  return maxDepth;
}

/**
 * Calculate query complexity (simplified)
 */
export function calculateQueryComplexity(query: string): number {
  const parsed = parseGraphQLQuery(query);
  let complexity = 0;
  
  // Base complexity for operation type
  switch (parsed.operationType) {
    case 'query':
      complexity += COMPLEXITY_WEIGHTS.scalar;
      break;
    case 'mutation':
      complexity += COMPLEXITY_WEIGHTS.mutation;
      break;
    case 'subscription':
      complexity += COMPLEXITY_WEIGHTS.subscription;
      break;
  }
  
  // Add complexity for fields
  complexity += parsed.fields.length * COMPLEXITY_WEIGHTS.object;
  
  // Add complexity for aliases (can be used for amplification attacks)
  complexity += parsed.aliases.length * COMPLEXITY_WEIGHTS.list;
  
  // Add complexity for directives
  complexity += parsed.directives.length * COMPLEXITY_WEIGHTS.scalar;
  
  // Check for list fields (simplified detection)
  const listFields = query.match(/\[\s*[a-zA-Z_][a-zA-Z0-9_]*\s*\]/g) || [];
  complexity += listFields.length * COMPLEXITY_WEIGHTS.list;
  
  // Check for connection patterns
  const connectionFields = query.match(/\b(edges|nodes|pageInfo)\b/g) || [];
  complexity += connectionFields.length * COMPLEXITY_WEIGHTS.connection;
  
  return complexity;
}

/**
 * Check for dangerous GraphQL patterns
 */
export function checkDangerousPatterns(query: string): { isDangerous: boolean; matches: string[] } {
  const matches: string[] = [];
  
  for (const pattern of DANGEROUS_PATTERNS) {
    const match = query.match(pattern);
    if (match) {
      matches.push(match[0]);
    }
  }
  
  return { isDangerous: matches.length > 0, matches };
}

/**
 * Analyze GraphQL query security
 */
export function analyzeGraphQLQuery(
  query: string,
  config: GraphQLSecurityConfig = DEFAULT_GRAPHQL_SECURITY_CONFIG
): QueryAnalysisResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  let isValid = true;
  
  // Parse query
  const parsed = parseGraphQLQuery(query);
  const depth = calculateQueryDepth(query);
  const complexity = calculateQueryComplexity(query);
  const aliases = parsed.aliases.length;
  const directives = parsed.directives.length;
  
  // Check depth limit
  if (depth > config.maxDepth) {
    errors.push(`Query depth ${depth} exceeds maximum allowed depth ${config.maxDepth}`);
    isValid = false;
  }
  
  // Check complexity limit
  if (complexity > config.maxComplexity) {
    errors.push(`Query complexity ${complexity} exceeds maximum allowed complexity ${config.maxComplexity}`);
    isValid = false;
  }
  
  // Check alias limit
  if (aliases > config.maxAliases) {
    errors.push(`Number of aliases ${aliases} exceeds maximum allowed ${config.maxAliases}`);
    isValid = false;
  }
  
  // Check directive limit
  if (directives > config.maxDirectives) {
    errors.push(`Number of directives ${directives} exceeds maximum allowed ${config.maxDirectives}`);
    isValid = false;
  }
  
  // Check for introspection queries
  if (!config.enableIntrospection) {
    const dangerousCheck = checkDangerousPatterns(query);
    if (dangerousCheck.isDangerous) {
      errors.push(`Introspection queries are disabled: ${dangerousCheck.matches.join(', ')}`);
      isValid = false;
    }
  }
  
  // Warnings for suspicious patterns
  if (complexity > config.maxComplexity * 0.8) {
    warnings.push('Query complexity is approaching the limit');
  }
  
  if (depth > config.maxDepth * 0.8) {
    warnings.push('Query depth is approaching the limit');
  }
  
  if (aliases > config.maxAliases * 0.8) {
    warnings.push('Number of aliases is approaching the limit');
  }
  
  return {
    isValid,
    depth,
    complexity,
    aliases,
    directives,
    errors,
    warnings
  };
}

/**
 * GraphQL security middleware for API routes
 */
export function withGraphQLSecurity(
  handler: (request: Request) => Promise<Response>,
  config: GraphQLSecurityConfig = DEFAULT_GRAPHQL_SECURITY_CONFIG
) {
  return async (request: Request): Promise<Response> => {
    try {
      // Only process GraphQL requests
      if (!request.url.includes('/graphql') && !request.headers.get('content-type')?.includes('application/json')) {
        return handler(request);
      }
      
      // Parse request body to extract GraphQL query
      let body: any;
      try {
        const text = await request.text();
        body = JSON.parse(text);
        
        // Recreate request with the body for the handler
        request = new Request(request.url, {
          method: request.method,
          headers: request.headers,
          body: text
        });
      } catch (error) {
        return new Response(
          JSON.stringify({ error: 'Invalid JSON in GraphQL request' }),
          { status: 400, headers: { 'Content-Type': 'application/json' } }
        );
      }
      
      // Extract GraphQL query
      const query = body.query;
      if (!query || typeof query !== 'string') {
        return new Response(
          JSON.stringify({ error: 'GraphQL query is required' }),
          { status: 400, headers: { 'Content-Type': 'application/json' } }
        );
      }
      
      // Analyze query security
      const analysis = analyzeGraphQLQuery(query, config);
      
      if (!analysis.isValid) {
        console.warn('🚨 GraphQL Security Violation:', {
          query: query.substring(0, 200) + '...',
          errors: analysis.errors,
          depth: analysis.depth,
          complexity: analysis.complexity
        });
        
        return new Response(
          JSON.stringify({
            error: 'GraphQL query violates security policies',
            details: analysis.errors
          }),
          { status: 400, headers: { 'Content-Type': 'application/json' } }
        );
      }
      
      // Log warnings
      if (analysis.warnings.length > 0) {
        console.warn('⚠️ GraphQL Security Warning:', {
          query: query.substring(0, 100) + '...',
          warnings: analysis.warnings
        });
      }
      
      // Continue with the request
      return handler(request);
      
    } catch (error) {
      console.error('GraphQL security middleware error:', error);
      return new Response(
        JSON.stringify({ error: 'GraphQL security validation failed' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }
  };
}

/**
 * Query whitelist for production
 */
export class GraphQLQueryWhitelist {
  private allowedQueries: Set<string> = new Set();
  private allowedQueryHashes: Set<string> = new Set();
  
  addQuery(query: string): void {
    const normalized = this.normalizeQuery(query);
    this.allowedQueries.add(normalized);
    
    // Also store hash for faster lookup
    this.generateQueryHash(normalized).then(hash => {
      this.allowedQueryHashes.add(hash);
    });
  }
  
  isQueryAllowed(query: string): boolean {
    const normalized = this.normalizeQuery(query);
    return this.allowedQueries.has(normalized);
  }
  
  async isQueryHashAllowed(query: string): Promise<boolean> {
    const hash = await this.generateQueryHash(query);
    return this.allowedQueryHashes.has(hash);
  }
  
  private normalizeQuery(query: string): string {
    return query
      .replace(/\s+/g, ' ')
      .replace(/\s*([{}(),:])\s*/g, '$1')
      .trim();
  }
  
  private async generateQueryHash(query: string): Promise<string> {
    const encoder = new TextEncoder();
    const data = encoder.encode(query);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }
}

// Global query whitelist instance
export const queryWhitelist = new GraphQLQueryWhitelist();
