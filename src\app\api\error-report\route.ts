import { NextRequest, NextResponse } from 'next/server';
import { withSecurity, securityConfigs } from '@/lib/middleware/security';
import { withRateLimit } from '@/lib/middleware/rateLimiter';
import { logAPIError, getClientInfo } from '@/lib/security/monitoring';

interface ErrorReport {
  errorId: string;
  message: string;
  stack?: string;
  componentStack?: string;
  userAgent: string;
  url: string;
  timestamp: string;
  userId?: string;
}

// Error reporting handler
const errorReportHandler = async (request: NextRequest) => {
  try {
    const { ip, userAgent } = getClientInfo(request);
    
    const body = await request.json();
    const {
      errorId,
      message,
      stack,
      componentStack,
      url,
      timestamp,
      userId
    }: ErrorReport = body;

    // Validate required fields
    if (!errorId || !message || !timestamp) {
      return NextResponse.json(
        { error: 'Missing required fields: errorId, message, timestamp' },
        { status: 400 }
      );
    }

    // Log the error report
    console.error('🚨 Client Error Report:', {
      errorId,
      message,
      url,
      timestamp,
      ip,
      userAgent: userAgent || body.userAgent,
      userId
    });

    // In production, you would typically:
    // 1. Store in database for analysis
    // 2. Send to external monitoring service (Sentry, DataDog, etc.)
    // 3. Alert on critical errors
    // 4. Generate metrics for error tracking

    // For now, we'll store in a simple log format
    const errorLog = {
      id: errorId,
      timestamp: new Date(timestamp),
      level: 'error',
      source: 'client',
      message,
      metadata: {
        stack,
        componentStack,
        url,
        userAgent: userAgent || body.userAgent,
        ip,
        userId
      }
    };

    // Log to console with structured format
    console.error('CLIENT_ERROR_REPORT', JSON.stringify(errorLog, null, 2));

    // In a real implementation, you might:
    // await database.errors.create(errorLog);
    // await sendToSentry(errorLog);
    // await checkForCriticalPatterns(errorLog);

    return NextResponse.json({
      success: true,
      message: 'Error report received',
      errorId
    });

  } catch (error) {
    const { ip } = getClientInfo(request);
    
    logAPIError(
      '/api/error-report',
      error instanceof Error ? error.message : 'Unknown error',
      undefined,
      ip
    );

    console.error('Failed to process error report:', error);
    
    return NextResponse.json(
      { error: 'Failed to process error report' },
      { status: 500 }
    );
  }
};

// Apply security middleware and rate limiting
const secureHandler = withSecurity(
  withRateLimit(errorReportHandler, 'general'),
  {
    ...securityConfigs.general,
    allowedMethods: ['POST', 'OPTIONS'],
    enableSizeLimit: true,
    sizeLimit: 10 * 1024 // 10KB limit for error reports
  }
);

export const POST = secureHandler;

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
