/**
 * Next.js Middleware for Route Protection
 * Handles authentication and authorization at the edge
 */

import { NextRequest, NextResponse } from 'next/server';

// Protected routes that require authentication
const PROTECTED_ROUTES = [
  '/dashboard',
  '/assignments',
  '/student-upload',
  '/assessment',
  '/submissions',
  '/analytics'
];

// Admin-only routes
const ADMIN_ROUTES = [
  '/admin',
  '/security-metrics'
];

// API routes that require authentication
const PROTECTED_API_ROUTES = [
  '/api/assignments',
  '/api/submissions',
  '/api/upload',
  '/api/assess-submission',
  '/api/bulk-assess',
  '/api/assignment-reset',
  '/api/security-metrics'
];

// Public routes that don't require authentication
const PUBLIC_ROUTES = [
  '/',
  '/auth/sign-in',
  '/auth/sign-up',
  '/auth/reset-password'
];

/**
 * Check if a path matches any of the given patterns
 */
function matchesPath(pathname: string, patterns: string[]): boolean {
  return patterns.some(pattern => {
    if (pattern.endsWith('*')) {
      return pathname.startsWith(pattern.slice(0, -1));
    }
    return pathname === pattern || pathname.startsWith(pattern + '/');
  });
}

/**
 * Extract token from request cookies or headers
 */
function getAuthToken(request: NextRequest): string | null {
  // Try to get token from Authorization header
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // Try to get token from cookies (Nhost stores it here)
  const tokenCookie = request.cookies.get('nhostAccessToken');
  if (tokenCookie) {
    return tokenCookie.value;
  }

  return null;
}

/**
 * Validate JWT token (basic validation for middleware)
 */
function isValidToken(token: string): boolean {
  try {
    // Basic JWT structure validation
    const parts = token.split('.');
    if (parts.length !== 3) {
      return false;
    }

    // Check if token is not expired (basic check)
    const payload = JSON.parse(atob(parts[1]));
    const now = Math.floor(Date.now() / 1000);
    
    if (payload.exp && payload.exp < now) {
      return false; // Token expired
    }

    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Get user role from JWT token
 */
function getUserRole(token: string): string | null {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    const payload = JSON.parse(atob(parts[1]));
    const hasuraClaims = payload['https://hasura.io/jwt/claims'];
    
    return hasuraClaims?.['x-hasura-default-role'] || null;
  } catch (error) {
    return null;
  }
}

/**
 * Main middleware function
 */
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static files and Next.js internals
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/static/') ||
    pathname.includes('.') ||
    pathname === '/favicon.ico'
  ) {
    return NextResponse.next();
  }

  // Allow public routes
  if (matchesPath(pathname, PUBLIC_ROUTES)) {
    return NextResponse.next();
  }

  // Get authentication token
  const token = getAuthToken(request);

  // Check if route requires authentication
  const requiresAuth = matchesPath(pathname, PROTECTED_ROUTES) || 
                      matchesPath(pathname, PROTECTED_API_ROUTES);

  if (requiresAuth) {
    if (!token || !isValidToken(token)) {
      // Redirect to sign-in for web pages
      if (!pathname.startsWith('/api/')) {
        const signInUrl = new URL('/auth/sign-in', request.url);
        signInUrl.searchParams.set('redirect', pathname);
        return NextResponse.redirect(signInUrl);
      }

      // Return 401 for API routes
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check admin routes
    if (matchesPath(pathname, ADMIN_ROUTES)) {
      const userRole = getUserRole(token);
      
      if (userRole !== 'admin') {
        // Redirect to dashboard for web pages
        if (!pathname.startsWith('/api/')) {
          return NextResponse.redirect(new URL('/dashboard', request.url));
        }

        // Return 403 for API routes
        return NextResponse.json(
          { error: 'Admin access required' },
          { status: 403 }
        );
      }
    }
  }

  // Add security headers to all responses
  const response = NextResponse.next();

  // Security headers
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Add authentication info to headers for API routes (if authenticated)
  if (token && isValidToken(token) && pathname.startsWith('/api/')) {
    response.headers.set('X-User-Authenticated', 'true');
    
    const userRole = getUserRole(token);
    if (userRole) {
      response.headers.set('X-User-Role', userRole);
    }
  }

  return response;
}

/**
 * Middleware configuration
 */
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};

export default middleware;
