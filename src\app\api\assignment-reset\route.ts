/**
 * Assignment Reset API Route
 * Provides clean slate functionality by clearing/archiving submissions
 * Bypasses GraphQL allowlist restrictions
 */

import { NextRequest, NextResponse } from 'next/server';
import { gql } from '@apollo/client';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { assignmentId, resetType } = body;

    if (!assignmentId) {
      return NextResponse.json(
        { success: false, error: 'Assignment ID is required' },
        { status: 400 }
      );
    }

    // Only delete is supported now (archive was unreliable)
    if (resetType && resetType !== 'delete') {
      return NextResponse.json(
        { success: false, error: 'Only delete reset type is supported' },
        { status: 400 }
      );
    }

    console.log(`🔄 Starting assignment reset (delete) for assignment ${assignmentId}`);

    // Get Apollo client with admin secret
    const { getServerApolloClient } = await import('@/lib/apollo-client');
    const client = getServerApolloClient();

    // Delete submissions permanently (only option now)
    const DELETE_SUBMISSIONS = gql`
      mutation DeleteAssignmentSubmissions($assignment_id: uuid!) {
        delete_student_submissions(
          where: { assignment_id: { _eq: $assignment_id } }
        ) {
          affected_rows
          returning {
            id
            student_name
          }
        }
      }
    `;

    const result = await client.mutate({
      mutation: DELETE_SUBMISSIONS,
      variables: { assignment_id: assignmentId }
    });

    const clearedCount = result.data?.delete_student_submissions?.affected_rows || 0;
    const clearedStudents = result.data?.delete_student_submissions?.returning || [];

    console.log(`🗑️ Deleted ${clearedCount} submissions for assignment ${assignmentId} (clean slate)`);
    clearedStudents.forEach((student: any) => {
      console.log(`  - Deleted submission for: ${student.student_name}`);
    });

    return NextResponse.json({
      success: true,
      message: `Successfully deleted ${clearedCount} submissions - clean slate ready`,
      data: {
        resetType: 'delete',
        assignmentId,
        clearedCount,
        clearedStudents: clearedStudents.map((s: any) => s.student_name),
        processedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Assignment reset API error:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        message: 'Failed to reset assignment submissions'
      },
      { status: 500 }
    );
  }
}

/**
 * Get assignment reset status/info
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const assignmentId = searchParams.get('assignmentId');

    if (!assignmentId) {
      return NextResponse.json(
        { success: false, error: 'Assignment ID is required' },
        { status: 400 }
      );
    }

    // Get Apollo client with admin secret
    const { getServerApolloClient } = await import('@/lib/apollo-client');
    const client = getServerApolloClient();

    // Get submission count for the assignment
    const GET_SUBMISSION_COUNT = gql`
      query GetSubmissionCount($assignment_id: uuid!) {
        student_submissions_aggregate(
          where: { assignment_id: { _eq: $assignment_id } }
        ) {
          aggregate {
            count
          }
        }
        student_submissions(
          where: { assignment_id: { _eq: $assignment_id } }
          limit: 5
        ) {
          id
          student_name
          processing_status
          upload_date
        }
      }
    `;

    const { data } = await client.query({
      query: GET_SUBMISSION_COUNT,
      variables: { assignment_id: assignmentId }
    });

    const submissionCount = data?.student_submissions_aggregate?.aggregate?.count || 0;
    const recentSubmissions = data?.student_submissions || [];

    return NextResponse.json({
      success: true,
      assignmentId,
      submissionCount,
      recentSubmissions: recentSubmissions.map((sub: any) => ({
        id: sub.id,
        studentName: sub.student_name,
        status: sub.processing_status,
        uploadDate: sub.upload_date
      }))
    });

  } catch (error) {
    console.error('❌ Assignment reset status error:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}
