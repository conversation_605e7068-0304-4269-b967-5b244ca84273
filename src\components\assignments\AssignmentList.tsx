import Link from 'next/link';
import Button from '@/components/ui/Button';
import { useUserData, useAuthenticationStatus, useAccessToken } from '@nhost/nextjs';
import { useState, useEffect } from 'react';

export default function AssignmentList() {
  const user = useUserData();
  const { isAuthenticated } = useAuthenticationStatus();
  const accessToken = useAccessToken();

  const [assignments, setAssignments] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Fetch assignments using API route
  useEffect(() => {
    if (!isAuthenticated || !user?.id || !accessToken) {
      setLoading(false);
      return;
    }

    const fetchAssignments = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('🔍 Fetching assignments via API route...');

        const response = await fetch('/api/assignments', {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        });
        const result = await response.json();

        if (response.ok && result.success) {
          setAssignments(result.assignments);
          console.log('✅ Assignments fetched successfully:', result.assignments.length);
        } else {
          throw new Error(result.error || 'Failed to fetch assignments');
        }
      } catch (err) {
        console.error('❌ Failed to fetch assignments:', err);
        setError(err instanceof Error ? err : new Error('Unknown error'));
      } finally {
        setLoading(false);
      }
    };

    fetchAssignments();
  }, [isAuthenticated, user?.id, accessToken]);



  return (
    <div className="w-full">


      <div className="flex justify-between items-center mb-8">
        <h2 className="text-2xl font-light text-text">Your Assignments</h2>
        <Link href="/assignments/new">
          <Button variant="primary" className="py-2 px-4">
            Create Assignment
          </Button>
        </Link>
      </div>

      {loading ? (
        <div className="bg-white rounded-lg shadow p-12 text-center">
          <p>Loading assignments...</p>
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <h3 className="text-red-800 font-medium mb-2">Error Loading Assignments</h3>
          <p className="text-red-600">{error.message}</p>
        </div>
      ) : assignments.length === 0 ? (
        <div className="bg-white rounded-lg shadow p-12 text-center">
          <h3 className="text-xl font-light mb-4">No Assignments Yet</h3>
          <p className="mb-6 text-gray-600">You haven't created any assignments yet. Get started by creating your first assignment.</p>
          <Link href="/assignments/new">
            <Button variant="primary">Create Your First Assignment</Button>
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {assignments.map((assignment) => (
            <div key={assignment.id} className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium mb-2">{assignment.title}</h3>
              <p className="text-gray-600 mb-4">{assignment.description}</p>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">
                  Created: {new Date(assignment.created_at).toLocaleDateString()}
                </span>
                <Link href={`/assignments/${assignment.id}`} className="text-blue-600 hover:text-blue-800">
                  View →
                </Link>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
