{"name": "ess", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "codegen": "graphql-codegen", "codegen:watch": "graphql-codegen --watch"}, "dependencies": {"@apollo/client": "^3.13.8", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@nhost/nextjs": "^2.2.7", "graphql": "^16.11.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@graphql-codegen/cli": "^5.0.6", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.1", "@graphql-codegen/typescript-react-apollo": "^4.3.2", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^16.5.0", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}