'use client';

import React, { useState, useEffect } from 'react';
import { useAuthenticationStatus, useUserData } from '@nhost/nextjs';
import { 
  <PERSON><PERSON>hart, Bar, LineChart, Line, PieChart, Pie, Cell, 
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  Scatter<PERSON>hart, Scatter, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar
} from 'recharts';
import Button from '@/components/ui/Button';

interface AdvancedAnalyticsData {
  aiModelUsage: Array<{ model: string; count: number; cost: number; color: string }>;
  performanceTrends: Array<{ date: string; avgScore: number; submissions: number; processingTime: number }>;
  subjectPerformance: Array<{ subject: string; avgGrade: number; submissions: number; satisfaction: number }>;
  routingEfficiency: Array<{ strategy: string; accuracy: number; cost: number; speed: number }>;
  predictiveInsights: Array<{ metric: string; current: number; predicted: number; trend: 'up' | 'down' | 'stable' }>;
  realTimeMetrics: {
    activeAssessments: number;
    queueLength: number;
    avgProcessingTime: number;
    successRate: number;
  };
}

interface ExportOptions {
  format: 'pdf' | 'excel' | 'csv';
  dateRange: 'week' | 'month' | 'quarter' | 'year';
  includeCharts: boolean;
  includeRawData: boolean;
}

export default function AdvancedAnalyticsDashboard() {
  const { isAuthenticated } = useAuthenticationStatus();
  const user = useUserData();
  
  const [data, setData] = useState<AdvancedAnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTimeRange, setSelectedTimeRange] = useState<'week' | 'month' | 'quarter' | 'year'>('month');
  const [selectedView, setSelectedView] = useState<'overview' | 'performance' | 'ai-insights' | 'predictions'>('overview');
  const [realTimeEnabled, setRealTimeEnabled] = useState(false);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'pdf',
    dateRange: 'month',
    includeCharts: true,
    includeRawData: false
  });

  // Fetch advanced analytics data
  useEffect(() => {
    if (!isAuthenticated || !user?.id) return;

    const fetchAdvancedAnalytics = async () => {
      try {
        setLoading(true);
        
        // Simulate advanced analytics data (replace with real API calls)
        const mockData: AdvancedAnalyticsData = {
          aiModelUsage: [
            { model: 'GPT-4', count: 45, cost: 12.50, color: '#10B981' },
            { model: 'Claude-3', count: 38, cost: 15.20, color: '#3B82F6' },
            { model: 'GPT-3.5', count: 22, cost: 3.80, color: '#F59E0B' }
          ],
          performanceTrends: [
            { date: '2024-01-01', avgScore: 85, submissions: 12, processingTime: 25 },
            { date: '2024-01-02', avgScore: 87, submissions: 15, processingTime: 23 },
            { date: '2024-01-03', avgScore: 89, submissions: 18, processingTime: 22 },
            { date: '2024-01-04', avgScore: 86, submissions: 14, processingTime: 24 },
            { date: '2024-01-05', avgScore: 91, submissions: 20, processingTime: 21 }
          ],
          subjectPerformance: [
            { subject: 'Mathematics', avgGrade: 88, submissions: 25, satisfaction: 92 },
            { subject: 'Literature', avgGrade: 85, submissions: 30, satisfaction: 89 },
            { subject: 'Science', avgGrade: 90, submissions: 20, satisfaction: 94 },
            { subject: 'History', avgGrade: 82, submissions: 15, satisfaction: 87 }
          ],
          routingEfficiency: [
            { strategy: 'Quality-Optimized', accuracy: 95, cost: 85, speed: 70 },
            { strategy: 'Cost-Optimized', accuracy: 88, cost: 95, speed: 85 },
            { strategy: 'Speed-Optimized', accuracy: 90, cost: 75, speed: 95 },
            { strategy: 'Balanced', accuracy: 92, cost: 80, speed: 80 }
          ],
          predictiveInsights: [
            { metric: 'Submission Volume', current: 150, predicted: 180, trend: 'up' },
            { metric: 'Average Score', current: 87, predicted: 89, trend: 'up' },
            { metric: 'Processing Time', current: 23, predicted: 20, trend: 'down' },
            { metric: 'Cost per Assessment', current: 0.45, predicted: 0.38, trend: 'down' }
          ],
          realTimeMetrics: {
            activeAssessments: 5,
            queueLength: 2,
            avgProcessingTime: 22,
            successRate: 98.5
          }
        };

        setData(mockData);
      } catch (error) {
        console.error('Failed to fetch advanced analytics:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAdvancedAnalytics();
  }, [isAuthenticated, user?.id, selectedTimeRange]);

  // Real-time updates
  useEffect(() => {
    if (!realTimeEnabled || !data) return;

    const interval = setInterval(() => {
      setData(prevData => {
        if (!prevData) return prevData;
        
        return {
          ...prevData,
          realTimeMetrics: {
            ...prevData.realTimeMetrics,
            activeAssessments: Math.max(0, prevData.realTimeMetrics.activeAssessments + Math.floor(Math.random() * 3) - 1),
            queueLength: Math.max(0, prevData.realTimeMetrics.queueLength + Math.floor(Math.random() * 3) - 1),
            avgProcessingTime: Math.max(15, prevData.realTimeMetrics.avgProcessingTime + Math.floor(Math.random() * 6) - 3),
            successRate: Math.min(100, Math.max(95, prevData.realTimeMetrics.successRate + (Math.random() - 0.5) * 2))
          }
        };
      });
    }, 2000);

    return () => clearInterval(interval);
  }, [realTimeEnabled, data]);

  const handleExport = async () => {
    try {
      console.log('🔄 Exporting analytics data...', exportOptions);
      
      // Simulate export process
      const exportData = {
        format: exportOptions.format,
        dateRange: exportOptions.dateRange,
        data: data,
        timestamp: new Date().toISOString(),
        user: user?.email
      };

      if (exportOptions.format === 'csv') {
        // Convert to CSV format
        const csvContent = convertToCSV(exportData);
        downloadFile(csvContent, `analytics-${Date.now()}.csv`, 'text/csv');
      } else if (exportOptions.format === 'excel') {
        // Simulate Excel export
        console.log('📊 Excel export would be generated here');
        alert('Excel export feature coming soon!');
      } else {
        // PDF export
        console.log('📄 PDF export would be generated here');
        alert('PDF export feature coming soon!');
      }
    } catch (error) {
      console.error('Export failed:', error);
      alert('Export failed. Please try again.');
    }
  };

  const convertToCSV = (data: any): string => {
    // Simple CSV conversion for demo
    const headers = ['Metric', 'Value', 'Date'];
    const rows = [
      ['AI Model Usage', JSON.stringify(data.data?.aiModelUsage), new Date().toISOString()],
      ['Performance Trends', JSON.stringify(data.data?.performanceTrends), new Date().toISOString()]
    ];
    
    return [headers, ...rows].map(row => row.join(',')).join('\n');
  };

  const downloadFile = (content: string, filename: string, contentType: string) => {
    const blob = new Blob([content], { type: contentType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="p-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="h-64 bg-gray-200 rounded"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="p-8 text-center">
        <p className="text-gray-600">No analytics data available</p>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header with Controls */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h1 className="text-3xl font-light mb-2">Advanced Analytics</h1>
          <p className="text-gray-600">AI-powered insights and performance metrics</p>
        </div>
        
        <div className="flex flex-wrap gap-3">
          {/* Time Range Selector */}
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="week">Last Week</option>
            <option value="month">Last Month</option>
            <option value="quarter">Last Quarter</option>
            <option value="year">Last Year</option>
          </select>

          {/* Real-time Toggle */}
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={realTimeEnabled}
              onChange={(e) => setRealTimeEnabled(e.target.checked)}
              className="rounded"
            />
            <span className="text-sm">Real-time</span>
            {realTimeEnabled && <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>}
          </label>

          {/* Export Button */}
          <Button onClick={handleExport} variant="outline" className="px-4 py-2">
            📊 Export
          </Button>
        </div>
      </div>

      {/* View Selector */}
      <div className="flex gap-2 border-b border-gray-200">
        {[
          { key: 'overview', label: '📊 Overview' },
          { key: 'performance', label: '📈 Performance' },
          { key: 'ai-insights', label: '🤖 AI Insights' },
          { key: 'predictions', label: '🔮 Predictions' }
        ].map(({ key, label }) => (
          <button
            key={key}
            onClick={() => setSelectedView(key as any)}
            className={`px-4 py-2 font-medium border-b-2 transition-colors ${
              selectedView === key
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-600 hover:text-gray-900'
            }`}
          >
            {label}
          </button>
        ))}
      </div>

      {/* Real-time Metrics Bar */}
      {realTimeEnabled && (
        <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-4">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{data.realTimeMetrics.activeAssessments}</div>
              <div className="text-sm text-gray-600">Active Assessments</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{data.realTimeMetrics.queueLength}</div>
              <div className="text-sm text-gray-600">Queue Length</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{data.realTimeMetrics.avgProcessingTime}s</div>
              <div className="text-sm text-gray-600">Avg Processing</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{data.realTimeMetrics.successRate.toFixed(1)}%</div>
              <div className="text-sm text-gray-600">Success Rate</div>
            </div>
          </div>
        </div>
      )}

      {/* Content based on selected view */}
      {selectedView === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* AI Model Usage */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium mb-4">AI Model Usage & Cost</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={data.aiModelUsage}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="count"
                    label={({ model, count }) => `${model}: ${count}`}
                  >
                    {data.aiModelUsage.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value: any, name: any, props: any) => [
                    `${value} assessments ($${props.payload.cost})`,
                    props.payload.model
                  ]} />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Performance Trends */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium mb-4">Performance Trends</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={data.performanceTrends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="avgScore" stroke="#10B981" strokeWidth={2} name="Avg Score" />
                  <Line type="monotone" dataKey="submissions" stroke="#3B82F6" strokeWidth={2} name="Submissions" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      )}

      {/* Export Options Modal would go here */}
    </div>
  );
}
