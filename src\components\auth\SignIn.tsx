import { useState } from 'react';
import { useSignInEmailPassword } from '@nhost/nextjs';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Button from '@/components/ui/Button';

export function SignIn() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const router = useRouter();

  const { signInEmailPassword, isLoading, isSuccess, needsEmailVerification, isError, error } =
    useSignInEmailPassword();

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    await signInEmailPassword(email, password);
  };

  if (isSuccess) {
    router.push('/dashboard');
    return null;
  }

  return (
    <div className="w-full">
      <form className="space-y-6" onSubmit={handleSubmit}>
        <div>
          <label htmlFor="email" className="block text-sm font-medium mb-2">
            Email address
          </label>
          <input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="block w-full rounded-md border border-gray-300 py-2 px-3 bg-white shadow-sm focus:ring-2 focus:ring-accent focus:border-accent"
          />
        </div>

        <div>
          <div className="flex items-center justify-between mb-2">
            <label htmlFor="password" className="block text-sm font-medium">
              Password
            </label>
            <Link href="/auth/reset-password" className="text-sm text-accent hover:text-accent/80">
              Forgot password?
            </Link>
          </div>
          <input
            id="password"
            name="password"
            type="password"
            autoComplete="current-password"
            required
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="block w-full rounded-md border border-gray-300 py-2 px-3 bg-white shadow-sm focus:ring-2 focus:ring-accent focus:border-accent"
          />
        </div>

        {isError && (
          <div className="text-red-500 text-sm">
            {error?.message || 'Error signing in'}
          </div>
        )}

        {needsEmailVerification && (
          <div className="text-amber-500 text-sm">
            Please check your email for a verification link
          </div>
        )}

        <div className="pt-2">
          <Button
            type="submit"
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? 'Signing in...' : 'Sign in'}
          </Button>
        </div>

        <div className="text-center text-sm mt-6">
          Don&apos;t have an account?{' '}
          <Link href="/auth/sign-up" className="text-accent hover:text-accent/80">
            Sign up
          </Link>
        </div>
      </form>
    </div>
  );
}
