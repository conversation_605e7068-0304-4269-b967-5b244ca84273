# ESS - Educational Support System

This is a [Next.js](https://nextjs.org) project integrated with [Nhost](https://nhost.io) for authentication, database, and storage.

## Getting Started

### 1. Set up Nhost

1. Create an account on [Nhost](https://app.nhost.io) if you don't have one
2. Create a new Nhost project
3. Copy your project's subdomain and region from the Nhost dashboard

### 2. Configure Environment Variables

Create or update your `.env.local` file with your Nhost project details:

```bash
NEXT_PUBLIC_NHOST_SUBDOMAIN=your-subdomain
NEXT_PUBLIC_NHOST_REGION=your-region
```

### 3. Install Dependencies and Run the Development Server

```bash
npm install
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Features

- **Authentication**: Sign up, sign in, and sign out functionality using Nhost
- **Protected Routes**: Dashboard page that requires authentication
- **User Profile**: Display user information from Nhost

## Project Structure

- `src/lib/nhost.ts` - Nhost client configuration
- `src/components/auth/` - Authentication components (SignIn, SignUp)
- `src/app/auth/` - Authentication pages
- `src/app/dashboard/` - Protected dashboard page

## Learn More

To learn more about the technologies used in this project:

- [Next.js Documentation](https://nextjs.org/docs)
- [Nhost Documentation](https://docs.nhost.io)
- [Nhost Next.js SDK](https://docs.nhost.io/reference/nextjs)
