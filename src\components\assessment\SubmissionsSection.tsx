import { DocumentIcon, ArrowDownTrayIcon, TrashIcon } from '@heroicons/react/24/outline';

interface Submission {
  id: string;
  fileName: string;
  fileSize: number; // in KB
  fileType: string;
  uploadedAt: Date;
}

interface SubmissionsSectionProps {
  submissions: Submission[];
  onUpdate: (submissions: Submission[]) => void;
}

export default function SubmissionsSection({ submissions, onUpdate }: SubmissionsSectionProps) {
  const formatFileSize = (sizeInKB: number): string => {
    if (sizeInKB < 1024) {
      return `${sizeInKB} KB`;
    }
    return `${(sizeInKB / 1024).toFixed(1)} MB`;
  };

  const getFileIcon = (fileType: string) => {
    switch (fileType.toLowerCase()) {
      case 'pdf':
        return (
          <div className="w-8 h-8 bg-red-100 rounded flex items-center justify-center">
            <DocumentIcon className="w-5 h-5 text-red-600" />
          </div>
        );
      case 'docx':
      case 'doc':
        return (
          <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
            <DocumentIcon className="w-5 h-5 text-blue-600" />
          </div>
        );
      default:
        return (
          <div className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center">
            <DocumentIcon className="w-5 h-5 text-gray-600" />
          </div>
        );
    }
  };

  const handleDownload = (submission: Submission) => {
    // TODO: Implement actual download functionality
    console.log('Downloading file:', submission.fileName);
    alert(`Downloading ${submission.fileName}`);
  };

  const handleRemove = (submissionId: string) => {
    if (confirm('Are you sure you want to remove this file?')) {
      const updatedSubmissions = submissions.filter(s => s.id !== submissionId);
      onUpdate(updatedSubmissions);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 className="text-xl font-semibold text-text mb-4">Submissions</h2>
      
      <div className="mb-4">
        <div className="text-sm font-medium text-gray-700">
          Uploaded Files
        </div>
      </div>

      <div className="space-y-3">
        {submissions.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No files uploaded yet
          </div>
        ) : (
          submissions.map((submission) => (
            <div
              key={submission.id}
              className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                {getFileIcon(submission.fileType)}
                <div>
                  <div className="text-sm font-medium text-gray-900">
                    {submission.fileName}
                  </div>
                  <div className="text-xs text-gray-500">
                    {formatFileSize(submission.fileSize)} • {submission.fileType.toUpperCase()}
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleDownload(submission)}
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                  title="Download file"
                >
                  <ArrowDownTrayIcon className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleRemove(submission.id)}
                  className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                  title="Remove file"
                >
                  <TrashIcon className="w-4 h-4" />
                </button>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Add More Files Button */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <button
          onClick={() => {
            // TODO: Implement add more files functionality
            console.log('Add more files clicked');
          }}
          className="text-sm text-accent hover:text-[#9A7649] font-medium transition-colors"
        >
          + Add More Files
        </button>
      </div>
    </div>
  );
}
