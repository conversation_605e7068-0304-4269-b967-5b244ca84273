import { useState } from 'react';
import { useSignUpEmailPassword } from '@nhost/nextjs';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Button from '@/components/ui/Button';
import Select from '@/components/ui/Select';

export function SignUp() {
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [role, setRole] = useState('');
  const [institutionType, setInstitutionType] = useState('');
  const [password, setPassword] = useState('');

  const router = useRouter();

  const { signUpEmailPassword, isLoading, isSuccess, needsEmailVerification, isError, error } =
    useSignUpEmailPassword();

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    await signUpEmailPassword(email, password, {
      displayName: fullName,
      metadata: {
        fullName,
        role,
        institutionType
      }
    });
  };

  if (isSuccess) {
    router.push('/dashboard');
    return null;
  }

  const roleOptions = [
    { value: 'teacher', label: 'Teacher' },
    { value: 'administrator', label: 'Administrator' }
  ];

  const institutionOptions = [
    { value: 'school', label: 'School' },
    { value: 'college', label: 'College' },
    { value: 'university', label: 'University' },
    { value: 'professional', label: 'Professional Training Establishment' }
  ];

  return (
    <div className="w-full">
      <div className="bg-accent text-white p-6 rounded-t-md">
        <h2 className="text-2xl font-normal text-center mb-2">Rubriq</h2>
        <p className="text-center">Sign in to your account</p>
      </div>

      <form className="space-y-6 p-6" onSubmit={handleSubmit}>
        <div>
          <label htmlFor="fullName" className="block text-sm font-medium mb-2">
            Full Name <span className="text-red-500">*</span>
          </label>
          <input
            id="fullName"
            name="fullName"
            type="text"
            required
            value={fullName}
            onChange={(e) => setFullName(e.target.value)}
            className="block w-full rounded-md border border-gray-300 py-2 px-3 bg-white shadow-sm focus:ring-2 focus:ring-accent focus:border-accent"
          />
        </div>

        <div>
          <label htmlFor="email" className="block text-sm font-medium mb-2">
            Email Address <span className="text-red-500">*</span>
          </label>
          <input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="block w-full rounded-md border border-gray-300 py-2 px-3 bg-white shadow-sm focus:ring-2 focus:ring-accent focus:border-accent"
          />
          <p className="text-xs text-gray-500 mt-1">We'll send a verification link to this email</p>
        </div>

        <div>
          <label htmlFor="role" className="block text-sm font-medium mb-2">
            Role <span className="text-red-500">*</span>
          </label>
          <Select
            id="role"
            name="role"
            options={roleOptions}
            value={role}
            onChange={(e) => setRole(e.target.value)}
            placeholder="Select your role"
            required
          />
        </div>

        <div>
          <label htmlFor="institutionType" className="block text-sm font-medium mb-2">
            Institution Type <span className="text-red-500">*</span>
          </label>
          <Select
            id="institutionType"
            name="institutionType"
            options={institutionOptions}
            value={institutionType}
            onChange={(e) => setInstitutionType(e.target.value)}
            placeholder="Select your institution"
            required
          />
        </div>

        <div>
          <label htmlFor="password" className="block text-sm font-medium mb-2">
            Password <span className="text-red-500">*</span>
          </label>
          <input
            id="password"
            name="password"
            type="password"
            autoComplete="new-password"
            required
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="block w-full rounded-md border border-gray-300 py-2 px-3 bg-white shadow-sm focus:ring-2 focus:ring-accent focus:border-accent"
          />
        </div>

        {isError && (
          <div className="text-red-500 text-sm">
            {error?.message || 'Error signing up'}
          </div>
        )}

        {needsEmailVerification && (
          <div className="text-amber-500 text-sm">
            Please check your email for a verification link
          </div>
        )}

        <div className="pt-2">
          <Button
            type="submit"
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? 'Creating account...' : 'Continue'}
          </Button>
        </div>

        <div className="text-center text-sm mt-6">
          By signing in, you agree to Rubriq's <Link href="/terms" className="text-accent hover:text-accent/80">Terms of Service</Link> and <Link href="/privacy" className="text-accent hover:text-accent/80">Privacy Policy</Link>
        </div>
      </form>
    </div>
  );
}
