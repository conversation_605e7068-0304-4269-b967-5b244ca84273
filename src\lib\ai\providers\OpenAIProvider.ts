import OpenAI from 'openai';
import {
  AssessmentConfig,
  AssessmentResult,
  Submission,
  AIProviderResponse,
  TokenUsage,
  AIAssessmentError,
  RateLimitError,
  QuotaExceededError,
  InvalidResponseError
} from '@/types/ai-assessment';
import { getAIConfig, calculateCost } from '../config';

export class OpenAIProvider {
  private client: OpenAI;
  private config: ReturnType<typeof getAIConfig>['openai'];

  constructor() {
    const aiConfig = getAIConfig();
    this.config = aiConfig.openai;

    this.client = new OpenAI({
      apiKey: this.config.apiKey,
      organization: this.config.organization,
    });
  }

  async assessSubmission(
    submission: Submission,
    config: AssessmentConfig
  ): Promise<AIProviderResponse> {
    const startTime = Date.now();

    try {
      console.log(`🤖 Starting OpenAI assessment for submission ${submission.id}`);

      const prompt = this.buildPrompt(submission, config);
      const systemPrompt = this.getSystemPrompt(config);

      const response = await this.client.chat.completions.create({
        model: this.config.defaultModel,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: prompt }
        ],
        temperature: this.config.temperature,
        max_tokens: this.config.maxTokens,
        response_format: { type: 'json_object' },
      });

      const processingTime = Date.now() - startTime;

      // Extract token usage
      const usage = response.usage;
      const tokenUsage: TokenUsage = {
        inputTokens: usage?.prompt_tokens || 0,
        outputTokens: usage?.completion_tokens || 0,
        totalTokens: usage?.total_tokens || 0,
        estimatedCost: calculateCost(
          'openai',
          this.config.defaultModel,
          usage?.prompt_tokens || 0,
          usage?.completion_tokens || 0
        ),
      };

      // Parse the response
      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new InvalidResponseError('openai', response);
      }

      const assessmentData = JSON.parse(content);

      // Debug the criteria scores returned by AI
      if (assessmentData.criteria_scores) {
        console.log(`🎯 AI returned ${assessmentData.criteria_scores.length} criteria scores:`);
        assessmentData.criteria_scores.forEach((score: any, index: number) => {
          console.log(`  ${index + 1}. "${score.criteria_name}" (ID: ${score.criteria_id}) - Score: ${score.score}`);
        });
      } else {
        console.warn('⚠️ No criteria_scores found in AI response');
        console.log('📄 Available response keys:', Object.keys(assessmentData));
      }

      const result = this.parseAssessmentResponse(
        assessmentData,
        submission,
        config,
        processingTime,
        tokenUsage
      );

      console.log(`✅ OpenAI assessment completed in ${processingTime}ms`);
      console.log(`💰 Cost: $${tokenUsage.estimatedCost.toFixed(4)}`);

      return {
        success: true,
        result,
        tokenUsage,
      };

    } catch (error: any) {
      console.error('❌ OpenAI assessment failed:', error);
      return this.handleError(error);
    }
  }

  private buildPrompt(submission: Submission, config: AssessmentConfig): string {
    // DEBUG: Log assignment context being sent to AI
    console.log(`🎯 AI PROMPT ASSIGNMENT CONTEXT:`);
    console.log(`📝 Assignment Title: "${submission.assignment.title}"`);
    console.log(`📄 Assignment Description: "${submission.assignment.description}"`);
    console.log(`🆔 Assignment ID: ${submission.assignment.id}`);

    const criteriaText = config.criteria
      .map(c => `- ID: ${c.id} | ${c.name} (${c.weight}%): ${c.description}`)
      .join('\n');

    const featuresText = Object.entries(config.features)
      .filter(([_, enabled]) => enabled)
      .map(([feature, _]) => feature.replace(/([A-Z])/g, ' $1').toLowerCase())
      .join(', ');

    return `
You are an expert educator tasked with assessing student work. Please provide a comprehensive assessment based on the following criteria.

ASSIGNMENT CONTEXT:
- Assignment: ${submission.assignment.title}
- Description: ${submission.assignment.description}

SUBMISSION DETAILS:
- Student: ${submission.studentName}
- File: ${submission.fileName}
- Type: ${submission.fileType}

ASSESSMENT CRITERIA:
${criteriaText}

SUBMISSION CONTENT:
${submission.content}

ASSESSMENT REQUIREMENTS:
1. Provide an overall grade using ${config.assessmentType} scale
2. Score each criterion individually (0-100)
3. Give specific, constructive feedback for each criterion
4. Highlight overall strengths and areas for improvement
5. Suggest specific next steps for improvement
6. Provide a confidence score (0-1) for your assessment

${featuresText ? `ADDITIONAL ANALYSIS: Please also check for ${featuresText}.` : ''}

FEEDBACK LEVEL: ${config.feedbackLevel}
${config.feedbackLevel === 'detailed' ? 'Provide extensive, specific feedback with examples.' : ''}
${config.feedbackLevel === 'brief' ? 'Provide concise, focused feedback.' : ''}

FORMAT YOUR RESPONSE AS JSON:
{
  "overall_grade": "A",
  "overall_score": 85,
  "criteria_scores": [
    {
      "criteria_id": "USE_THE_EXACT_ID_FROM_CRITERIA_LIST_ABOVE",
      "criteria_name": "USE_THE_EXACT_NAME_FROM_CRITERIA_LIST_ABOVE",
      "score": 85,
      "max_score": 100,
      "feedback": "Detailed feedback for this specific criterion...",
      "strengths": ["Specific strength 1", "Specific strength 2"],
      "improvements": ["Specific improvement 1", "Specific improvement 2"]
    }
  ],
  "strengths": ["Overall strength 1", "Overall strength 2"],
  "areas_for_improvement": ["Overall improvement area 1", "Overall improvement area 2"],
  "suggestions": ["Specific suggestion 1", "Specific suggestion 2"],
  "feedback_summary": "Overall assessment summary...",
  "confidence": 0.85
}

IMPORTANT: For each criterion in the criteria_scores array, you MUST use the exact ID and name from the ASSESSMENT CRITERIA list above. Do not make up new IDs or names.`;
  }

  private getSystemPrompt(config: AssessmentConfig): string {
    return `You are an expert educational assessor with deep knowledge across multiple subjects. Your role is to provide fair, constructive, and detailed assessments of student work.

Key principles:
- Be objective and consistent in your grading
- Provide specific, actionable feedback
- Highlight both strengths and areas for improvement
- Use encouraging language while being honest about areas needing work
- Consider the assessment type: ${config.assessmentType}
- Maintain high academic standards while being supportive

Always respond with valid JSON format as specified in the user prompt.`;
  }

  private parseAssessmentResponse(
    data: any,
    submission: Submission,
    config: AssessmentConfig,
    processingTime: number,
    tokenUsage: TokenUsage
  ): AssessmentResult {
    // Transform AI criteria_scores to match CriteriaFeedback interface
    const detailedFeedback = (data.criteria_scores || []).map((criteriaScore: any) => ({
      criteriaId: criteriaScore.criteria_id || criteriaScore.criteriaId || '',
      criteriaName: criteriaScore.criteria_name || criteriaScore.criteriaName || '',
      score: criteriaScore.score || 0,
      maxScore: criteriaScore.max_score || criteriaScore.maxScore || 100,
      feedback: criteriaScore.feedback || '',
      strengths: criteriaScore.strengths || [],
      improvements: criteriaScore.improvements || []
    }));

    console.log('🔄 Transformed AI criteria scores:');
    detailedFeedback.forEach((feedback: any, index: number) => {
      console.log(`  ${index + 1}. ID: "${feedback.criteriaId}", Name: "${feedback.criteriaName}", Score: ${feedback.score}`);
    });

    return {
      id: `assessment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      submissionId: submission.id,
      assignmentId: submission.assignment.id,
      overallGrade: data.overall_grade || 'N/A',
      overallScore: data.overall_score || 0,
      feedbackSummary: data.feedback_summary || '',
      detailedFeedback: detailedFeedback,
      strengths: data.strengths || [],
      areasForImprovement: data.areas_for_improvement || [],
      suggestions: data.suggestions || [],
      confidenceScore: data.confidence || 0,
      processingTimeMs: processingTime,
      tokenUsage,
      aiProvider: 'openai',
      modelUsed: this.config.defaultModel,
      createdAt: new Date(),
    };
  }

  private handleError(error: any): AIProviderResponse {
    if (error.status === 429) {
      const retryAfter = error.headers?.['retry-after'];
      throw new RateLimitError('openai', retryAfter ? parseInt(retryAfter) : undefined);
    }

    if (error.status === 402 || error.code === 'insufficient_quota') {
      throw new QuotaExceededError('openai');
    }

    if (error instanceof SyntaxError) {
      throw new InvalidResponseError('openai', error.message);
    }

    throw new AIAssessmentError(
      error.message || 'Unknown OpenAI error',
      'openai',
      error.status,
      [500, 502, 503, 504].includes(error.status)
    );
  }
}
