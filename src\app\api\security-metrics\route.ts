import { NextRequest, NextResponse } from 'next/server';
import { withSecurity, securityConfigs } from '@/lib/middleware/security';
import { withRateLimit } from '@/lib/middleware/rateLimiter';
import { getSecurityMetrics } from '@/lib/security/monitoring';

// Security metrics handler
const securityMetricsHandler = async (request: NextRequest) => {
  try {
    // Get security metrics from monitoring system
    const metrics = getSecurityMetrics();

    return NextResponse.json(metrics);

  } catch (error) {
    console.error('Failed to fetch security metrics:', error);
    
    return NextResponse.json(
      { error: 'Failed to fetch security metrics' },
      { status: 500 }
    );
  }
};

// Apply security middleware and rate limiting
export const GET = withSecurity(
  withRateLimit(securityMetricsHandler, 'general'),
  {
    ...securityConfigs.general,
    allowedMethods: ['GET', 'OPTIONS'],
    requireAuth: true // Only authenticated users can view security metrics
  }
);
