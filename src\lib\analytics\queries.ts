import { gql } from '@apollo/client';

// Get user's assignments with submission and assessment counts
export const GET_USER_ANALYTICS_OVERVIEW = gql`
  query GetUserAnalyticsOverview($userId: uuid!) {
    assignments(where: { user_id: { _eq: $userId } }) {
      id
      title
      description
      created_at
      student_submissions {
        id
        student_name
        upload_date
        processing_status
        assessment_results {
          id
          overall_grade
          created_at
        }
      }
    }
  }
`;

// Get detailed assessment results for analytics
export const GET_USER_ASSESSMENT_RESULTS = gql`
  query GetUserAssessmentResults($userId: uuid!, $timeRange: timestamptz) {
    assessment_results(
      where: {
        student_submission: {
          assignment: { user_id: { _eq: $userId } }
        }
        created_at: { _gte: $timeRange }
      }
      order_by: { created_at: desc }
    ) {
      id
      overall_grade
      general_comments
      created_at
      student_submission {
        id
        student_name
        upload_date
        assignment {
          id
          title
          description
          created_at
        }
      }
      per_criterion_feedbacks {
        id
        score
        feedback_text
        assessment_criterium {
          criterion_name
        }
      }
    }
  }
`;

// Get submission trends over time
export const GET_SUBMISSION_TRENDS = gql`
  query GetSubmissionTrends($userId: uuid!, $timeRange: timestamptz) {
    student_submissions(
      where: {
        assignment: { user_id: { _eq: $userId } }
        upload_date: { _gte: $timeRange }
      }
      order_by: { upload_date: asc }
    ) {
      id
      upload_date
      processing_status
      assignment {
        id
        title
      }
      assessment_results {
        id
        overall_grade
        created_at
      }
    }
  }
`;

// Get processing efficiency metrics
export const GET_PROCESSING_EFFICIENCY = gql`
  query GetProcessingEfficiency($userId: uuid!) {
    assignments(where: { user_id: { _eq: $userId } }) {
      id
      title
      created_at
      student_submissions {
        id
        upload_date
        processing_status
        assessment_results {
          id
          created_at
        }
      }
    }
  }
`;

// Get recent activity feed
export const GET_RECENT_ACTIVITY = gql`
  query GetRecentActivity($userId: uuid!, $limit: Int = 10) {
    assessment_results(
      where: {
        student_submission: {
          assignment: { user_id: { _eq: $userId } }
        }
      }
      order_by: { created_at: desc }
      limit: $limit
    ) {
      id
      overall_grade
      created_at
      student_submission {
        id
        student_name
        upload_date
        assignment {
          id
          title
          description
        }
      }
    }

    student_submissions(
      where: {
        assignment: { user_id: { _eq: $userId } }
        assessment_results: { id: { _is_null: true } }
      }
      order_by: { upload_date: desc }
      limit: $limit
    ) {
      id
      student_name
      upload_date
      processing_status
      assignment {
        id
        title
      }
    }
  }
`;

// Get grade distribution
export const GET_GRADE_DISTRIBUTION = gql`
  query GetGradeDistribution($userId: uuid!, $timeRange: timestamptz) {
    assessment_results(
      where: {
        student_submission: {
          assignment: { user_id: { _eq: $userId } }
        }
        created_at: { _gte: $timeRange }
        overall_grade: { _is_null: false }
      }
    ) {
      overall_grade
    }
  }
`;
