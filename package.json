{"name": "ess", "version": "0.1.0", "private": true, "engines": {"node": ">=18.17.0", "npm": ">=9.0.0"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "codegen": "graphql-codegen", "codegen:watch": "graphql-codegen --watch"}, "dependencies": {"@anthropic-ai/sdk": "^0.24.3", "@apollo/client": "^3.13.8", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@nhost/nextjs": "^2.2.7", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.9", "@types/papaparse": "^5.3.16", "autoprefixer": "^10.4.21", "date-fns": "^4.1.0", "express": "^4.18.2", "graphql": "^16.11.0", "isomorphic-dompurify": "^2.25.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "next": "^14.2.29", "openai": "^4.52.7", "papaparse": "^5.5.3", "postcss": "^8.5.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.4", "recharts": "^2.15.3", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@graphql-codegen/cli": "^5.0.6", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.1", "@graphql-codegen/typescript-react-apollo": "^4.3.2", "@netlify/plugin-nextjs": "^5.11.3", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "dotenv": "^16.5.0", "eslint": "^8.56.0", "eslint-config-next": "^14.2.29", "postcss-loader": "^8.1.1", "tailwindcss": "^3.3.3", "typescript": "^5"}}