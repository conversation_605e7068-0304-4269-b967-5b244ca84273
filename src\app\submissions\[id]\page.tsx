'use client';

import { useEffect } from 'react';
import { useAuthenticationStatus } from '@nhost/nextjs';
import { useRouter } from 'next/navigation';
import { useQuery, gql } from '@apollo/client';
import DashboardLayout from '@/components/layout/DashboardLayout';
import Link from 'next/link';
import { formatDate, getStatusColor } from '@/lib/utils';
import Button from '@/components/ui/Button';
import FileDownload from '@/components/submissions/FileDownload';

const GET_SUBMISSION_DETAILS = gql`
  query GetSubmissionDetails($id: uuid!) {
    student_submissions_by_pk(id: $id) {
      id
      status
      submitted_at
      assignment {
        id
        title
        description
      }
      submission_files {
        id
        file_id
        file_name
        file_type
      }
    }
  }
`;

export default function SubmissionDetailsPage({ params }: { params: { id: string } }) {
  const { isAuthenticated, isLoading } = useAuthenticationStatus();
  const router = useRouter();
  const submissionId = params.id;

  const { data, loading: submissionLoading } = useQuery(GET_SUBMISSION_DETAILS, {
    variables: { id: submissionId },
    skip: !isAuthenticated,
  });

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/sign-in');
    }
  }, [isLoading, isAuthenticated, router]);

  if (isLoading || submissionLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[50vh]">
          <p>Loading...</p>
        </div>
      </DashboardLayout>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect in the useEffect
  }

  const submission = data?.student_submissions_by_pk;

  if (!submission) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h1 className="text-2xl font-light mb-4">Submission Not Found</h1>
          <p className="text-gray-600 mb-6">The submission you're looking for doesn't exist or you don't have access to it.</p>
          <Link href="/submissions" className="text-accent hover:text-accent/80">
            Back to Submissions
          </Link>
        </div>
      </DashboardLayout>
    );
  }



  return (
    <DashboardLayout>
      <div className="mb-8">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-light">Submission Details</h1>
          <Link href="/submissions">
            <Button variant="outline">Back to Submissions</Button>
          </Link>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h2 className="text-2xl font-medium">{submission.assignment.title}</h2>
            <p className="text-gray-600 mt-1">
              Submitted: {formatDate(submission.submitted_at)}
            </p>
          </div>
          <span className={`px-3 py-1 inline-flex text-sm font-semibold rounded-full ${getStatusColor(submission.status)}`}>
            {submission.status.charAt(0).toUpperCase() + submission.status.slice(1)}
          </span>
        </div>

        {submission.assignment.description && (
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-2">Assignment Description</h3>
            <div className="bg-gray-50 p-4 rounded-md">
              <p className="text-gray-700 whitespace-pre-line">{submission.assignment.description}</p>
            </div>
          </div>
        )}

        <div>
          <h3 className="text-lg font-medium mb-3">Submitted Files</h3>
          {submission.submission_files.length === 0 ? (
            <p className="text-gray-500 italic">No files submitted</p>
          ) : (
            <ul className="divide-y divide-gray-200 border border-gray-200 rounded-md">
              {submission.submission_files.map((file: { id: string; file_id: string; file_name: string; file_type: string }) => (
                <li key={file.id}>
                  <FileDownload
                    fileId={file.file_id}
                    fileName={file.file_name}
                    fileType={file.file_type}
                  />
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
