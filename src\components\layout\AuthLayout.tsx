import Link from 'next/link';
import React from 'react';

interface AuthLayoutProps {
  children: React.ReactNode;
  title: string;
}

export default function AuthLayout({ children, title }: AuthLayoutProps) {
  return (
    <div className="min-h-screen flex">
      {/* Left side - Form */}
      <div className="w-full lg:w-1/2 flex flex-col justify-center p-8 md:p-12">
        <div className="mb-8">
          <Link href="/" className="text-2xl font-light">
            GRADEFLOW
          </Link>
        </div>
        
        <div className="max-w-md mx-auto w-full">
          <h1 className="text-3xl font-light mb-8">{title}</h1>
          {children}
        </div>
      </div>
      
      {/* Right side - Background */}
      <div className="hidden lg:block lg:w-1/2 relative">
        <div 
          className="absolute inset-0"
          style={{
            background: 'linear-gradient(135deg, rgba(163, 189, 184, 0.4) 0%, rgba(179, 139, 93, 0.1) 100%)',
          }}
        >
          <div className="absolute inset-0 flex flex-col items-center justify-center p-12 text-center">
            <h2 className="text-4xl font-light mb-6 text-text">
              Welcome to Rubriq
            </h2>
            <p className="text-lg max-w-md text-text/80">
              The AI-powered grading and assessment platform that helps educators save time and provide consistent feedback.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
