/**
 * Authentication Middleware for API Routes
 * Provides JWT validation and user context extraction
 */

import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

export interface AuthenticatedUser {
  id: string;
  email: string;
  role: string;
  allowedRoles: string[];
}

export interface AuthenticatedRequest extends NextRequest {
  user?: AuthenticatedUser;
}

// JWT verification configuration
const JWT_SECRET = process.env.NHOST_JWT_SECRET;
const NHOST_SUBDOMAIN = process.env.NEXT_PUBLIC_NHOST_SUBDOMAIN;
const NHOST_REGION = process.env.NEXT_PUBLIC_NHOST_REGION;

/**
 * Extract and validate JW<PERSON> token from request
 */
function extractToken(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  
  if (!authHeader) {
    return null;
  }

  // Check for Bearer token format
  const bearerMatch = authHeader.match(/^Bearer\s+(.+)$/);
  if (!bearerMatch) {
    return null;
  }

  return bearerMatch[1];
}

/**
 * Validate JWT token and extract user information
 */
async function validateToken(token: string): Promise<AuthenticatedUser | null> {
  try {
    // For Nhost, we need to verify the token with their public key
    // In development, we can decode without verification for testing
    let payload: any;

    if (process.env.NODE_ENV === 'development' && !JWT_SECRET) {
      // Development mode: decode without verification (NOT for production)
      const tokenParts = token.split('.');
      if (tokenParts.length !== 3) {
        return null;
      }
      payload = JSON.parse(atob(tokenParts[1]));
    } else {
      // Production mode: proper JWT verification
      if (!JWT_SECRET) {
        console.error('JWT_SECRET not configured for production');
        return null;
      }
      payload = jwt.verify(token, JWT_SECRET) as any;
    }

    // Extract Hasura claims
    const hasuraClaims = payload['https://hasura.io/jwt/claims'];
    if (!hasuraClaims) {
      console.error('No Hasura claims found in JWT token');
      return null;
    }

    const userId = hasuraClaims['x-hasura-user-id'];
    const defaultRole = hasuraClaims['x-hasura-default-role'];
    const allowedRoles = hasuraClaims['x-hasura-allowed-roles'];

    if (!userId || !defaultRole) {
      console.error('Missing required claims in JWT token');
      return null;
    }

    // Extract user email from standard claims
    const email = payload.email || payload.sub || '<EMAIL>';

    return {
      id: userId,
      email: email,
      role: defaultRole,
      allowedRoles: Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles]
    };

  } catch (error) {
    console.error('JWT validation error:', error);
    return null;
  }
}

/**
 * Authentication middleware for API routes
 */
export function withAuth(
  handler: (request: AuthenticatedRequest) => Promise<NextResponse>,
  options: {
    required?: boolean;
    roles?: string[];
  } = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const { required = true, roles = [] } = options;

    try {
      // Extract token from request
      const token = extractToken(request);

      if (!token) {
        if (required) {
          return NextResponse.json(
            { error: 'Authentication required', code: 'AUTH_REQUIRED' },
            { status: 401 }
          );
        }
        // Continue without authentication if not required
        return await handler(request as AuthenticatedRequest);
      }

      // Validate token and extract user
      const user = await validateToken(token);

      if (!user) {
        return NextResponse.json(
          { error: 'Invalid or expired token', code: 'INVALID_TOKEN' },
          { status: 401 }
        );
      }

      // Check role-based access if roles are specified
      if (roles.length > 0) {
        const hasRequiredRole = roles.some(role => 
          user.allowedRoles.includes(role) || user.role === role
        );

        if (!hasRequiredRole) {
          return NextResponse.json(
            { 
              error: 'Insufficient permissions', 
              code: 'INSUFFICIENT_PERMISSIONS',
              required: roles,
              current: user.role
            },
            { status: 403 }
          );
        }
      }

      // Add user to request object
      (request as AuthenticatedRequest).user = user;

      // Call the handler with authenticated request
      return await handler(request as AuthenticatedRequest);

    } catch (error) {
      console.error('Authentication middleware error:', error);
      
      return NextResponse.json(
        { error: 'Authentication error', code: 'AUTH_ERROR' },
        { status: 500 }
      );
    }
  };
}

/**
 * Helper to get user from authenticated request
 */
export function getAuthenticatedUser(request: AuthenticatedRequest): AuthenticatedUser | null {
  return request.user || null;
}

/**
 * Helper to check if user has specific role
 */
export function hasRole(user: AuthenticatedUser, role: string): boolean {
  return user.allowedRoles.includes(role) || user.role === role;
}

/**
 * Helper to check if user owns a resource
 */
export function isResourceOwner(user: AuthenticatedUser, resourceUserId: string): boolean {
  return user.id === resourceUserId;
}

/**
 * Middleware configuration for different endpoint types
 */
export const authConfigs = {
  // Requires authentication, any role
  authenticated: {
    required: true,
    roles: []
  },

  // Requires admin role
  admin: {
    required: true,
    roles: ['admin']
  },

  // Requires teacher role (or admin)
  teacher: {
    required: true,
    roles: ['teacher', 'admin']
  },

  // Optional authentication
  optional: {
    required: false,
    roles: []
  },

  // Public access (no authentication)
  public: {
    required: false,
    roles: []
  }
};

/**
 * Enhanced authentication middleware that combines with security middleware
 */
export function withAuthAndSecurity(
  handler: (request: AuthenticatedRequest) => Promise<NextResponse>,
  authConfig: typeof authConfigs[keyof typeof authConfigs],
  securityConfig?: any
) {
  // First apply authentication, then security
  const authHandler = withAuth(handler, authConfig);
  
  // If security middleware is available, apply it
  if (securityConfig && typeof securityConfig === 'function') {
    return securityConfig(authHandler);
  }
  
  return authHandler;
}
