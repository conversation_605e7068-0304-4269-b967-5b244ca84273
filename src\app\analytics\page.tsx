'use client';

import { useEffect, useState } from 'react';
import { useAuthenticationStatus, useUserData } from '@nhost/nextjs';
import { useRouter } from 'next/navigation';
import { subDays } from 'date-fns';
import DashboardLayout from '@/components/layout/DashboardLayout';
import KPICards from '@/components/analytics/KPICards';
import SimpleGradeChart from '@/components/analytics/SimpleGradeChart';
import SimpleTrendsChart from '@/components/analytics/SimpleTrendsChart';
import ProcessingEfficiencyChart from '@/components/analytics/ProcessingEfficiencyChart';
import RecentActivityFeed from '@/components/analytics/RecentActivityFeed';
// Removed GraphQL imports - using API routes instead
import {
  calculateKPIs,
  calculateGradeDistribution,
  calculateAssessmentTrends,
  calculateProcessingEfficiency,
  calculateRecentActivity
} from '@/lib/analytics/calculations';
import { AnalyticsTimeRange } from '@/lib/analytics/types';

const TIME_RANGES: AnalyticsTimeRange[] = [
  { label: 'Last 7 days', value: 'week', days: 7 },
  { label: 'Last 30 days', value: 'month', days: 30 },
  { label: 'Last 90 days', value: 'quarter', days: 90 },
  { label: 'Last year', value: 'year', days: 365 },
  { label: 'All time', value: 'all', days: 9999 }
];

export default function AnalyticsPage() {
  const { isAuthenticated, isLoading: authLoading } = useAuthenticationStatus();
  const user = useUserData();
  const router = useRouter();
  const [selectedTimeRange, setSelectedTimeRange] = useState<AnalyticsTimeRange>(TIME_RANGES[1]); // Default to 30 days

  // State for API data with individual loading states
  const [overviewData, setOverviewData] = useState<any>(null);
  const [assessmentData, setAssessmentData] = useState<any>(null);
  const [trendsData, setTrendsData] = useState<any>(null);
  const [activityData, setActivityData] = useState<any>(null);

  // Individual loading states to prevent jittering
  const [overviewLoading, setOverviewLoading] = useState(true);
  const [assessmentLoading, setAssessmentLoading] = useState(true);
  const [trendsLoading, setTrendsLoading] = useState(true);
  const [activityLoading, setActivityLoading] = useState(true);

  const [error, setError] = useState<string | null>(null);

  // Global loading state for initial page load
  const loading = overviewLoading || assessmentLoading || trendsLoading || activityLoading;

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/sign-in');
    }
  }, [authLoading, isAuthenticated, router]);

  // Calculate time range for queries
  const timeRangeDate = selectedTimeRange.value === 'all'
    ? new Date('2020-01-01').toISOString()
    : subDays(new Date(), selectedTimeRange.days).toISOString();

  // Fetch analytics data using API routes
  useEffect(() => {
    if (!isAuthenticated || !user?.id) {
      // Set all individual loading states to false when not authenticated
      setOverviewLoading(false);
      setAssessmentLoading(false);
      setTrendsLoading(false);
      setActivityLoading(false);
      return;
    }

    const fetchAnalyticsData = async () => {
      try {
        setError(null);



        // Fetch data individually to prevent jittering
        // Start with fastest/simplest queries first

        // 1. Overview data (KPIs) - usually fastest
        try {
          const overviewRes = await fetch(`/api/analytics?user_id=${user.id}&type=overview`);
          const overview = await overviewRes.json();
          if (overview.success) setOverviewData(overview.data);
        } catch (err) {
          // Overview data failed
        } finally {
          setOverviewLoading(false);
        }

        // 2. Assessment data (charts) - medium complexity
        try {
          const assessmentRes = await fetch(`/api/analytics?user_id=${user.id}&type=assessments&timeRange=${timeRangeDate}`);
          const assessment = await assessmentRes.json();
          if (assessment.success) setAssessmentData(assessment.data);
        } catch (err) {
          console.error('❌ Assessment data failed:', err);
        } finally {
          setAssessmentLoading(false);
        }

        // 3. Trends data - medium complexity
        try {
          const trendsRes = await fetch(`/api/analytics?user_id=${user.id}&type=trends&timeRange=${timeRangeDate}`);
          const trends = await trendsRes.json();
          if (trends.success) setTrendsData(trends.data);
        } catch (err) {
          console.error('❌ Trends data failed:', err);
        } finally {
          setTrendsLoading(false);
        }

        // 4. Activity data - usually fastest
        try {
          const activityRes = await fetch(`/api/analytics?user_id=${user.id}&type=activity`);
          const activity = await activityRes.json();
          if (activity.success) setActivityData(activity.data);
        } catch (err) {
          // Activity data failed
        } finally {
          setActivityLoading(false);
        }

      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        // Set all loading states to false on global error
        setOverviewLoading(false);
        setAssessmentLoading(false);
        setTrendsLoading(false);
        setActivityLoading(false);
      }
    };

    fetchAnalyticsData();
  }, [isAuthenticated, user?.id, timeRangeDate]);



  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p>Loading...</p>
      </div>
    );
  }

  if (!isAuthenticated) {
    // Redirect to login instead of returning null
    if (typeof window !== 'undefined') {
      window.location.href = '/login';
    }
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Redirecting to Login...</h2>
          <p className="text-gray-600">Please wait while we redirect you.</p>
        </div>
      </div>
    );
  }

  // Show API errors if they exist
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center max-w-md">
          <h2 className="text-2xl font-bold text-red-600 mb-4">Data Loading Error</h2>
          <div className="text-left bg-red-50 p-4 rounded-lg">
            <p className="text-red-700 mb-2">{error}</p>
          </div>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Calculate analytics data - REUSE THE WORKING ASSESSMENT DATA!
  const kpis = overviewData ? calculateKPIs(overviewData) : [];

  // WORKING CHARTS - Use real data when available, sample data as fallback
  const gradeDistribution = assessmentData?.assessment_results?.length > 0
    ? calculateGradeDistribution(assessmentData.assessment_results)
    : [
        { grade: 'A', count: 5, percentage: 25, color: '#10B981' },
        { grade: 'B', count: 8, percentage: 40, color: '#3B82F6' },
        { grade: 'C', count: 4, percentage: 20, color: '#F59E0B' },
        { grade: 'D', count: 2, percentage: 10, color: '#EF4444' },
        { grade: 'F', count: 1, percentage: 5, color: '#6B7280' }
      ];

  const assessmentTrends = assessmentData?.assessment_results?.length > 0
    ? calculateAssessmentTrends(
        assessmentData.assessment_results.map((result: any) => ({
          upload_date: result.student_submission.upload_date,
          assessment_results: [result]
        })),
        selectedTimeRange.days
      )
    : [
        { date: 'Dec 01', assessments: 3, submissions: 5 },
        { date: 'Dec 02', assessments: 2, submissions: 3 },
        { date: 'Dec 03', assessments: 4, submissions: 6 },
        { date: 'Dec 04', assessments: 1, submissions: 2 },
        { date: 'Dec 05', assessments: 5, submissions: 7 }
      ];

  const processingEfficiency = overviewData ? calculateProcessingEfficiency(overviewData.assignments) : [];
  const recentActivity = activityData ? calculateRecentActivity(
    activityData.assessment_results || [],
    activityData.student_submissions || []
  ) : [];





  // Loading state is now managed by our useState hook

  return (
    <DashboardLayout>
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-light mb-2">Analytics Dashboard</h1>
            <p className="text-gray-600">
              Insights and performance metrics for {user?.displayName || user?.email}
            </p>
          </div>

          {/* Time Range Selector */}
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">Time Range:</label>
            <select
              value={selectedTimeRange.value}
              onChange={(e) => {
                const range = TIME_RANGES.find(r => r.value === e.target.value);
                if (range) setSelectedTimeRange(range);
              }}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent"
            >
              {TIME_RANGES.map((range) => (
                <option key={range.value} value={range.value}>
                  {range.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* KPI Cards */}
      <KPICards kpis={kpis} loading={overviewLoading} />

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Grade Distribution - REAL-TIME DATA CONNECTED */}
        <div className="relative">
          <SimpleGradeChart
            data={gradeDistribution}
            loading={assessmentLoading}
          />
          {/* Data Source Indicator */}
          <div className="absolute top-2 right-2">
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
              assessmentData?.assessment_results?.length > 0
                ? 'bg-green-100 text-green-800'
                : 'bg-amber-100 text-amber-800'
            }`}>
              {assessmentData?.assessment_results?.length > 0 ? '🔴 Live Data' : '📊 Sample Data'}
            </span>
          </div>
        </div>

        {/* Assessment Trends - REAL-TIME DATA CONNECTED */}
        <div className="relative">
          <SimpleTrendsChart
            data={assessmentTrends}
            loading={trendsLoading}
          />
          {/* Data Source Indicator */}
          <div className="absolute top-2 right-2">
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
              trendsData?.student_submissions?.length > 0
                ? 'bg-green-100 text-green-800'
                : 'bg-amber-100 text-amber-800'
            }`}>
              {trendsData?.student_submissions?.length > 0 ? '🔴 Live Data' : '📊 Sample Data'}
            </span>
          </div>
        </div>
      </div>

      {/* Processing Efficiency Chart - Fixed height to prevent jittering */}
      <div className="mb-8">
        <ProcessingEfficiencyChart
          data={processingEfficiency}
          loading={overviewLoading}
        />
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <RecentActivityFeed
            activities={recentActivity}
            loading={activityLoading}
          />
        </div>

        {/* Quick Stats Sidebar */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium mb-4">Quick Stats</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Active Assignments</span>
              <span className="font-medium">
                {overviewData?.assignments?.length || 0}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Pending Assessments</span>
              <span className="font-medium text-amber-600">
                {trendsData?.student_submissions?.filter((sub: any) => !sub.assessment_results?.length).length || 0}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Completion Rate</span>
              <span className="font-medium text-green-600">
                {(() => {
                  const total = trendsData?.student_submissions?.length || 0;
                  const completed = trendsData?.student_submissions?.filter((sub: any) => sub.assessment_results?.length > 0).length || 0;
                  return total > 0 ? Math.round((completed / total) * 100) : 0;
                })()}%
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Average Grade</span>
              <span className="font-medium">
                {(() => {
                  const grades = assessmentData?.assessment_results?.map((r: any) => r.overall_grade).filter(Boolean) || [];
                  if (grades.length === 0) return 'N/A';

                  const gradeValues = { 'A': 4, 'B': 3, 'C': 2, 'D': 1, 'F': 0 };
                  const validGrades = grades.filter((g: string) => g in gradeValues);
                  if (validGrades.length === 0) return 'N/A';

                  const avg = validGrades.reduce((sum: number, grade: string) =>
                    sum + gradeValues[grade as keyof typeof gradeValues], 0) / validGrades.length;

                  return avg >= 3.5 ? 'A' : avg >= 2.5 ? 'B' : avg >= 1.5 ? 'C' : avg >= 0.5 ? 'D' : 'F';
                })()}
              </span>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
