/**
 * Format a date string to a more readable format
 * @param dateString ISO date string
 * @returns Formatted date string (e.g., "May 21, 2023")
 */
export function formatDate(dateString: string): string {
  if (!dateString) return 'N/A';
  
  const date = new Date(dateString);
  
  // Check if date is valid
  if (isNaN(date.getTime())) {
    return 'Invalid date';
  }
  
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

/**
 * Get a color class based on submission status
 * @param status Submission status
 * @returns Tailwind CSS class string
 */
export function getStatusColor(status: string): string {
  switch (status) {
    case 'draft':
      return 'bg-gray-200 text-gray-800';
    case 'submitted':
      return 'bg-blue-100 text-blue-800';
    case 'grading':
      return 'bg-yellow-100 text-yellow-800';
    case 'graded':
      return 'bg-green-100 text-green-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

/**
 * Format a file size in bytes to a human-readable format
 * @param bytes File size in bytes
 * @returns Formatted file size (e.g., "1.5 MB")
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

/**
 * Get a file type icon based on file extension
 * @param fileName File name with extension
 * @returns Icon name or type
 */
export function getFileTypeIcon(fileName: string): string {
  if (!fileName) return 'document';
  
  const extension = fileName.split('.').pop()?.toLowerCase();
  
  if (!extension) return 'document';
  
  // Image files
  if (['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'].includes(extension)) {
    return 'image';
  }
  
  // Document files
  if (['pdf', 'doc', 'docx', 'txt', 'rtf'].includes(extension)) {
    return 'document';
  }
  
  // Code files
  if (['js', 'ts', 'jsx', 'tsx', 'py', 'java', 'c', 'cpp', 'cs', 'html', 'css', 'json'].includes(extension)) {
    return 'code';
  }
  
  // Archive files
  if (['zip', 'rar', 'tar', 'gz', '7z'].includes(extension)) {
    return 'archive';
  }
  
  return 'document';
}
