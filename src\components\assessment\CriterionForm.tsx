import React, { useState, useEffect } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';

interface Criterion {
  id: string;
  name: string;
  description: string;
  weight: number;
}

interface CriterionFormProps {
  criterion: Criterion | null;
  onSave: (criterion: Omit<Criterion, 'id'>) => void;
  onCancel: () => void;
  remainingWeight: number;
}

export default function CriterionForm({ criterion, onSave, onCancel, remainingWeight }: CriterionFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    weight: 0
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (criterion) {
      setFormData({
        name: criterion.name,
        description: criterion.description,
        weight: criterion.weight
      });
    } else {
      const defaultWeight = Math.min(remainingWeight, 25);
      console.log('Setting default weight:', defaultWeight, 'remaining:', remainingWeight);
      setFormData({
        name: '',
        description: '',
        weight: defaultWeight > 0 ? defaultWeight : 1 // Default to remaining weight or 1%
      });
    }
  }, [criterion, remainingWeight]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Criterion name is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (formData.weight <= 0) {
      newErrors.weight = 'Weight must be greater than 0';
    } else if (formData.weight > remainingWeight) {
      newErrors.weight = `Weight cannot exceed ${remainingWeight}% (remaining available weight)`;
    } else if (formData.weight > 100) {
      newErrors.weight = 'Weight cannot exceed 100%';
    }

    console.log('Validation errors:', newErrors);
    console.log('Form data:', formData);
    console.log('Remaining weight:', remainingWeight);

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted with data:', formData);

    if (validateForm()) {
      console.log('Form validation passed, saving criterion');
      onSave(formData);
    } else {
      console.log('Form validation failed:', errors);
    }
  };

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-text">
            {criterion ? 'Edit Criterion' : 'Add New Criterion'}
          </h3>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Criterion Name */}
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-text mb-2">
              Criterion Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className={`block w-full rounded-md border py-2 px-3 bg-white shadow-sm focus:ring-2 focus:ring-accent focus:border-accent ${
                errors.name ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Enter criterion name"
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600">{errors.name}</p>
            )}
          </div>

          {/* Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-text mb-2">
              Description <span className="text-red-500">*</span>
            </label>
            <textarea
              id="description"
              rows={4}
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              className={`block w-full rounded-md border py-2 px-3 bg-white shadow-sm focus:ring-2 focus:ring-accent focus:border-accent ${
                errors.description ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Describe what this criterion evaluates"
              maxLength={500}
            />
            <div className="mt-1 flex justify-between">
              {errors.description && (
                <p className="text-sm text-red-600">{errors.description}</p>
              )}
              <p className="text-sm text-gray-500 ml-auto">
                {formData.description.length}/500 characters
              </p>
            </div>
          </div>

          {/* Weight */}
          <div>
            <label htmlFor="weight" className="block text-sm font-medium text-text mb-2">
              Weight (%) <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <input
                type="number"
                id="weight"
                min="1"
                max={remainingWeight}
                value={formData.weight}
                onChange={(e) => handleInputChange('weight', parseInt(e.target.value) || 0)}
                className={`block w-full rounded-md border py-2 px-3 bg-white shadow-sm focus:ring-2 focus:ring-accent focus:border-accent ${
                  errors.weight ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Enter weight percentage"
              />
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <span className="text-gray-500 text-sm">%</span>
              </div>
            </div>
            {errors.weight && (
              <p className="mt-1 text-sm text-red-600">{errors.weight}</p>
            )}
            <p className="mt-1 text-sm text-gray-500">
              Available weight: {remainingWeight}%
            </p>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-accent text-white rounded-md hover:bg-[#9A7649] transition-colors"
            >
              {criterion ? 'Update Criterion' : 'Add Criterion'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
