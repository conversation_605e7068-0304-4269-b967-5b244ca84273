import { ClockIcon, InformationCircleIcon } from '@heroicons/react/24/outline';

interface ProcessingTimeEstimateProps {
  estimatedTime: number; // in minutes
}

export default function ProcessingTimeEstimate({ estimatedTime }: ProcessingTimeEstimateProps) {
  const formatTime = (minutes: number): string => {
    if (minutes < 1) {
      return 'Less than 1 minute';
    } else if (minutes < 60) {
      return `${minutes.toFixed(1)} minutes`;
    } else {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      if (remainingMinutes === 0) {
        return `${hours} hour${hours > 1 ? 's' : ''}`;
      }
      return `${hours} hour${hours > 1 ? 's' : ''} ${remainingMinutes.toFixed(0)} minutes`;
    }
  };

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
      <div className="flex items-start space-x-3">
        <ClockIcon className="w-6 h-6 text-blue-600 mt-0.5 flex-shrink-0" />
        <div className="flex-1">
          <h3 className="text-lg font-medium text-blue-900 mb-2">
            Estimated Processing Time
          </h3>
          <p className="text-blue-800 mb-3">
            Based on your configuration and file, processing should take approximately{' '}
            <span className="font-semibold">{formatTime(estimatedTime)}</span>.
          </p>
          <div className="flex items-start space-x-2 text-sm text-blue-700">
            <InformationCircleIcon className="w-4 h-4 mt-0.5 flex-shrink-0" />
            <p>
              Processing time varies depending on file complexity and system load. 
              Consider upgrading to expedited processing for faster results.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
