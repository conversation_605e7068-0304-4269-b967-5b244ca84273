import { Request, Response } from 'express';
import { AIService } from '../../../src/lib/ai/AIService';
import { DatabaseIntegration } from '../../../src/lib/ai/DatabaseIntegration';
import {
  AssessmentConfig,
  Submission,
  AssessmentResult,
  AIAssessmentError
} from '../../../src/types/ai-assessment';
import { ApolloClient, InMemoryCache, createHttpLink } from '@apollo/client';

/**
 * Nhost Function: Process AI Assessment
 *
 * This function handles the AI assessment of student submissions.
 * It's designed to be platform-agnostic and can easily be migrated to Vercel.
 */
export default async (req: Request, res: Response) => {
  // CORS headers
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({
      error: 'Method not allowed',
      message: 'Only POST requests are supported'
    });
  }

  try {
    console.log('🚀 Assessment request received');

    // Validate request body
    const { submissionId, config } = req.body;

    if (!submissionId || !config) {
      return res.status(400).json({
        error: 'Invalid request',
        message: 'Both submissionId and config are required'
      });
    }

    // Create GraphQL client for database operations
    const graphqlClient = new ApolloClient({
      link: createHttpLink({
        uri: process.env.NHOST_GRAPHQL_URL || 'http://localhost:1337/v1/graphql',
        headers: {
          'x-hasura-admin-secret': process.env.NHOST_ADMIN_SECRET || ''
        }
      }),
      cache: new InMemoryCache()
    });

    // Create database integration
    const dbIntegration = new DatabaseIntegration(graphqlClient);

    // Get submission data from database
    console.log(`📄 Fetching submission data: ${submissionId}`);
    const submissionData = await dbIntegration.getSubmissionForAssessment(submissionId);

    // Validate configuration
    AIService.validateConfig(config);

    // Create AI service instance
    const aiService = new AIService();

    // Process the assessment
    console.log(`📝 Processing AI assessment for submission: ${submissionId}`);
    const startTime = Date.now();

    const result: AssessmentResult = await aiService.assessSubmission(
      submissionData as Submission,
      config as AssessmentConfig
    );

    // Create criteria mapping and save results to database
    const criteriaMapping = dbIntegration.createCriteriaMapping(
      result.detailedFeedback,
      submissionData.criteria
    );

    await dbIntegration.saveAssessmentResults(submissionId, result, criteriaMapping);

    const totalTime = Date.now() - startTime;
    console.log(`✅ Assessment completed and saved in ${totalTime}ms`);

    // Return successful result
    return res.status(200).json({
      success: true,
      result: {
        id: result.id,
        submissionId: submissionId,
        overallGrade: result.overallGrade,
        overallScore: result.overallScore,
        feedbackSummary: result.feedbackSummary,
        confidenceScore: result.confidenceScore,
        aiProvider: result.aiProvider,
        modelUsed: result.modelUsed
      },
      metadata: {
        processingTimeMs: totalTime,
        timestamp: new Date().toISOString(),
        provider: result.aiProvider,
        model: result.modelUsed,
        tokenUsage: result.tokenUsage
      }
    });

  } catch (error: any) {
    console.error('❌ Assessment failed:', error);

    // Handle specific AI errors
    if (error instanceof AIAssessmentError) {
      const statusCode = error.statusCode || 500;
      return res.status(statusCode).json({
        success: false,
        error: error.name,
        message: error.message,
        provider: error.provider,
        retryable: error.retryable
      });
    }

    // Handle validation errors
    if (error.message.includes('required') || error.message.includes('Invalid')) {
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        message: error.message
      });
    }

    // Handle unexpected errors
    return res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'An unexpected error occurred during assessment',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Alternative Vercel-compatible export
 * This allows the same code to work on both platforms
 */
export const handler = async (req: any, res: any) => {
  return await module.exports.default(req, res);
};
