import { AssessmentConfig } from '@/types/ai-assessment';
import { nhost } from '@/lib/nhost';

/**
 * Frontend API for triggering AI assessments
 * Platform-agnostic - works with both Nhost and Vercel
 */
export class AssessmentAPI {
  private baseUrl: string;

  constructor() {
    // Automatically detect the platform and set the correct endpoint
    this.baseUrl = this.getBaseUrl();
  }

  /**
   * Submit an assessment for AI processing
   */
  async submitAssessment(
    submissionId: string,
    config: AssessmentConfig
  ): Promise<{
    success: boolean;
    processingId?: string;
    estimatedCompletion?: Date;
    error?: string;
  }> {
    try {
      console.log(`🚀 Submitting assessment for submission: ${submissionId}`);
      console.log(`🤖 Using ${config.aiModel} model`);
      console.log(`🌐 API URL: ${this.baseUrl}/assess-submission`);
      console.log(`📋 Config:`, config);

      const response = await fetch(`${this.baseUrl}/assess-submission`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Add authentication headers if needed
          ...(this.getAuthHeaders())
        },
        body: JSON.stringify({
          submissionId,
          config
        })
      });

      console.log(`📡 Response status: ${response.status} ${response.statusText}`);
      console.log(`📡 Response headers:`, Object.fromEntries(response.headers.entries()));

      // Check if response is JSON
      const contentType = response.headers.get('content-type');
      let data;

      if (contentType && contentType.includes('application/json')) {
        try {
          data = await response.json();
        } catch (jsonError) {
          console.error('❌ Failed to parse JSON response:', jsonError);
          const textResponse = await response.text();
          console.error('📄 Raw response:', textResponse);
          throw new Error(`Invalid JSON response: ${textResponse.substring(0, 200)}...`);
        }
      } else {
        // Not JSON, get as text for better error reporting
        const textResponse = await response.text();
        console.error('📄 Full HTML Error Response:');
        console.error(textResponse);

        // Try to extract meaningful error from HTML
        const errorMatch = textResponse.match(/<title>(.*?)<\/title>/i);
        const errorTitle = errorMatch ? errorMatch[1] : 'Unknown Error';

        // Look for error messages in common patterns
        const bodyMatch = textResponse.match(/<body[^>]*>(.*?)<\/body>/is);
        const bodyContent = bodyMatch ? bodyMatch[1].replace(/<[^>]*>/g, ' ').trim() : '';

        throw new Error(`Server returned HTML error page: ${errorTitle}. Content: ${bodyContent.substring(0, 300)}...`);
      }

      if (!response.ok) {
        throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      if (data.success) {
        console.log(`✅ Assessment submitted successfully`);
        console.log(`⏱️ Processing time: ${data.metadata?.processingTimeMs}ms`);
        console.log(`💰 Estimated cost: $${data.metadata?.tokenUsage?.estimatedCost?.toFixed(4) || '0.0000'}`);

        return {
          success: true,
          processingId: data.result?.id,
          estimatedCompletion: new Date(Date.now() + 60000) // 1 minute from now
        };
      } else {
        throw new Error(data.message || 'Assessment submission failed');
      }

    } catch (error: any) {
      console.error('❌ Assessment submission failed:', error);

      return {
        success: false,
        error: error.message || 'Unknown error occurred'
      };
    }
  }

  /**
   * Get assessment status (for future polling implementation)
   */
  async getAssessmentStatus(processingId: string): Promise<{
    status: 'pending' | 'processing' | 'completed' | 'failed';
    progress?: number;
    result?: any;
    error?: string;
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/assessment-status/${processingId}`, {
        headers: this.getAuthHeaders()
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP ${response.status}`);
      }

      return data;

    } catch (error: any) {
      console.error('Failed to get assessment status:', error);
      return {
        status: 'failed',
        error: error.message
      };
    }
  }

  /**
   * Estimate processing time based on configuration
   */
  static estimateProcessingTime(config: AssessmentConfig): number {
    let baseTime = 30; // Base 30 seconds

    // Model complexity
    if (config.aiModel === 'comprehensive') {
      baseTime += 45; // Anthropic typically takes longer for detailed analysis
    }

    // Priority adjustments
    switch (config.priority) {
      case 'high':
        baseTime *= 0.7;
        break;
      case 'urgent':
        baseTime *= 0.5;
        break;
    }

    // Feedback level
    switch (config.feedbackLevel) {
      case 'detailed':
        baseTime += 30;
        break;
      case 'brief':
        baseTime -= 10;
        break;
    }

    // Feature complexity
    const enabledFeatures = Object.values(config.features).filter(Boolean).length;
    baseTime += enabledFeatures * 5;

    // Criteria complexity
    baseTime += config.criteria.length * 3;

    return Math.max(baseTime, 15); // Minimum 15 seconds
  }

  /**
   * Validate assessment configuration
   */
  static validateConfig(config: AssessmentConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!config.submissionId) {
      errors.push('Submission ID is required');
    }

    if (!config.criteria || config.criteria.length === 0) {
      errors.push('At least one assessment criteria is required');
    }

    if (config.criteria) {
      const totalWeight = config.criteria.reduce((sum, c) => sum + c.weight, 0);
      if (Math.abs(totalWeight - 100) > 0.01) {
        errors.push(`Criteria weights must sum to 100%, got ${totalWeight}%`);
      }
    }

    if (!['standard', 'comprehensive'].includes(config.aiModel)) {
      errors.push('Invalid AI model specified');
    }

    if (!['standard', 'high', 'urgent'].includes(config.priority)) {
      errors.push('Invalid priority level specified');
    }

    if (!['brief', 'standard', 'detailed'].includes(config.feedbackLevel)) {
      errors.push('Invalid feedback level specified');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Get the correct base URL for the current platform
   */
  private getBaseUrl(): string {
    // Check if we're in a browser environment
    if (typeof window !== 'undefined') {
      // Browser environment - use relative URLs or detect platform
      const hostname = window.location.hostname;

      if (hostname.includes('vercel.app') || hostname.includes('vercel.com') || hostname === 'localhost') {
        // Vercel deployment or local development
        return '/api';
      } else if (hostname.includes('nhost.run') || hostname.includes('nhost.app')) {
        // Nhost deployment
        return '/functions/v1';
      } else {
        // Default to Vercel API routes
        return '/api';
      }
    } else {
      // Server environment - prefer Vercel API routes
      if (process.env.VERCEL_URL) {
        return '/api';
      }
      return process.env.NHOST_FUNCTIONS_URL || '/api';
    }
  }

  /**
   * Get authentication headers
   */
  private getAuthHeaders(): Record<string, string> {
    const headers: Record<string, string> = {};

    // Add authentication token if available
    if (typeof window !== 'undefined') {
      try {
        // Use proper Nhost client to get access token
        const token = nhost.auth.getAccessToken();

        if (token) {
          headers['Authorization'] = `Bearer ${token}`;
          console.log('🔑 Using Nhost access token for API request');
        } else {
          console.warn('⚠️ No access token available from Nhost');
        }
      } catch (error) {
        console.error('❌ Error getting Nhost access token:', error);
      }
    }

    return headers;
  }

  /**
   * Calculate estimated cost based on configuration
   */
  static estimateCost(config: AssessmentConfig): number {
    // Rough estimation based on typical token usage
    let estimatedTokens = 1000; // Base tokens

    // Model differences
    if (config.aiModel === 'comprehensive') {
      estimatedTokens *= 1.5; // Anthropic typically uses more tokens
    }

    // Feedback level
    switch (config.feedbackLevel) {
      case 'detailed':
        estimatedTokens *= 1.8;
        break;
      case 'brief':
        estimatedTokens *= 0.6;
        break;
    }

    // Criteria complexity
    estimatedTokens += config.criteria.length * 200;

    // Feature complexity
    const enabledFeatures = Object.values(config.features).filter(Boolean).length;
    estimatedTokens += enabledFeatures * 100;

    // Rough cost calculation (this would be more accurate with actual pricing)
    const costPerToken = config.aiModel === 'standard' ? 0.000002 : 0.000015; // Rough estimates

    return estimatedTokens * costPerToken;
  }
}
