// Analytics Types for Teacher Dashboard

export interface AnalyticsKPI {
  label: string;
  value: number;
  icon: string;
  trend?: {
    direction: 'up' | 'down' | 'stable';
    percentage: number;
    period: string;
  };
}

export interface GradeDistribution {
  grade: string;
  count: number;
  percentage: number;
  color: string;
}

export interface AssessmentTrend {
  date: string;
  assessments: number;
  submissions: number;
}

export interface ProcessingEfficiency {
  assignmentTitle: string;
  averageProcessingTime: number; // in minutes
  submissionCount: number;
  completionRate: number; // percentage
}

export interface RecentActivity {
  id: string;
  type: 'assessment' | 'submission' | 'assignment';
  title: string;
  studentName?: string;
  assignmentTitle?: string;
  timestamp: Date;
  status: string;
  grade?: string;
}

export interface SubjectPerformance {
  subject: string;
  averageGrade: string;
  submissionCount: number;
  assessmentCount: number;
  topPerformers: string[];
}

export interface AnalyticsDashboardData {
  kpis: AnalyticsKPI[];
  gradeDistribution: GradeDistribution[];
  assessmentTrends: AssessmentTrend[];
  processingEfficiency: ProcessingEfficiency[];
  recentActivity: RecentActivity[];
  subjectPerformance: SubjectPerformance[];
  lastUpdated: Date;
}

export interface AnalyticsTimeRange {
  label: string;
  value: 'week' | 'month' | 'quarter' | 'year' | 'all';
  days: number;
}

export interface AnalyticsFilters {
  timeRange: AnalyticsTimeRange;
  subjects: string[];
  assignmentTypes: string[];
}
