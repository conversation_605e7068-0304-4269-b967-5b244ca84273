'use client';

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>A<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';

interface TrendData {
  date: string;
  assessments: number;
  submissions: number;
}

interface SimpleTrendsChartProps {
  data: TrendData[];
  loading?: boolean;
}

export default function SimpleTrendsChart({ data, loading }: SimpleTrendsChartProps) {
  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium mb-4">Assessment Trends</h3>
        <div className="h-64 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium mb-4">Assessment Trends</h3>
        <div className="h-64 flex items-center justify-center text-gray-500">
          No trend data available
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-medium mb-4">Assessment Trends</h3>
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line 
              type="monotone" 
              dataKey="submissions" 
              stroke="#3B82F6" 
              strokeWidth={2}
              name="Submissions"
            />
            <Line 
              type="monotone" 
              dataKey="assessments" 
              stroke="#10B981" 
              strokeWidth={2}
              name="Assessments"
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
