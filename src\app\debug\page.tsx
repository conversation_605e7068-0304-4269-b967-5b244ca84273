'use client';

import { useQuery, useMutation, gql } from '@apollo/client';
import { useUserData } from '@nhost/nextjs';
import { useState } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';

// Test queries
const GET_ASSIGNMENTS_TEST = gql`
  query GetAssignmentsTest($user_id: uuid!) {
    assignments(
      where: { user_id: { _eq: $user_id } }
    ) {
      id
      title
      description
      created_at
    }
  }
`;

const GET_SUBMISSIONS_TEST = gql`
  query GetSubmissionsTest {
    student_submissions {
      id
      assignment_id
      student_name
      file_path
      file_type
      processing_status
      upload_date
    }
  }
`;

const CREATE_ASSIGNMENT_TEST = gql`
  mutation CreateAssignmentTest(
    $title: String!
    $description: String!
    $user_id: uuid!
  ) {
    insert_assignments_one(object: {
      title: $title
      description: $description
      user_id: $user_id
    }) {
      id
      title
      description
      created_at
    }
  }
`;

export default function DebugPage() {
  const user = useUserData();
  const [testTitle, setTestTitle] = useState('Test Assignment');
  const [testDescription, setTestDescription] = useState('This is a test assignment');

  // Test assignment query
  const { data: assignmentsData, loading: assignmentsLoading, error: assignmentsError } = useQuery(GET_ASSIGNMENTS_TEST, {
    variables: { user_id: user?.id },
    skip: !user?.id,
  });

  // Test submissions query
  const { data: submissionsData, loading: submissionsLoading, error: submissionsError } = useQuery(GET_SUBMISSIONS_TEST, {
    skip: !user?.id,
  });

  // Test assignment creation
  const [createAssignment, { loading: createLoading, error: createError }] = useMutation(CREATE_ASSIGNMENT_TEST);

  const handleCreateTest = async () => {
    if (!user?.id) {
      alert('User not authenticated');
      return;
    }

    try {
      const result = await createAssignment({
        variables: {
          title: testTitle,
          description: testDescription,
          user_id: user.id,
        },
      });

      alert('Test assignment created successfully!');
    } catch (error) {

      alert('Failed to create test assignment');
    }
  };

  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto p-6">
        <h1 className="text-3xl font-bold mb-6">GraphQL Debug Page</h1>
        
        {/* User Info */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">User Information</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(user, null, 2)}
          </pre>
        </div>

        {/* Assignments Test */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Assignments Query Test</h2>
          {assignmentsLoading && <p>Loading assignments...</p>}
          {assignmentsError && (
            <div className="text-red-600 mb-4">
              <p>Error: {assignmentsError.message}</p>
              <pre className="bg-red-50 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(assignmentsError, null, 2)}
              </pre>
            </div>
          )}
          {assignmentsData && (
            <div>
              <p className="mb-2">Found {assignmentsData.assignments.length} assignments</p>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(assignmentsData, null, 2)}
              </pre>
            </div>
          )}
        </div>

        {/* Submissions Test */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Submissions Query Test</h2>
          {submissionsLoading && <p>Loading submissions...</p>}
          {submissionsError && (
            <div className="text-red-600 mb-4">
              <p>Error: {submissionsError.message}</p>
              <pre className="bg-red-50 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(submissionsError, null, 2)}
              </pre>
            </div>
          )}
          {submissionsData && (
            <div>
              <p className="mb-2">Found {submissionsData.student_submissions.length} submissions</p>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(submissionsData, null, 2)}
              </pre>
            </div>
          )}
        </div>

        {/* Create Assignment Test */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Create Assignment Test</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Title:</label>
              <input
                type="text"
                value={testTitle}
                onChange={(e) => setTestTitle(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Description:</label>
              <textarea
                value={testDescription}
                onChange={(e) => setTestDescription(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                rows={3}
              />
            </div>
            <button
              onClick={handleCreateTest}
              disabled={createLoading || !user?.id}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {createLoading ? 'Creating...' : 'Create Test Assignment'}
            </button>
            {createError && (
              <div className="text-red-600">
                <p>Error: {createError.message}</p>
                <pre className="bg-red-50 p-4 rounded text-sm overflow-auto">
                  {JSON.stringify(createError, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
