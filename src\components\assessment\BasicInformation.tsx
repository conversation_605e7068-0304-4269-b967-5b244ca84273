import { useState } from 'react';
import { PencilIcon } from '@heroicons/react/24/outline';

interface BasicInformationProps {
  title: string;
  description: string;
  onUpdate: (updates: { title?: string; description?: string }) => void;
}

export default function BasicInformation({ title, description, onUpdate }: BasicInformationProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editTitle, setEditTitle] = useState(title);
  const [editDescription, setEditDescription] = useState(description);

  const handleSave = () => {
    onUpdate({ title: editTitle, description: editDescription });
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditTitle(title);
    setEditDescription(description);
    setIsEditing(false);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-text">Basic Information</h2>
        {!isEditing && (
          <button
            onClick={() => setIsEditing(true)}
            className="flex items-center space-x-1 text-gray-600 hover:text-gray-800 transition-colors"
          >
            <PencilIcon className="w-4 h-4" />
            <span className="text-sm">Edit</span>
          </button>
        )}
      </div>

      <div className="space-y-4">
        {/* Assessment Title */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Assessment Title
          </label>
          {isEditing ? (
            <input
              type="text"
              value={editTitle}
              onChange={(e) => setEditTitle(e.target.value)}
              placeholder="Enter assessment title"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent"
            />
          ) : (
            <div className="text-gray-600">
              {title || 'No title provided'}
            </div>
          )}
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          {isEditing ? (
            <textarea
              value={editDescription}
              onChange={(e) => setEditDescription(e.target.value)}
              placeholder="Enter assessment description"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent"
            />
          ) : (
            <div className="text-gray-600">
              {description || 'No description provided'}
            </div>
          )}
        </div>

        {/* Edit Actions */}
        {isEditing && (
          <div className="flex justify-end space-x-3 pt-2">
            <button
              onClick={handleCancel}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-accent text-white rounded-md hover:bg-[#9A7649] transition-colors"
            >
              Save
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
