'use client';

import { useState, useEffect } from 'react';
import { useAuthenticationStatus, useUserData } from '@nhost/nextjs';
import { useRouter, useSearchParams } from 'next/navigation';
import { useQuery, gql } from '@apollo/client';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { useSubmissionFilter, FILTER_PRESETS } from '@/lib/submission-filters';
import { GET_ASSIGNMENT_SUBMISSIONS } from '@/lib/gql/submission-queries';

export default function BulkConfigPage() {
  const { isAuthenticated, isLoading } = useAuthenticationStatus();
  const user = useUserData();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get assignment ID from URL params
  const assignmentId = searchParams.get('assignmentId');

  // Query to get submissions for this assignment
  const { data: submissionsData, loading: submissionsLoading } = useQuery(
    GET_ASSIGNMENT_SUBMISSIONS,
    {
      variables: { assignment_id: assignmentId },
      skip: !assignmentId || !isAuthenticated,
      onCompleted: (data) => {
        console.log('📄 Bulk config submissions loaded:', data);
        console.log('📊 Number of submissions:', data?.student_submissions?.length || 0);
      }
    }
  );

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState('reading-comprehension');

  // Quick template options for bulk assessment
  const quickTemplates = [
    { id: 'reading-comprehension', name: 'Reading Comprehension', icon: '📖' },
    { id: 'essay-analysis', name: 'Essay Analysis', icon: '📝' },
    { id: 'creative-writing', name: 'Creative Writing', icon: '✍️' },
    { id: 'historical-analysis', name: 'Historical Analysis', icon: '🏛️' },
    { id: 'scientific-inquiry', name: 'Scientific Inquiry', icon: '🔬' },
    { id: 'mathematical-modeling', name: 'Mathematical Modeling', icon: '📊' }
  ];

  // Handle bulk assessment submission
  const handleBulkSubmit = async () => {
    if (!assignmentId || !selectedTemplate) {
      alert('Missing assignment ID or template selection');
      return;
    }

    setIsSubmitting(true);
    try {
      console.log('🚀 Submitting bulk assessment...');
      console.log('📊 Number of submissions:', submissionsData?.student_submissions?.length);
      console.log('📋 Selected template:', selectedTemplate);

      // Prepare bulk assessment configuration
      const bulkConfig = {
        assignmentId: assignmentId,
        aiModel: 'standard',
        priority: 'standard',
        feedbackLevel: 'detailed',
        assessmentType: 'comprehensive',
        template: selectedTemplate,
        features: {
          enableDetailedFeedback: true,
          enableSuggestions: true,
          enableConfidenceScoring: true
        }
      };

      console.log('📝 Bulk assessment config:', bulkConfig);

      // Submit to bulk assessment API
      const response = await fetch('/api/bulk-assess', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bulkConfig),
      });

      const result = await response.json();

      if (result.success) {
        console.log('✅ Bulk assessment submitted successfully!');
        alert(`✅ Bulk assessment started for ${submissionsData?.student_submissions?.length} submissions!`);

        // Redirect to bulk results page
        router.push(`/assessment/results?assignmentId=${assignmentId}`);
      } else {
        throw new Error(result.message || 'Bulk assessment submission failed');
      }

    } catch (error: any) {
      console.error('❌ Error submitting bulk assessment:', error);
      alert(`Failed to submit bulk assessment: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading || submissionsLoading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading bulk assessment configuration...</div>
        </div>
      </DashboardLayout>
    );
  }

  if (!isAuthenticated) {
    router.push('/auth/sign-in');
    return null;
  }

  // ASSESSMENT-CENTRIC FILTERING (per assessment_filter_prompt.md)
  // Maintain strict data separation between assessments
  const allSubmissions = submissionsData?.student_submissions || [];
  const submissions = assignmentId
    ? allSubmissions.filter((sub: any) => sub.assignment_id === assignmentId)
    : allSubmissions;

  const hasFiltered = assignmentId && submissions.length !== allSubmissions.length;
  const filterSummary = hasFiltered
    ? `Showing ${submissions.length} submissions for this assessment`
    : `Showing all ${submissions.length} submissions`;
  const assignment = submissions[0]?.assignment;

  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto p-6 space-y-8">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-semibold text-text mb-2">
            Bulk Assessment Configuration
          </h1>
          <p className="text-gray-600">
            Quick setup for bulk assessment of {submissions.length} submissions
          </p>
          {hasFiltered && filterSummary && (
            <div className="mt-2 text-sm text-gray-500">
              {filterSummary}
            </div>
          )}
        </div>

        {/* Assignment Information */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-text mb-4">Assignment Details</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Assignment Title
              </label>
              <div className="text-gray-900 font-medium">{assignment?.title || 'Unknown Assignment'}</div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Number of Submissions
              </label>
              <div className="text-gray-900 font-semibold text-accent">{submissions.length} files</div>
            </div>
          </div>
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <div className="text-gray-900">{assignment?.description || 'No description available'}</div>
          </div>
        </div>

        {/* Quick Template Selection */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-text mb-4">Assessment Template</h2>
          <p className="text-gray-600 mb-6">Choose a template that best matches your assignment type:</p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {quickTemplates.map((template) => (
              <div
                key={template.id}
                onClick={() => setSelectedTemplate(template.id)}
                className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                  selectedTemplate === template.id
                    ? 'border-accent bg-accent/5 shadow-md'
                    : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
                }`}
              >
                <div className="text-center">
                  <div className="text-3xl mb-2">{template.icon}</div>
                  <div className="font-medium text-gray-900">{template.name}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Submissions Preview */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-text mb-4">Submissions to Process</h2>
          <div className="max-h-64 overflow-y-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {submissions.map((submission: any) => (
                <div key={submission.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <div className="w-8 h-8 bg-accent text-white rounded-full flex items-center justify-center text-sm font-medium">
                    {submission.student_name?.charAt(0) || '?'}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">{submission.student_name}</div>
                    <div className="text-sm text-gray-500">{submission.file_type?.toUpperCase()}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Processing Configuration */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-text mb-4">Processing Settings</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <div className="font-medium text-blue-900">AI Model</div>
              <div className="text-blue-700">Standard (OpenAI)</div>
              <div className="text-sm text-blue-600 mt-1">Optimized for bulk processing</div>
            </div>
            <div className="p-4 bg-green-50 rounded-lg">
              <div className="font-medium text-green-900">Priority</div>
              <div className="text-green-700">Standard</div>
              <div className="text-sm text-green-600 mt-1">Balanced speed and quality</div>
            </div>
            <div className="p-4 bg-purple-50 rounded-lg">
              <div className="font-medium text-purple-900">Feedback Level</div>
              <div className="text-purple-700">Detailed</div>
              <div className="text-sm text-purple-600 mt-1">Comprehensive assessment</div>
            </div>
          </div>
        </div>

        {/* Processing Time Estimate */}
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-orange-900 mb-2">⏱️ Processing Time Estimate</h3>
          <div className="text-orange-800">
            <div className="text-2xl font-bold">{submissions.length * 2} minutes</div>
            <div className="text-sm">Approximately 2 minutes per submission</div>
          </div>
        </div>

        {/* Submit Actions */}
        <div className="flex justify-between pt-6">
          <button
            onClick={() => router.push(`/student-upload?assignmentId=${assignmentId}`)}
            className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors flex items-center space-x-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            <span>Back to Uploads</span>
          </button>
          
          <button
            onClick={handleBulkSubmit}
            disabled={isSubmitting || submissions.length === 0 || !selectedTemplate}
            className="px-8 py-3 bg-accent text-white rounded-md hover:bg-[#9A7649] transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed text-lg font-medium"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>Processing...</span>
              </>
            ) : (
              <>
                <span>🚀</span>
                <span>Start Bulk Assessment</span>
              </>
            )}
          </button>
        </div>
      </div>
    </DashboardLayout>
  );
}
