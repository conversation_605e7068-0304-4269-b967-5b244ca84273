import React, { useState } from 'react';
import { PencilIcon, TrashIcon, PlusIcon } from '@heroicons/react/24/outline';
import CriterionForm from './CriterionForm';

interface Criterion {
  id: string;
  name: string;
  description: string;
  weight: number;
}

interface CriteriaBuilderProps {
  criteria: Criterion[];
  onCriteriaChange: (criteria: Criterion[]) => void;
}

export default function CriteriaBuilder({ criteria, onCriteriaChange }: CriteriaBuilderProps) {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingCriterion, setEditingCriterion] = useState<Criterion | null>(null);

  const totalWeight = criteria.reduce((sum, criterion) => sum + criterion.weight, 0);
  const remainingWeight = 100 - totalWeight;

  const handleAddCriterion = () => {
    console.log('Opening add criterion form');
    setEditingCriterion(null);
    setIsFormOpen(true);
  };

  const handleEditCriterion = (criterion: Criterion) => {
    setEditingCriterion(criterion);
    setIsFormOpen(true);
  };

  const handleDeleteCriterion = (criterionId: string) => {
    if (window.confirm('Are you sure you want to delete this criterion?')) {
      const updatedCriteria = criteria.filter(c => c.id !== criterionId);
      onCriteriaChange(updatedCriteria);
    }
  };

  const handleSaveCriterion = (criterionData: Omit<Criterion, 'id'>) => {
    console.log('handleSaveCriterion called with:', criterionData);
    console.log('editingCriterion:', editingCriterion);
    console.log('current criteria:', criteria);

    if (editingCriterion) {
      // Update existing criterion
      console.log('Updating existing criterion');
      const updatedCriteria = criteria.map(c =>
        c.id === editingCriterion.id
          ? { ...criterionData, id: editingCriterion.id }
          : c
      );
      console.log('Updated criteria:', updatedCriteria);
      onCriteriaChange(updatedCriteria);
    } else {
      // Add new criterion
      console.log('Adding new criterion');
      const newCriterion: Criterion = {
        ...criterionData,
        id: Date.now().toString()
      };
      console.log('New criterion:', newCriterion);
      const newCriteria = [...criteria, newCriterion];
      console.log('New criteria array:', newCriteria);
      onCriteriaChange(newCriteria);
    }
    setIsFormOpen(false);
    setEditingCriterion(null);
  };

  const handleCancelForm = () => {
    setIsFormOpen(false);
    setEditingCriterion(null);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex justify-between items-start mb-6">
        <div>
          <h2 className="text-xl font-semibold text-text mb-2">Assessment Criteria</h2>
          <p className="text-gray-600">Customize the criteria used to evaluate submissions</p>
        </div>
        <button
          onClick={handleAddCriterion}
          className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
        >
          <PencilIcon className="w-4 h-4" />
          <span>Edit Criteria</span>
        </button>
      </div>

      {/* Weight Summary */}
      <div className={`mb-6 p-4 rounded-lg border ${
        totalWeight === 100
          ? 'bg-green-50 border-green-200'
          : totalWeight > 100
          ? 'bg-red-50 border-red-200'
          : 'bg-orange-50 border-orange-200'
      }`}>
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">Total Weight:</span>
          <span className={`text-lg font-bold ${
            totalWeight === 100 ? 'text-green-600' : totalWeight > 100 ? 'text-red-600' : 'text-orange-600'
          }`}>
            {totalWeight}%
          </span>
        </div>
        {totalWeight !== 100 && (
          <div className={`mt-2 text-sm ${
            totalWeight > 100 ? 'text-red-600' : 'text-orange-600'
          }`}>
            {totalWeight < 100
              ? `${remainingWeight}% remaining to reach 100%`
              : `${totalWeight - 100}% over the 100% limit`
            }
          </div>
        )}
        {totalWeight === 100 && (
          <div className="mt-2 text-sm text-green-600">
            ✓ Perfect! All criteria weights total 100%
          </div>
        )}
      </div>

      {/* Criteria List */}
      <div className="space-y-4">
        {criteria.map((criterion) => (
          <div
            key={criterion.id}
            className="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow"
          >
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <h3 className="font-medium text-text">{criterion.name}</h3>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                    {criterion.weight}%
                  </span>
                </div>
                <p className="text-sm text-gray-600">{criterion.description}</p>
              </div>
              <div className="flex items-center space-x-2 ml-4">
                <button
                  onClick={() => handleEditCriterion(criterion)}
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                  title="Edit criterion"
                >
                  <PencilIcon className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleDeleteCriterion(criterion.id)}
                  className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                  title="Delete criterion"
                >
                  <TrashIcon className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {criteria.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <PlusIcon className="w-12 h-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No criteria defined</h3>
          <p className="text-gray-600 mb-4">Add your first assessment criterion to get started.</p>
          <button
            onClick={handleAddCriterion}
            className="px-4 py-2 bg-accent text-white rounded-md hover:bg-[#9A7649] transition-colors"
          >
            Add First Criterion
          </button>
        </div>
      )}

      {/* Criterion Form Modal */}
      {isFormOpen && (
        <CriterionForm
          criterion={editingCriterion}
          onSave={handleSaveCriterion}
          onCancel={handleCancelForm}
          remainingWeight={editingCriterion ? remainingWeight + editingCriterion.weight : remainingWeight}
        />
      )}
    </div>
  );
}
