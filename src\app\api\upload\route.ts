import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const bucketId = formData.get('bucketId') as string || 'default';

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    console.log('API Upload - File details:', {
      name: file.name,
      size: file.size,
      type: file.type,
      bucketId
    });

    // Use direct Nhost Storage API with admin secret (no React Context)
    const uploadUrl = `https://${process.env.NEXT_PUBLIC_NHOST_SUBDOMAIN}.storage.${process.env.NEXT_PUBLIC_NHOST_REGION}.nhost.run/v1/files`;

    const uploadFormData = new FormData();
    uploadFormData.append('file', file);
    uploadFormData.append('bucket-id', bucketId);

    const uploadResponse = await fetch(uploadUrl, {
      method: 'POST',
      headers: {
        'x-hasura-admin-secret': process.env.NHOST_ADMIN_SECRET!,
      },
      body: uploadFormData,
    });

    if (!uploadResponse.ok) {
      const errorText = await uploadResponse.text();
      console.error('Nhost upload error:', errorText);
      return NextResponse.json(
        { error: `Upload failed: ${errorText}` },
        { status: 500 }
      );
    }

    const uploadResult = await uploadResponse.json();
    console.log('Upload successful:', uploadResult);

    return NextResponse.json({
      success: true,
      fileMetadata: uploadResult,
    });

  } catch (error) {
    console.error('Upload API error:', error);
    return NextResponse.json(
      { error: `Server error: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    );
  }
}
