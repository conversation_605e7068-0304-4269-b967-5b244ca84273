/**
 * Intelligent AI Model Routing System
 * Automatically selects the best AI model based on content analysis and requirements
 */

import { AssessmentConfig, Submission } from '@/types/ai-assessment';

export interface ContentAnalysis {
  complexity: 'low' | 'medium' | 'high';
  contentType: 'text' | 'code' | 'math' | 'creative' | 'analytical' | 'mixed';
  wordCount: number;
  technicalTerms: number;
  subjectArea: string;
  requiresNuancedAnalysis: boolean;
  estimatedProcessingTime: number; // in seconds
}

export interface ModelRecommendation {
  provider: 'openai' | 'anthropic';
  model: string;
  confidence: number; // 0-100
  reasoning: string[];
  estimatedCost: number;
  estimatedTime: number;
}

export interface RoutingDecision {
  selectedModel: ModelRecommendation;
  alternatives: ModelRecommendation[];
  analysisDetails: ContentAnalysis;
  routingStrategy: 'cost-optimized' | 'quality-optimized' | 'speed-optimized' | 'balanced';
}

/**
 * Intelligent AI Router
 */
export class IntelligentAIRouter {
  private subjectKeywords = {
    math: ['equation', 'formula', 'calculate', 'solve', 'theorem', 'proof', 'algebra', 'geometry', 'calculus'],
    science: ['experiment', 'hypothesis', 'analysis', 'data', 'research', 'method', 'conclusion', 'variable'],
    literature: ['character', 'theme', 'metaphor', 'symbolism', 'narrative', 'author', 'literary', 'analysis'],
    history: ['historical', 'period', 'event', 'timeline', 'source', 'primary', 'secondary', 'context'],
    code: ['function', 'variable', 'class', 'method', 'algorithm', 'syntax', 'debug', 'compile', 'import'],
    creative: ['creative', 'imagination', 'original', 'artistic', 'design', 'innovative', 'expression']
  };

  private technicalTerms = [
    'algorithm', 'methodology', 'framework', 'implementation', 'optimization', 'analysis',
    'synthesis', 'evaluation', 'assessment', 'criteria', 'metrics', 'parameters'
  ];

  /**
   * Analyze submission content to determine optimal AI model
   */
  analyzeContent(submission: Submission): ContentAnalysis {
    const content = submission.content.toLowerCase();
    const words = content.split(/\s+/).filter(word => word.length > 2);
    const wordCount = words.length;

    // Detect subject area
    let subjectArea = 'general';
    let maxMatches = 0;
    
    for (const [subject, keywords] of Object.entries(this.subjectKeywords)) {
      const matches = keywords.filter(keyword => content.includes(keyword)).length;
      if (matches > maxMatches) {
        maxMatches = matches;
        subjectArea = subject;
      }
    }

    // Count technical terms
    const technicalTerms = this.technicalTerms.filter(term => content.includes(term)).length;

    // Determine complexity
    let complexity: 'low' | 'medium' | 'high' = 'low';
    if (wordCount > 1000 || technicalTerms > 5) {
      complexity = 'high';
    } else if (wordCount > 500 || technicalTerms > 2) {
      complexity = 'medium';
    }

    // Determine content type
    let contentType: ContentAnalysis['contentType'] = 'text';
    if (subjectArea === 'code') contentType = 'code';
    else if (subjectArea === 'math') contentType = 'math';
    else if (subjectArea === 'creative') contentType = 'creative';
    else if (technicalTerms > 3) contentType = 'analytical';
    else if (Object.values(this.subjectKeywords).flat().some(keyword => content.includes(keyword))) {
      contentType = 'mixed';
    }

    // Determine if nuanced analysis is required
    const requiresNuancedAnalysis = 
      contentType === 'creative' ||
      contentType === 'analytical' ||
      subjectArea === 'literature' ||
      complexity === 'high' ||
      technicalTerms > 4;

    // Estimate processing time
    const baseTime = 15; // seconds
    const complexityMultiplier = { low: 1, medium: 1.5, high: 2.5 };
    const estimatedProcessingTime = Math.round(baseTime * complexityMultiplier[complexity]);

    return {
      complexity,
      contentType,
      wordCount,
      technicalTerms,
      subjectArea,
      requiresNuancedAnalysis,
      estimatedProcessingTime
    };
  }

  /**
   * Generate model recommendations based on content analysis
   */
  generateRecommendations(
    analysis: ContentAnalysis,
    strategy: 'cost-optimized' | 'quality-optimized' | 'speed-optimized' | 'balanced' = 'balanced'
  ): ModelRecommendation[] {
    const recommendations: ModelRecommendation[] = [];

    // OpenAI GPT-4 Recommendation
    const gpt4Reasoning = [];
    let gpt4Confidence = 70;

    if (analysis.contentType === 'code') {
      gpt4Confidence += 15;
      gpt4Reasoning.push('Excellent for code analysis and debugging');
    }
    if (analysis.contentType === 'math') {
      gpt4Confidence += 10;
      gpt4Reasoning.push('Strong mathematical reasoning capabilities');
    }
    if (analysis.complexity === 'low') {
      gpt4Confidence += 5;
      gpt4Reasoning.push('Efficient for straightforward tasks');
    }
    if (strategy === 'speed-optimized') {
      gpt4Confidence += 10;
      gpt4Reasoning.push('Fast response times');
    }

    recommendations.push({
      provider: 'openai',
      model: 'gpt-4',
      confidence: Math.min(gpt4Confidence, 95),
      reasoning: gpt4Reasoning.length > 0 ? gpt4Reasoning : ['Reliable general-purpose model'],
      estimatedCost: this.estimateCost('openai', 'gpt-4', analysis.wordCount),
      estimatedTime: analysis.estimatedProcessingTime
    });

    // Anthropic Claude Recommendation
    const claudeReasoning = [];
    let claudeConfidence = 75;

    if (analysis.requiresNuancedAnalysis) {
      claudeConfidence += 20;
      claudeReasoning.push('Superior nuanced analysis and reasoning');
    }
    if (analysis.contentType === 'creative' || analysis.contentType === 'analytical') {
      claudeConfidence += 15;
      claudeReasoning.push('Excellent for creative and analytical content');
    }
    if (analysis.subjectArea === 'literature') {
      claudeConfidence += 10;
      claudeReasoning.push('Strong literary analysis capabilities');
    }
    if (analysis.complexity === 'high') {
      claudeConfidence += 10;
      claudeReasoning.push('Handles complex content exceptionally well');
    }
    if (strategy === 'quality-optimized') {
      claudeConfidence += 15;
      claudeReasoning.push('Highest quality analysis and feedback');
    }

    recommendations.push({
      provider: 'anthropic',
      model: 'claude-3-sonnet',
      confidence: Math.min(claudeConfidence, 95),
      reasoning: claudeReasoning.length > 0 ? claudeReasoning : ['High-quality general analysis'],
      estimatedCost: this.estimateCost('anthropic', 'claude-3-sonnet', analysis.wordCount),
      estimatedTime: analysis.estimatedProcessingTime * 1.2
    });

    // GPT-3.5 Turbo for cost optimization
    if (strategy === 'cost-optimized' || analysis.complexity === 'low') {
      recommendations.push({
        provider: 'openai',
        model: 'gpt-3.5-turbo',
        confidence: analysis.complexity === 'low' ? 80 : 60,
        reasoning: ['Most cost-effective option', 'Good for simple assessments'],
        estimatedCost: this.estimateCost('openai', 'gpt-3.5-turbo', analysis.wordCount),
        estimatedTime: analysis.estimatedProcessingTime * 0.8
      });
    }

    // Sort by confidence and strategy preference
    return recommendations.sort((a, b) => {
      if (strategy === 'cost-optimized') {
        return a.estimatedCost - b.estimatedCost;
      } else if (strategy === 'speed-optimized') {
        return a.estimatedTime - b.estimatedTime;
      } else {
        return b.confidence - a.confidence;
      }
    });
  }

  /**
   * Make routing decision based on submission and preferences
   */
  makeRoutingDecision(
    submission: Submission,
    config: AssessmentConfig,
    strategy: 'cost-optimized' | 'quality-optimized' | 'speed-optimized' | 'balanced' = 'balanced'
  ): RoutingDecision {
    const analysis = this.analyzeContent(submission);
    const recommendations = this.generateRecommendations(analysis, strategy);
    
    // Select the best recommendation
    const selectedModel = recommendations[0];
    const alternatives = recommendations.slice(1);

    console.log(`🧠 Intelligent Routing Decision for ${submission.id}:`);
    console.log(`📊 Content Analysis:`, analysis);
    console.log(`🎯 Selected Model: ${selectedModel.provider}/${selectedModel.model} (${selectedModel.confidence}% confidence)`);
    console.log(`💡 Reasoning: ${selectedModel.reasoning.join(', ')}`);

    return {
      selectedModel,
      alternatives,
      analysisDetails: analysis,
      routingStrategy: strategy
    };
  }

  /**
   * Override model selection based on routing decision
   */
  applyRoutingDecision(config: AssessmentConfig, decision: RoutingDecision): AssessmentConfig {
    const newConfig = { ...config };
    
    // Map provider to aiModel format
    if (decision.selectedModel.provider === 'openai') {
      newConfig.aiModel = 'standard';
    } else {
      newConfig.aiModel = 'comprehensive';
    }

    // Add routing metadata
    newConfig.routingDecision = {
      selectedProvider: decision.selectedModel.provider,
      selectedModel: decision.selectedModel.model,
      confidence: decision.selectedModel.confidence,
      reasoning: decision.selectedModel.reasoning,
      contentAnalysis: decision.analysisDetails
    };

    return newConfig;
  }

  /**
   * Estimate cost based on provider, model, and content length
   */
  private estimateCost(provider: 'openai' | 'anthropic', model: string, wordCount: number): number {
    // Rough token estimation (1 word ≈ 1.3 tokens)
    const estimatedTokens = Math.round(wordCount * 1.3);

    // Cost per 1K tokens (approximate)
    const costs: Record<string, Record<string, number>> = {
      'openai': {
        'gpt-4': 0.03,
        'gpt-3.5-turbo': 0.002
      },
      'anthropic': {
        'claude-3-sonnet': 0.015,
        'claude-3-haiku': 0.0025
      }
    };

    const providerCosts = costs[provider];
    const costPer1K = providerCosts?.[model] || 0.01;
    return (estimatedTokens / 1000) * costPer1K;
  }

  /**
   * Get routing statistics for analytics
   */
  getRoutingStats(): {
    totalDecisions: number;
    providerDistribution: Record<string, number>;
    averageConfidence: number;
    costSavings: number;
  } {
    // This would be implemented with actual tracking in production
    return {
      totalDecisions: 0,
      providerDistribution: { openai: 0, anthropic: 0 },
      averageConfidence: 0,
      costSavings: 0
    };
  }
}

// Global router instance
export const intelligentRouter = new IntelligentAIRouter();

/**
 * Helper functions for easy integration
 */

export function analyzeSubmissionContent(submission: Submission): ContentAnalysis {
  return intelligentRouter.analyzeContent(submission);
}

export function getOptimalModel(
  submission: Submission,
  config: AssessmentConfig,
  strategy?: 'cost-optimized' | 'quality-optimized' | 'speed-optimized' | 'balanced'
): RoutingDecision {
  return intelligentRouter.makeRoutingDecision(submission, config, strategy);
}

export function enhanceConfigWithRouting(
  config: AssessmentConfig,
  submission: Submission,
  strategy?: 'cost-optimized' | 'quality-optimized' | 'speed-optimized' | 'balanced'
): AssessmentConfig {
  const decision = getOptimalModel(submission, config, strategy);
  return intelligentRouter.applyRoutingDecision(config, decision);
}
