@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #F5F2EA;
  --text: #2A2A2A;
  --accent: #B38B5D;
  --teal: #A3BDB8;
  --light-gray: #F0F0F0;
  --white: #FFFFFF;
}

@theme inline {
  --color-background: var(--background);
  --color-text: var(--text);
  --color-accent: var(--accent);
  --color-teal: var(--teal);
  --color-light-gray: var(--light-gray);
  --color-white: var(--white);
  --font-sans: var(--font-roboto);
}

body {
  font-family: var(--font-roboto), 'Roboto', sans-serif;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 300;
  line-height: 1.2;
}

h1 {
  font-size: 3rem;
}

h2 {
  font-size: 2.5rem;
}

p {
  font-weight: 400;
  line-height: 1.6;
}

button, .button {
  border-radius: 25px;
  font-weight: 500;
  transition: background-color 0.3s ease;
}
