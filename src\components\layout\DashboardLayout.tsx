'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuthenticationStatus, useUserData, useSignOut } from '@nhost/nextjs';
import { useRouter } from 'next/navigation';
import { ThemeProvider, ThemeToggleButton } from '@/lib/theme/theme-provider';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const pathname = usePathname();
  const { isAuthenticated, isLoading } = useAuthenticationStatus();
  const user = useUserData();
  const { signOut } = useSignOut();
  const router = useRouter();

  const handleSignOut = async () => {
    await signOut();
    router.push('/');
  };

  const navItems = [
    { name: 'Dashboard', path: '/dashboard', icon: '📊' },
    { name: 'Analytics', path: '/analytics', icon: '📈' },
    { name: 'Assignments', path: '/assignments', icon: '📝' },
    { name: 'Create Assignment', path: '/assignments/manage', icon: '➕' },
    { name: 'Student Upload', path: '/student-upload', icon: '📁' },
    { name: 'Submissions', path: '/submissions', icon: '📤' },
    { name: 'Phase 4 Demo', path: '/test-phase4', icon: '🚀' },
  ];

  return (
    <ThemeProvider defaultTheme="system">
      <div className="flex h-screen bg-background dark:bg-gray-900 theme-transition">
        {/* Sidebar */}
        <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full'} md:translate-x-0 transition-transform duration-300 ease-in-out theme-transition`}>
          <div className="flex flex-col h-full">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <Link href="/" className="text-2xl font-light text-gray-900 dark:text-white">
                RUBRIQ
              </Link>
            </div>

            <nav className="flex-1 overflow-y-auto p-4">
              <ul className="space-y-2">
                {navItems.map((item) => (
                  <li key={item.path}>
                    <Link
                      href={item.path}
                      className={`flex items-center p-3 rounded-lg transition-colors theme-transition ${
                        pathname === item.path
                          ? 'bg-accent text-white'
                          : 'text-gray-700 dark:text-gray-300 hover:bg-lightGray dark:hover:bg-gray-700'
                      }`}
                    >
                      <span className="mr-3">{item.icon}</span>
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </nav>

            <div className="p-4 border-t border-gray-200 dark:border-gray-700 space-y-3">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-lightGray dark:bg-gray-700 flex items-center justify-center text-lg text-gray-700 dark:text-gray-300">
                  {user?.displayName?.[0] || user?.email?.[0] || '?'}
                </div>
                <div className="ml-3">
                  <p className="font-medium truncate max-w-[160px] text-gray-900 dark:text-white">
                    {user?.displayName || user?.email}
                  </p>
                  <button
                    onClick={handleSignOut}
                    className="text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                  >
                    Sign out
                  </button>
                </div>
              </div>
              <div className="flex justify-center">
                <ThemeToggleButton className="w-full" />
              </div>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="flex-1 md:ml-64">
          <div className="p-4 md:p-8">
            {/* Mobile menu button */}
            <button
              className="md:hidden mb-4 p-2 rounded-md bg-lightGray dark:bg-gray-700 text-gray-700 dark:text-gray-300 theme-transition"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            >
              {isSidebarOpen ? '✕' : '☰'}
            </button>

            {/* Page content */}
            {children}
          </div>
        </div>
      </div>
    </ThemeProvider>
  );
}
