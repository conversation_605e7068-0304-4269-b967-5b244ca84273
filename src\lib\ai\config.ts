// AI Configuration - Platform Agnostic
export interface AIConfig {
  openai: {
    apiKey: string;
    organization?: string;
    baseURL?: string;
    defaultModel: string;
    maxTokens: number;
    temperature: number;
  };
  anthropic: {
    apiKey: string;
    baseURL?: string;
    defaultModel: string;
    maxTokens: number;
    temperature: number;
  };
  features: {
    retryAttempts: number;
    retryDelay: number;
    timeoutMs: number;
    enableCostTracking: boolean;
    enableUsageLogging: boolean;
  };
}

export const getAIConfig = (): AIConfig => {
  // Validate required environment variables
  const openaiKey = process.env.OPENAI_API_KEY;
  const anthropicKey = process.env.ANTHROPIC_API_KEY;

  if (!openaiKey) {
    console.error('❌ OPENAI_API_KEY environment variable is required');
    throw new Error('OPENAI_API_KEY environment variable is required');
  }

  if (!anthropicKey) {
    console.error('❌ ANTHROPIC_API_KEY environment variable is required');
    throw new Error('ANTHROPIC_API_KEY environment variable is required');
  }

  // Validate key formats
  if (!openaiKey.startsWith('sk-')) {
    console.error('❌ Invalid OpenAI API key format - must start with sk-');
    throw new Error('Invalid OpenAI API key format');
  }

  if (!anthropicKey.startsWith('sk-ant-')) {
    console.error('❌ Invalid Anthropic API key format - must start with sk-ant-');
    throw new Error('Invalid Anthropic API key format');
  }

  const environment = process.env.NODE_ENV || 'development';
  const isProduction = environment === 'production';

  // PRODUCTION SAFETY: Use cheaper models initially to control costs
  const openaiModel = isProduction ? 'gpt-3.5-turbo' : 'gpt-3.5-turbo'; // Start with cheaper model
  const anthropicModel = isProduction ? 'claude-3-haiku-20240307' : 'claude-3-haiku-20240307'; // Start with cheapest

  console.log(`🔧 AI Config: ${environment} mode`);
  console.log(`🤖 OpenAI Model: ${openaiModel}`);
  console.log(`🤖 Anthropic Model: ${anthropicModel}`);

  return {
    openai: {
      apiKey: openaiKey,
      organization: process.env.OPENAI_ORG_ID,
      defaultModel: openaiModel,
      maxTokens: isProduction ? 2000 : 1500, // Reduced for cost control
      temperature: 0.3,
    },
    anthropic: {
      apiKey: anthropicKey,
      defaultModel: anthropicModel,
      maxTokens: isProduction ? 2000 : 1500, // Reduced for cost control
      temperature: 0.2,
    },
    features: {
      retryAttempts: isProduction ? 2 : 3, // Fewer retries in production
      retryDelay: 1000, // 1 second base delay
      timeoutMs: 45000, // Reduced timeout for faster failures
      enableCostTracking: true,
      enableUsageLogging: true,
    },
  };
};

// Model pricing (per 1K tokens)
export const MODEL_PRICING = {
  openai: {
    'gpt-4-turbo-preview': { input: 0.01, output: 0.03 },
    'gpt-4': { input: 0.03, output: 0.06 },
    'gpt-3.5-turbo': { input: 0.0005, output: 0.0015 },
  },
  anthropic: {
    'claude-3-opus-20240229': { input: 0.015, output: 0.075 },
    'claude-3-sonnet-20240229': { input: 0.003, output: 0.015 },
    'claude-3-haiku-20240307': { input: 0.00025, output: 0.00125 },
  },
} as const;

export const calculateCost = (
  provider: 'openai' | 'anthropic',
  model: string,
  inputTokens: number,
  outputTokens: number
): number => {
  const providerPricing = MODEL_PRICING[provider];
  const pricing = providerPricing?.[model as keyof typeof providerPricing];

  if (!pricing || typeof pricing !== 'object' || !('input' in pricing) || !('output' in pricing)) {
    console.warn(`No pricing data for ${provider}:${model}`);
    return 0;
  }

  // Type assertion to help TypeScript understand the structure
  const pricingData = pricing as { input: number; output: number };
  const inputCost = (inputTokens / 1000) * pricingData.input;
  const outputCost = (outputTokens / 1000) * pricingData.output;

  return inputCost + outputCost;
};

// Environment validation
export const validateEnvironment = (): void => {
  try {
    getAIConfig();
    console.log('✅ AI configuration validated successfully');
  } catch (error) {
    console.error('❌ AI configuration validation failed:', error);
    throw error;
  }
};

// Development helpers
export const isDevelopment = (): boolean => {
  return process.env.NODE_ENV === 'development';
};

export const isProduction = (): boolean => {
  return process.env.NODE_ENV === 'production';
};

export const getModelForEnvironment = (provider: 'openai' | 'anthropic'): string => {
  const config = getAIConfig();
  return provider === 'openai' ? config.openai.defaultModel : config.anthropic.defaultModel;
};
