import { NextRequest, NextResponse } from 'next/server';
import { logRateLimitViolation, getClientInfo } from '@/lib/security/monitoring';

// Simple in-memory rate limiter for basic protection
// In production, consider using Redis or a dedicated service
class RateLimiter {
  private requests: Map<string, { count: number; resetTime: number }> = new Map();
  private readonly windowMs: number;
  private readonly maxRequests: number;

  constructor(windowMs: number = 60000, maxRequests: number = 10) {
    this.windowMs = windowMs;
    this.maxRequests = maxRequests;
  }

  isAllowed(identifier: string): { allowed: boolean; resetTime?: number; remaining?: number } {
    const now = Date.now();
    const record = this.requests.get(identifier);

    // Clean up expired records
    if (record && now > record.resetTime) {
      this.requests.delete(identifier);
    }

    const currentRecord = this.requests.get(identifier);

    if (!currentRecord) {
      // First request in window
      this.requests.set(identifier, {
        count: 1,
        resetTime: now + this.windowMs
      });
      return { allowed: true, remaining: this.maxRequests - 1 };
    }

    if (currentRecord.count >= this.maxRequests) {
      return {
        allowed: false,
        resetTime: currentRecord.resetTime,
        remaining: 0
      };
    }

    // Increment count
    currentRecord.count++;
    this.requests.set(identifier, currentRecord);

    return {
      allowed: true,
      remaining: this.maxRequests - currentRecord.count
    };
  }

  // Clean up old entries periodically
  cleanup() {
    const now = Date.now();
    for (const [key, record] of this.requests.entries()) {
      if (now > record.resetTime) {
        this.requests.delete(key);
      }
    }
  }
}

// Different rate limiters for different endpoints
const rateLimiters = {
  // AI assessment endpoint - more restrictive
  assessment: new RateLimiter(60000, 5), // 5 requests per minute

  // File upload endpoint - moderate
  upload: new RateLimiter(60000, 10), // 10 requests per minute

  // General API endpoints
  general: new RateLimiter(60000, 30), // 30 requests per minute

  // Authentication endpoints
  auth: new RateLimiter(300000, 5), // 5 requests per 5 minutes
};

// Clean up old entries every 5 minutes
setInterval(() => {
  Object.values(rateLimiters).forEach(limiter => limiter.cleanup());
}, 300000);

export function getRateLimiter(endpoint: keyof typeof rateLimiters) {
  return rateLimiters[endpoint] || rateLimiters.general;
}

export function getClientIdentifier(request: NextRequest): string {
  // Try to get user ID from auth token first
  const authHeader = request.headers.get('authorization');
  if (authHeader) {
    try {
      // Extract user ID from JWT token (simplified)
      const token = authHeader.replace('Bearer ', '');
      // In a real implementation, you'd decode the JWT properly
      return `user:${token.substring(0, 10)}`;
    } catch {
      // Fall back to IP if token parsing fails
    }
  }

  // Fall back to IP address
  const forwarded = request.headers.get('x-forwarded-for');
  const ip = forwarded ? forwarded.split(',')[0] :
             request.headers.get('x-real-ip') ||
             'unknown';

  return `ip:${ip}`;
}

export function createRateLimitResponse(resetTime: number): NextResponse {
  const retryAfter = Math.ceil((resetTime - Date.now()) / 1000);

  return NextResponse.json(
    {
      error: 'Rate limit exceeded',
      message: 'Too many requests. Please try again later.',
      retryAfter
    },
    {
      status: 429,
      headers: {
        'Retry-After': retryAfter.toString(),
        'X-RateLimit-Limit': '10',
        'X-RateLimit-Remaining': '0',
        'X-RateLimit-Reset': resetTime.toString()
      }
    }
  );
}

// Middleware function to apply rate limiting
export function withRateLimit(
  handler: (request: NextRequest) => Promise<NextResponse>,
  endpoint: keyof typeof rateLimiters = 'general'
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const identifier = getClientIdentifier(request);
    const limiter = getRateLimiter(endpoint);
    const result = limiter.isAllowed(identifier);

    if (!result.allowed && result.resetTime) {
      // Log rate limit violation for security monitoring
      const { ip, userAgent } = getClientInfo(request);
      logRateLimitViolation(ip, endpoint, userAgent);

      console.warn(`Rate limit exceeded for ${identifier} on ${endpoint} endpoint`);
      return createRateLimitResponse(result.resetTime);
    }

    // Add rate limit headers to successful responses
    const response = await handler(request);

    if (result.remaining !== undefined) {
      response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
    }

    return response;
  };
}
