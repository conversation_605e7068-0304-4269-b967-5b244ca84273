# Accessing Your Nhost Database Schema

To plan and build pages based on your database schema, you need to access and understand the structure of your Nhost database. This guide explains how to access your schema through the Nhost Dashboard.

## Accessing Schema through Nhost Dashboard

1. **Log in to your Nhost account** at [https://app.nhost.io](https://app.nhost.io)

2. **Select your project** from the dashboard

3. **Navigate to the Database section** in the left sidebar

4. **Access the Hasura Console** by clicking on "Open Hasura Console" button

5. **Explore your schema** through the "Data" tab in the Hasura Console:
   - Tables: View all your database tables
   - Relationships: See how tables are related to each other
   - Permissions: Check access rules for different user roles

## Understanding Your Schema

In the Hasura Console, you can:

1. **View Table Structure**:
   - Click on any table to see its columns, data types, and constraints
   - Examine primary keys, foreign keys, and unique constraints

2. **Explore Relationships**:
   - See how tables are connected through foreign keys
   - Understand one-to-one, one-to-many, and many-to-many relationships

3. **Check Permissions**:
   - View and modify row-level and column-level permissions
   - Understand what data different user roles can access

4. **Test GraphQL Queries**:
   - Use the "API" tab to test GraphQL queries against your schema
   - Explore available queries, mutations, and subscriptions

## Exporting Schema Information

To have a local reference of your schema:

1. **Take Screenshots** of important tables and relationships

2. **Export GraphQL Schema**:
   - Go to the "API" tab
   - Click on "Schema" in the Explorer panel
   - Use the "Export Schema" option to download the GraphQL schema

3. **Document Table Structures**:
   - Create a spreadsheet or document listing all tables
   - Note column names, data types, and relationships

## Planning Page Builds Based on Schema

Once you have access to your schema:

1. **Identify Key Entities** that will need dedicated pages (e.g., users, products, orders)

2. **Map Relationships** to understand how data should flow between pages

3. **Plan Data Access Patterns** based on permissions and user roles

4. **Design Forms and Inputs** based on column data types and constraints

5. **Create Component Hierarchy** that mirrors your data structure

By thoroughly understanding your schema, you can build pages that accurately reflect your data model and provide the right functionality for your users.
