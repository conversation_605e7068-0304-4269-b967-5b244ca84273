# Setting Up Hasura Permissions

This guide will help you set up the necessary permissions in Hasura to make your tables accessible through the GraphQL API.

## Understanding Roles in Hasura

Hasura has several built-in roles:

1. **admin** - Has full access to all tables and operations
2. **user** - Authenticated users (with a valid JWT token)
3. **public** - Unauthenticated users

## Setting Up Permissions for Each Table

For each table in your database, you need to set up appropriate permissions for each role. Here's how to do it:

1. Go to the **Data** tab in Hasura Console
2. Select the table you want to set permissions for
3. Click on the **Permissions** tab
4. Configure permissions for each role

### Example Permission Setup for a Table

Let's say you have a table called `assignments`. Here's how you might set up permissions:

#### For the `user` role:

1. **Select (read) permissions**:
   - Row select permission: `{"creator_id": {"_eq": "X-Hasura-User-Id"}}` (users can only see assignments they created)
   - Column select permissions: Allow all columns

2. **Insert permissions**:
   - Row insert permission: `{"creator_id": {"_eq": "X-Hasura-User-Id"}}` (users can only insert rows where they are the creator)
   - Column insert permissions: Allow all columns except system columns
   - Column presets: Set `creator_id` to `X-Hasura-User-Id`

3. **Update permissions**:
   - Row update permission: `{"creator_id": {"_eq": "X-Hasura-User-Id"}}` (users can only update their own assignments)
   - Column update permissions: Allow all columns except system columns and creator_id

4. **Delete permissions**:
   - Row delete permission: `{"creator_id": {"_eq": "X-Hasura-User-Id"}}` (users can only delete their own assignments)

## Common Permission Patterns

### Owner-based permissions

```json
{
  "user_id": {
    "_eq": "X-Hasura-User-Id"
  }
}
```

### Organization-based permissions

```json
{
  "organization": {
    "members": {
      "user_id": {
        "_eq": "X-Hasura-User-Id"
      }
    }
  }
}
```

### Role-based permissions

```json
{
  "_or": [
    {
      "creator_id": {
        "_eq": "X-Hasura-User-Id"
      }
    },
    {
      "organization": {
        "members": {
          "user_id": {
            "_eq": "X-Hasura-User-Id"
          },
          "role": {
            "_eq": "admin"
          }
        }
      }
    }
  ]
}
```

## Setting Up Permissions for Your Schema

Based on the tables you have in your database, here are the recommended permission settings:

### For Tables Related to Organizations and Courses

1. **Organizations Table**:
   - User role: 
     - Select: Allow users to see organizations they are members of
     - Insert: Allow users to create new organizations
     - Update: Allow users to update organizations they are admins of
     - Delete: Allow users to delete organizations they are admins of

2. **Courses Table**:
   - User role:
     - Select: Allow users to see courses in organizations they are members of
     - Insert: Allow users to create courses in organizations they are admins or teachers in
     - Update: Allow users to update courses they created or are admins for
     - Delete: Allow users to delete courses they created or are admins for

### For Tables Related to Assignments and Submissions

1. **Assignments Table**:
   - User role:
     - Select: Allow users to see assignments in courses they are members of
     - Insert: Allow users to create assignments in courses they are teachers or admins for
     - Update: Allow users to update assignments they created
     - Delete: Allow users to delete assignments they created

2. **Submissions Table**:
   - User role:
     - Select: Allow students to see their own submissions, teachers to see submissions for their assignments
     - Insert: Allow students to create submissions for assignments in their courses
     - Update: Allow students to update their own submissions before the due date
     - Delete: Allow students to delete their own submissions before the due date

### For Tables Related to Assessments

1. **Assessments Table**:
   - User role:
     - Select: Allow teachers to see assessments for submissions to their assignments, students to see assessments of their submissions
     - Insert: Allow teachers to create assessments for submissions to their assignments
     - Update: Allow teachers to update assessments they created
     - Delete: Allow teachers to delete assessments they created

## Testing Your Permissions

After setting up permissions, you should test them to ensure they work as expected:

1. Run the GraphQL Codegen again to generate updated TypeScript types
2. Create a simple query in your application to fetch data from one of your tables
3. Test the query with different user roles to ensure permissions are working correctly

## Troubleshooting

If you're still seeing "no_queries_available" in your GraphQL schema:

1. Make sure you've set up permissions for the appropriate role
2. Check that you're authenticated with the correct role when making requests
3. Verify that your JWT token includes the necessary claims (e.g., X-Hasura-User-Id)
4. Check the Hasura logs for any permission-related errors
