import React from 'react';

interface SelectOption {
  value: string;
  label: string;
}

interface SelectProps {
  id: string;
  name: string;
  options: SelectOption[];
  value: string;
  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  placeholder?: string;
  required?: boolean;
  className?: string;
}

export default function Select({
  id,
  name,
  options,
  value,
  onChange,
  placeholder = 'Select an option',
  required = false,
  className = '',
}: SelectProps) {
  return (
    <select
      id={id}
      name={name}
      value={value}
      onChange={onChange}
      required={required}
      className={`block w-full rounded-md border border-gray-300 py-2 px-3 bg-white shadow-sm focus:ring-2 focus:ring-accent focus:border-accent ${className}`}
    >
      <option value="" disabled>
        {placeholder}
      </option>
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
}
