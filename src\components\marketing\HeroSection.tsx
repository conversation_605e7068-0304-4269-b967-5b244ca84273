'use client';

import Link from 'next/link';
import Button from '../ui/Button';
import { useAuthenticationStatus } from '@nhost/nextjs';

export default function HeroSection() {
  const { isAuthenticated } = useAuthenticationStatus();

  return (
    <section className="relative overflow-hidden">
      {/* Background gradient - green to ivory */}
      <div
        className="absolute top-0 right-0 w-full h-full -z-10"
        style={{
          background: 'linear-gradient(135deg, rgba(163, 189, 184, 0.3) 0%, rgba(245, 242, 234, 1) 100%)',
        }}
      ></div>

      <div className="max-w-7xl mx-auto px-6 py-20 md:py-32">
        <div className="max-w-xl">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-light mb-6">
            AI-Powered <br />Grading & Assessment
          </h1>
          <p className="text-lg mb-8 max-w-md">
            Streamline your grading process with our AI-powered assessment platform. Save time and provide consistent feedback to your students.
          </p>
          <div className="flex flex-wrap gap-4">
            {isAuthenticated ? (
              <Link href="/dashboard">
                <Button variant="primary" className="text-base">
                  Go to Dashboard
                </Button>
              </Link>
            ) : (
              <Link href="/auth/sign-up">
                <Button variant="primary" className="text-base">
                  Get Started
                </Button>
              </Link>
            )}
          </div>
        </div>

        {/* Right side content removed */}
      </div>
    </section>
  );
}
