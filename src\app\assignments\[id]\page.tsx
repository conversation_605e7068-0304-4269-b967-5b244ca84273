'use client';

import { useEffect, useState } from 'react';
import { useAuthenticationStatus } from '@nhost/nextjs';
import { useRouter } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import Button from '@/components/ui/Button';
import Link from 'next/link';

export default function AssignmentDetailsPage({ params }: { params: { id: string } }) {
  const { isAuthenticated, isLoading } = useAuthenticationStatus();
  const router = useRouter();
  const assignmentId = params.id;

  // State for assignment data
  const [assignment, setAssignment] = useState<any>(null);
  const [assignmentLoading, setAssignmentLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch assignment details using API route (bypasses allowlist)
  useEffect(() => {
    if (!isAuthenticated || !assignmentId) {
      setAssignmentLoading(false);
      return;
    }

    const fetchAssignment = async () => {
      try {
        setAssignmentLoading(true);
        setError(null);

        console.log('🔍 Fetching assignment details via API route...');

        const response = await fetch(`/api/assignments/${assignmentId}`);
        const result = await response.json();

        if (response.ok && result.success) {
          setAssignment(result.assignment);
          console.log('✅ Assignment fetched successfully:', result.assignment.id);
        } else {
          throw new Error(result.error || 'Failed to fetch assignment');
        }
      } catch (err) {
        console.error('❌ Failed to fetch assignment:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setAssignmentLoading(false);
      }
    };

    fetchAssignment();
  }, [isAuthenticated, assignmentId]);

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/sign-in');
    }
  }, [isLoading, isAuthenticated, router]);

  if (isLoading || assignmentLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[50vh]">
          <p>Loading...</p>
        </div>
      </DashboardLayout>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect in the useEffect
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-light mb-4">Error Loading Assignment</h2>
          <p className="mb-6 text-red-600">{error}</p>
          <Link href="/assignments">
            <Button variant="primary">Back to Assignments</Button>
          </Link>
        </div>
      </DashboardLayout>
    );
  }

  if (!assignment) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-light mb-4">Assignment Not Found</h2>
          <p className="mb-6">The assignment you're looking for doesn't exist or you don't have permission to view it.</p>
          <Link href="/assignments">
            <Button variant="primary">Back to Assignments</Button>
          </Link>
        </div>
      </DashboardLayout>
    );
  }



  return (
    <DashboardLayout>
      <div className="mb-8">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-light mb-2">{assignment.title}</h1>
          <Link href={`/assignments/${assignmentId}/edit`}>
            <Button variant="outline">Edit Assignment</Button>
          </Link>
        </div>

      </div>



      <div className="bg-white rounded-lg shadow p-6 mb-8">
        <h2 className="text-xl font-medium mb-4">Description</h2>
        <div className="prose max-w-none">
          <p className="whitespace-pre-line">{assignment.description}</p>
        </div>
      </div>

      <div className="flex gap-4">
        <Link href={`/student-upload?assignment=${assignmentId}`}>
          <Button variant="primary">Create Submission</Button>
        </Link>
        <Link href="/assignments">
          <Button variant="outline">Back to Assignments</Button>
        </Link>
      </div>
    </DashboardLayout>
  );
}
