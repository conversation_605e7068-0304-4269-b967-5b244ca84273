import {
  AssessmentConfig,
  AssessmentResult,
  Submission,
  AIProviderResponse,
  ProcessingStatus,
  AIAssessmentError,
  RateLimitError
} from '@/types/ai-assessment';
import { OpenAIProvider } from './providers/OpenAIProvider';
import { AnthropicProvider } from './providers/AnthropicProvider';
import { getAIConfig } from './config';
import { intelligentRouter, enhanceConfigWithRouting } from './intelligent-routing';

export class AIService {
  private openaiProvider: OpenAIProvider;
  private anthropicProvider: AnthropicProvider;
  private config: ReturnType<typeof getAIConfig>;

  constructor() {
    this.config = getAIConfig();
    this.openaiProvider = new OpenAIProvider();
    this.anthropicProvider = new AnthropicProvider();
  }

  /**
   * Main assessment method with intelligent routing
   */
  async assessSubmission(
    submission: Submission,
    config: AssessmentConfig,
    useIntelligentRouting: boolean = true
  ): Promise<AssessmentResult> {
    console.log(`🚀 Starting assessment for submission ${submission.id}`);

    // Apply intelligent routing if enabled
    let enhancedConfig = config;
    if (useIntelligentRouting) {
      console.log(`🧠 Applying intelligent AI routing...`);
      enhancedConfig = enhanceConfigWithRouting(config, submission, 'balanced');
      console.log(`🎯 Routing decision: ${enhancedConfig.aiModel} model selected`);

      if (enhancedConfig.routingDecision) {
        console.log(`📊 Content analysis:`, enhancedConfig.routingDecision.contentAnalysis);
        console.log(`💡 Reasoning: ${enhancedConfig.routingDecision.reasoning.join(', ')}`);
      }
    }

    console.log(`📊 Using ${enhancedConfig.aiModel} model (${enhancedConfig.aiModel === 'standard' ? 'OpenAI' : 'Anthropic'})`);

    try {
      const response = await this.processWithRetry(submission, enhancedConfig);

      if (!response.success || !response.result) {
        throw new Error('Assessment failed: No result returned');
      }

      // Add routing information to result
      if (enhancedConfig.routingDecision && response.result) {
        response.result.routingInfo = {
          selectedProvider: enhancedConfig.routingDecision.selectedProvider,
          selectedModel: enhancedConfig.routingDecision.selectedModel,
          confidence: enhancedConfig.routingDecision.confidence,
          contentAnalysis: enhancedConfig.routingDecision.contentAnalysis
        };
      }

      // Log usage for cost tracking
      if (response.tokenUsage && this.config.features.enableUsageLogging) {
        await this.logUsage(response.tokenUsage, submission.id, enhancedConfig.aiModel);
      }

      return response.result;

    } catch (error) {
      console.error(`❌ Assessment failed for submission ${submission.id}:`, error);
      throw error;
    }
  }

  /**
   * Process assessment with retry logic
   */
  private async processWithRetry(
    submission: Submission,
    config: AssessmentConfig,
    attempt: number = 1
  ): Promise<AIProviderResponse> {
    try {
      const provider = config.aiModel === 'standard' ? this.openaiProvider : this.anthropicProvider;
      return await provider.assessSubmission(submission, config);

    } catch (error) {
      console.warn(`⚠️ Assessment attempt ${attempt} failed:`, error);

      if (attempt >= this.config.features.retryAttempts) {
        throw error;
      }

      if (this.isRetryableError(error)) {
        const delay = this.calculateRetryDelay(attempt, error);
        console.log(`🔄 Retrying in ${delay}ms (attempt ${attempt + 1}/${this.config.features.retryAttempts})`);
        
        await this.delay(delay);
        return this.processWithRetry(submission, config, attempt + 1);
      }

      throw error;
    }
  }

  /**
   * Determine if an error is retryable
   */
  private isRetryableError(error: any): boolean {
    if (error instanceof RateLimitError) {
      return true;
    }

    if (error instanceof AIAssessmentError) {
      return error.retryable;
    }

    // Network errors, timeouts, server errors
    const retryableStatuses = [408, 429, 500, 502, 503, 504];
    return retryableStatuses.includes(error.status);
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  private calculateRetryDelay(attempt: number, error?: any): number {
    let baseDelay = this.config.features.retryDelay;

    // Exponential backoff
    const exponentialDelay = baseDelay * Math.pow(2, attempt - 1);

    // If rate limited, respect the retry-after header
    if (error instanceof RateLimitError && error.retryAfter) {
      return Math.max(exponentialDelay, error.retryAfter * 1000);
    }

    // Add jitter to prevent thundering herd
    const jitter = Math.random() * 1000;
    
    return exponentialDelay + jitter;
  }

  /**
   * Delay utility
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Log usage for cost tracking
   */
  private async logUsage(tokenUsage: any, submissionId: string, aiModel: string): Promise<void> {
    try {
      // This would typically save to database

      // TODO: Implement actual database logging
      // await saveUsageRecord({
      //   submissionId,
      //   provider: aiModel === 'standard' ? 'openai' : 'anthropic',
      //   ...tokenUsage,
      //   timestamp: new Date()
      // });

    } catch (error) {
      console.error('Failed to log usage:', error);
      // Don't throw - usage logging failure shouldn't break assessment
    }
  }

  /**
   * Estimate processing time based on configuration
   */
  static estimateProcessingTime(config: AssessmentConfig): number {
    let baseTime = 30; // Base 30 seconds

    // Model complexity
    if (config.aiModel === 'comprehensive') {
      baseTime += 45; // Anthropic typically takes longer
    }

    // Priority adjustments
    switch (config.priority) {
      case 'high':
        baseTime *= 0.7;
        break;
      case 'urgent':
        baseTime *= 0.5;
        break;
    }

    // Feedback level
    switch (config.feedbackLevel) {
      case 'detailed':
        baseTime += 30;
        break;
      case 'brief':
        baseTime -= 10;
        break;
    }

    // Feature complexity
    const enabledFeatures = Object.values(config.features).filter(Boolean).length;
    baseTime += enabledFeatures * 5;

    // Criteria complexity
    baseTime += config.criteria.length * 3;

    return Math.max(baseTime, 15); // Minimum 15 seconds
  }

  /**
   * Validate configuration before processing
   */
  static validateConfig(config: AssessmentConfig): void {
    if (!config.submissionId) {
      throw new Error('Submission ID is required');
    }

    if (!config.criteria || config.criteria.length === 0) {
      throw new Error('At least one assessment criteria is required');
    }

    const totalWeight = config.criteria.reduce((sum, c) => sum + c.weight, 0);
    if (Math.abs(totalWeight - 100) > 0.01) {
      throw new Error(`Criteria weights must sum to 100%, got ${totalWeight}%`);
    }

    if (!['standard', 'comprehensive'].includes(config.aiModel)) {
      throw new Error('Invalid AI model specified');
    }
  }

  /**
   * Get provider status
   */
  async getProviderStatus(): Promise<{ openai: boolean; anthropic: boolean }> {
    const status = { openai: false, anthropic: false };

    try {
      // Simple test calls to check if providers are working
      // This would be implemented with lightweight test requests
      status.openai = true; // Placeholder
      status.anthropic = true; // Placeholder
    } catch (error) {
      console.error('Provider status check failed:', error);
    }

    return status;
  }
}
