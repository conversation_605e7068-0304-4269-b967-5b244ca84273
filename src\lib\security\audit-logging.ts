/**
 * Comprehensive Audit Logging System
 * Provides detailed audit trails, compliance logging, and security event tracking
 */

export interface AuditEvent {
  id: string;
  timestamp: Date;
  eventType: AuditEventType;
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  sessionId?: string;
  ip?: string;
  userAgent?: string;
  resource?: string;
  action: string;
  outcome: 'success' | 'failure' | 'error';
  details: Record<string, any>;
  metadata?: {
    requestId?: string;
    correlationId?: string;
    source?: string;
    environment?: string;
  };
}

export type AuditEventType = 
  | 'authentication'
  | 'authorization'
  | 'data_access'
  | 'data_modification'
  | 'file_upload'
  | 'file_download'
  | 'configuration_change'
  | 'security_event'
  | 'compliance_event'
  | 'system_event'
  | 'user_action'
  | 'api_access'
  | 'admin_action';

export interface AuditFilter {
  eventType?: AuditEventType[];
  severity?: string[];
  userId?: string;
  dateFrom?: Date;
  dateTo?: Date;
  outcome?: string[];
  resource?: string;
}

export interface ComplianceReport {
  period: { from: Date; to: Date };
  totalEvents: number;
  eventsByType: Record<AuditEventType, number>;
  securityIncidents: number;
  dataAccessEvents: number;
  failedAuthentications: number;
  suspiciousActivities: AuditEvent[];
  complianceScore: number;
}

/**
 * Audit Logger Class
 */
export class AuditLogger {
  private events: AuditEvent[] = [];
  private maxEvents: number = 10000;
  private retentionDays: number = 90;

  constructor(maxEvents: number = 10000, retentionDays: number = 90) {
    this.maxEvents = maxEvents;
    this.retentionDays = retentionDays;
    
    // Clean up old events periodically
    setInterval(() => this.cleanupOldEvents(), 24 * 60 * 60 * 1000); // Daily cleanup
  }

  /**
   * Log an audit event
   */
  logEvent(event: Omit<AuditEvent, 'id' | 'timestamp'>): void {
    const auditEvent: AuditEvent = {
      id: this.generateEventId(),
      timestamp: new Date(),
      ...event,
      metadata: {
        environment: process.env.NODE_ENV || 'unknown',
        source: 'audit-logger',
        ...event.metadata
      }
    };

    this.events.push(auditEvent);

    // Maintain size limit
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents);
    }

    // Log to console with appropriate level
    this.logToConsole(auditEvent);

    // In production, send to external logging service
    if (process.env.NODE_ENV === 'production') {
      this.sendToExternalLogger(auditEvent);
    }
  }

  /**
   * Query audit events
   */
  queryEvents(filter: AuditFilter = {}, limit: number = 100): AuditEvent[] {
    let filteredEvents = this.events;

    // Apply filters
    if (filter.eventType && filter.eventType.length > 0) {
      filteredEvents = filteredEvents.filter(e => filter.eventType!.includes(e.eventType));
    }

    if (filter.severity && filter.severity.length > 0) {
      filteredEvents = filteredEvents.filter(e => filter.severity!.includes(e.severity));
    }

    if (filter.userId) {
      filteredEvents = filteredEvents.filter(e => e.userId === filter.userId);
    }

    if (filter.dateFrom) {
      filteredEvents = filteredEvents.filter(e => e.timestamp >= filter.dateFrom!);
    }

    if (filter.dateTo) {
      filteredEvents = filteredEvents.filter(e => e.timestamp <= filter.dateTo!);
    }

    if (filter.outcome && filter.outcome.length > 0) {
      filteredEvents = filteredEvents.filter(e => filter.outcome!.includes(e.outcome));
    }

    if (filter.resource) {
      filteredEvents = filteredEvents.filter(e => e.resource?.includes(filter.resource!));
    }

    // Sort by timestamp (newest first) and limit
    return filteredEvents
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * Generate compliance report
   */
  generateComplianceReport(from: Date, to: Date): ComplianceReport {
    const events = this.queryEvents({ dateFrom: from, dateTo: to }, 10000);
    
    const eventsByType: Record<AuditEventType, number> = {
      authentication: 0,
      authorization: 0,
      data_access: 0,
      data_modification: 0,
      file_upload: 0,
      file_download: 0,
      configuration_change: 0,
      security_event: 0,
      compliance_event: 0,
      system_event: 0,
      user_action: 0,
      api_access: 0,
      admin_action: 0
    };

    let securityIncidents = 0;
    let failedAuthentications = 0;
    const suspiciousActivities: AuditEvent[] = [];

    events.forEach(event => {
      eventsByType[event.eventType]++;

      if (event.severity === 'high' || event.severity === 'critical') {
        securityIncidents++;
      }

      if (event.eventType === 'authentication' && event.outcome === 'failure') {
        failedAuthentications++;
      }

      if (this.isSuspiciousActivity(event)) {
        suspiciousActivities.push(event);
      }
    });

    // Calculate compliance score (0-100)
    const complianceScore = this.calculateComplianceScore(events, securityIncidents, failedAuthentications);

    return {
      period: { from, to },
      totalEvents: events.length,
      eventsByType,
      securityIncidents,
      dataAccessEvents: eventsByType.data_access + eventsByType.data_modification,
      failedAuthentications,
      suspiciousActivities,
      complianceScore
    };
  }

  /**
   * Get audit statistics
   */
  getAuditStatistics(): {
    totalEvents: number;
    eventsByType: Record<AuditEventType, number>;
    eventsBySeverity: Record<string, number>;
    recentEvents: AuditEvent[];
    oldestEvent?: Date;
    newestEvent?: Date;
  } {
    const eventsByType: Record<AuditEventType, number> = {
      authentication: 0,
      authorization: 0,
      data_access: 0,
      data_modification: 0,
      file_upload: 0,
      file_download: 0,
      configuration_change: 0,
      security_event: 0,
      compliance_event: 0,
      system_event: 0,
      user_action: 0,
      api_access: 0,
      admin_action: 0
    };

    const eventsBySeverity: Record<string, number> = {
      low: 0,
      medium: 0,
      high: 0,
      critical: 0
    };

    this.events.forEach(event => {
      eventsByType[event.eventType]++;
      eventsBySeverity[event.severity]++;
    });

    const sortedEvents = [...this.events].sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    return {
      totalEvents: this.events.length,
      eventsByType,
      eventsBySeverity,
      recentEvents: sortedEvents.slice(0, 10),
      oldestEvent: this.events.length > 0 ? sortedEvents[sortedEvents.length - 1].timestamp : undefined,
      newestEvent: this.events.length > 0 ? sortedEvents[0].timestamp : undefined
    };
  }

  /**
   * Export audit logs
   */
  exportAuditLogs(filter: AuditFilter = {}, format: 'json' | 'csv' = 'json'): string {
    const events = this.queryEvents(filter, 10000);

    if (format === 'csv') {
      return this.exportToCSV(events);
    }

    return JSON.stringify(events, null, 2);
  }

  private generateEventId(): string {
    return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private logToConsole(event: AuditEvent): void {
    const logLevel = this.getLogLevel(event.severity);
    console[logLevel](`📋 Audit Event [${event.eventType}]:`, {
      id: event.id,
      action: event.action,
      outcome: event.outcome,
      userId: event.userId,
      resource: event.resource,
      timestamp: event.timestamp.toISOString()
    });
  }

  private getLogLevel(severity: string): 'log' | 'warn' | 'error' {
    switch (severity) {
      case 'low': return 'log';
      case 'medium': return 'warn';
      case 'high':
      case 'critical': return 'error';
      default: return 'log';
    }
  }

  private async sendToExternalLogger(event: AuditEvent): Promise<void> {
    try {
      // In production, send to your logging service (e.g., Splunk, ELK, CloudWatch)
      // Example implementation:
      // await fetch('/api/external-audit-log', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(event)
      // });
      
      console.log('📋 Audit event logged:', event.id);
    } catch (error) {
      console.error('Failed to send audit event to external logger:', error);
    }
  }

  private cleanupOldEvents(): void {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.retentionDays);
    
    const originalLength = this.events.length;
    this.events = this.events.filter(event => event.timestamp > cutoffDate);
    
    if (this.events.length < originalLength) {
      console.log(`🧹 Cleaned up ${originalLength - this.events.length} old audit events`);
    }
  }

  private isSuspiciousActivity(event: AuditEvent): boolean {
    // Define suspicious activity patterns
    return (
      (event.eventType === 'authentication' && event.outcome === 'failure') ||
      (event.severity === 'high' || event.severity === 'critical') ||
      (event.eventType === 'security_event') ||
      (event.action.includes('admin') && event.outcome === 'failure')
    );
  }

  private calculateComplianceScore(
    events: AuditEvent[], 
    securityIncidents: number, 
    failedAuthentications: number
  ): number {
    let score = 100;

    // Deduct points for security incidents
    score -= Math.min(securityIncidents * 5, 30);

    // Deduct points for failed authentications
    score -= Math.min(failedAuthentications * 2, 20);

    // Bonus points for having audit events (shows monitoring is active)
    if (events.length > 0) {
      score += 10;
    }

    return Math.max(0, Math.min(100, score));
  }

  private exportToCSV(events: AuditEvent[]): string {
    const headers = ['ID', 'Timestamp', 'Event Type', 'Severity', 'User ID', 'Action', 'Outcome', 'Resource', 'IP'];
    const rows = events.map(event => [
      event.id,
      event.timestamp.toISOString(),
      event.eventType,
      event.severity,
      event.userId || '',
      event.action,
      event.outcome,
      event.resource || '',
      event.ip || ''
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }
}

// Global audit logger instance
export const auditLogger = new AuditLogger();

/**
 * Helper functions for common audit events
 */

export function logAuthentication(
  userId: string,
  outcome: 'success' | 'failure',
  ip?: string,
  userAgent?: string,
  details: Record<string, any> = {}
): void {
  auditLogger.logEvent({
    eventType: 'authentication',
    severity: outcome === 'failure' ? 'medium' : 'low',
    userId,
    ip,
    userAgent,
    action: 'user_login',
    outcome,
    details
  });
}

export function logDataAccess(
  userId: string,
  resource: string,
  action: string,
  outcome: 'success' | 'failure' | 'error',
  details: Record<string, any> = {}
): void {
  auditLogger.logEvent({
    eventType: 'data_access',
    severity: 'low',
    userId,
    resource,
    action,
    outcome,
    details
  });
}

export function logFileUpload(
  userId: string,
  fileName: string,
  fileSize: number,
  outcome: 'success' | 'failure' | 'error',
  ip?: string,
  details: Record<string, any> = {}
): void {
  auditLogger.logEvent({
    eventType: 'file_upload',
    severity: 'medium',
    userId,
    ip,
    resource: fileName,
    action: 'file_upload',
    outcome,
    details: { fileSize, ...details }
  });
}

export function logSecurityEvent(
  eventDescription: string,
  severity: 'low' | 'medium' | 'high' | 'critical',
  userId?: string,
  ip?: string,
  details: Record<string, any> = {}
): void {
  auditLogger.logEvent({
    eventType: 'security_event',
    severity,
    userId,
    ip,
    action: eventDescription,
    outcome: 'success',
    details
  });
}

export function logAdminAction(
  userId: string,
  action: string,
  resource: string,
  outcome: 'success' | 'failure' | 'error',
  details: Record<string, any> = {}
): void {
  auditLogger.logEvent({
    eventType: 'admin_action',
    severity: 'high',
    userId,
    resource,
    action,
    outcome,
    details
  });
}
