'use client';

import Link from 'next/link';
import Button from '../ui/Button';
import { useAuthenticationStatus } from '@nhost/nextjs';

interface CallToActionProps {
  title: string;
  description: string;
  buttonText: string;
  buttonLink: string;
}

export default function CallToAction({
  title,
  description,
  buttonText,
  buttonLink
}: CallToActionProps) {
  const { isAuthenticated } = useAuthenticationStatus();

  return (
    <section className="py-20 bg-accent/10">
      <div className="max-w-4xl mx-auto px-6 text-center">
        <h2 className="text-3xl md:text-4xl font-light mb-4">{title}</h2>
        <p className="text-lg mb-8 max-w-2xl mx-auto">{description}</p>

        {isAuthenticated ? (
          <div className="flex justify-center gap-4">
            <Link href="/dashboard">
              <Button variant="primary" className="text-base">
                Go to Dashboard
              </Button>
            </Link>
          </div>
        ) : (
          <Link href={buttonLink}>
            <Button variant="primary" className="text-base">
              {buttonText}
            </Button>
          </Link>
        )}
      </div>
    </section>
  );
}
