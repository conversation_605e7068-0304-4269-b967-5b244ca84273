interface TestimonialProps {
  quote: string;
  author: string;
  role?: string;
  avatarSrc?: string;
}

export default function Testimonial({ quote, author, role, avatarSrc }: TestimonialProps) {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-4xl mx-auto px-6 text-center">
        <div className="text-5xl mb-8">"</div>
        <blockquote className="text-2xl md:text-3xl font-light mb-8 leading-relaxed">
          {quote}
        </blockquote>
        <div className="flex items-center justify-center">
          {avatarSrc ? (
            <img 
              src={avatarSrc} 
              alt={author} 
              className="w-12 h-12 rounded-full mr-4"
            />
          ) : (
            <div className="w-12 h-12 rounded-full bg-lightGray mr-4 flex items-center justify-center">
              {author.charAt(0)}
            </div>
          )}
          <div className="text-left">
            <div className="font-medium">{author}</div>
            {role && <div className="text-gray-600 text-sm">{role}</div>}
          </div>
        </div>
      </div>
    </section>
  );
}
