'use client';

import MainLayout from '@/components/layout/MainLayout';
import HeroSection from '@/components/marketing/HeroSection';
import ValueProposition from '@/components/marketing/ValueProposition';
import FeatureHighlight from '@/components/marketing/FeatureHighlight';
import Testimonial from '@/components/marketing/Testimonial';
import CallToAction from '@/components/marketing/CallToAction';

export default function Home() {
  return (
    <MainLayout>
      <HeroSection />

      <ValueProposition
        values={[
          {
            title: 'Save Time',
            description: 'Reduce grading time by up to 70% with AI-powered assessments.',
            icon: '⏱️'
          },
          {
            title: 'Consistent Feedback',
            description: 'Provide standardized, criteria-based feedback for all students.',
            icon: '✓'
          },
          {
            title: 'Multiple Formats',
            description: 'Grade essays, code, images, and more with a single platform.',
            icon: '📄'
          }
        ]}
      />

      <FeatureHighlight
        title="Intelligent Assessment"
        description="Our AI analyzes student work against your custom criteria, providing detailed feedback and suggested grades. Save hours of manual grading while maintaining quality and consistency."
        imageSrc="/illustrations/intelligent-assessment.svg"
        imageAlt="AI analyzing student work with neural network visualization"
      />

      <FeatureHighlight
        title="Customizable Criteria"
        description="Create detailed rubrics and assessment criteria tailored to your specific needs. Our platform adapts to your teaching style and subject requirements."
        imageSrc="/illustrations/customizable-criteria.svg?v=2"
        imageAlt="Customizable assessment criteria and rubric builder"
        reverse={true}
      />

      <Testimonial
        quote="This platform has transformed how I grade assignments. What used to take hours now takes minutes, and the quality of feedback has improved dramatically."
        author="Dr. Sarah Johnson"
        role="Computer Science Professor"
      />

      <CallToAction
        title="Start Grading Smarter"
        description="Join thousands of educators saving time with AI-powered assessments."
        buttonText="Sign Up"
        buttonLink="/auth/sign-up"
      />
    </MainLayout>
  );
}
