'use client';

import { useState, useEffect } from 'react';
import { useAuthenticationStatus, useUserData } from '@nhost/nextjs';
import { useRouter, useSearchParams } from 'next/navigation';
import { useQuery, gql } from '@apollo/client';
import DashboardLayout from '@/components/layout/DashboardLayout';
import ProgressIndicator from '@/components/assessment/ProgressIndicator';
// Filtering removed - using direct submissions for real-time data
import { GET_ASSIGNMENT_SUBMISSIONS } from '@/lib/gql/submission-queries';

export default function BulkReviewPage() {
  const { isAuthenticated, isLoading } = useAuthenticationStatus();
  const user = useUserData();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get assignment ID from URL params
  const assignmentId = searchParams.get('assignmentId');

  // Query to get submissions for this assignment
  const { data: submissionsData, loading: submissionsLoading } = useQuery(
    GET_ASSIGNMENT_SUBMISSIONS,
    {
      variables: { assignment_id: assignmentId },
      skip: !assignmentId || !isAuthenticated,
      onCompleted: (data) => {
        console.log('📄 Bulk review submissions loaded:', data);
        console.log('📊 Number of submissions:', data?.student_submissions?.length || 0);
      }
    }
  );

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [estimatedTime, setEstimatedTime] = useState(0);

  // Load configuration from localStorage
  const [assessmentConfig, setAssessmentConfig] = useState<any>(null);

  useEffect(() => {
    const configData = localStorage.getItem('assessmentConfig');
    if (configData) {
      const config = JSON.parse(configData);
      setAssessmentConfig(config);
      console.log('📋 Loaded bulk assessment config:', config);
      
      // Calculate estimated time (2 minutes per submission)
      const submissionCount = submissionsData?.student_submissions?.length || 0;
      setEstimatedTime(submissionCount * 2);
    }
  }, [submissionsData]);

  // Handle bulk assessment submission
  const handleBulkSubmit = async () => {
    if (!assignmentId || !assessmentConfig) {
      alert('Missing assignment ID or configuration');
      return;
    }

    setIsSubmitting(true);
    try {
      console.log('🚀 Submitting bulk assessment...');
      console.log('📊 Number of submissions:', submissionsData?.student_submissions?.length);

      // Prepare bulk assessment configuration
      const bulkConfig = {
        assignmentId: assignmentId,
        aiModel: 'standard',
        priority: 'standard',
        feedbackLevel: 'detailed',
        assessmentType: 'comprehensive',
        criteria: assessmentConfig.criteria || [],
        features: {
          enableDetailedFeedback: true,
          enableSuggestions: true,
          enableConfidenceScoring: true
        }
      };

      console.log('📝 Bulk assessment config:', bulkConfig);

      // Submit to bulk assessment API
      const response = await fetch('/api/bulk-assess', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bulkConfig),
      });

      const result = await response.json();

      if (result.success) {
        console.log('✅ Bulk assessment submitted successfully!');
        alert(`✅ Bulk assessment started for ${submissionsData?.student_submissions?.length} submissions!`);

        // Redirect to bulk results page
        router.push(`/assessment/results?assignmentId=${assignmentId}`);
      } else {
        throw new Error(result.message || 'Bulk assessment submission failed');
      }

    } catch (error: any) {
      console.error('❌ Error submitting bulk assessment:', error);
      alert(`Failed to submit bulk assessment: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSaveDraft = async () => {
    try {
      console.log('Saving bulk assessment draft...');
      alert('Bulk assessment draft saved successfully!');
    } catch (error) {
      console.error('Error saving draft:', error);
      alert('Failed to save draft');
    }
  };

  if (isLoading || submissionsLoading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading bulk assessment review...</div>
        </div>
      </DashboardLayout>
    );
  }

  if (!isAuthenticated) {
    router.push('/auth/sign-in');
    return null;
  }

  const submissions = submissionsData?.student_submissions || [];
  const assignment = submissions[0]?.assignment;

  return (
    <DashboardLayout>
      <div className="max-w-6xl mx-auto p-6 space-y-8">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-semibold text-text mb-2">
            Bulk Assessment Review
          </h1>
          <p className="text-gray-600">
            Review and submit bulk assessment for {submissions.length} submissions
          </p>
        </div>

        {/* Progress Indicator */}
        <ProgressIndicator currentStep={3} />

        {/* Assignment Information */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-text mb-4">Assignment Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Assignment Title
              </label>
              <div className="text-gray-900">{assignment?.title || 'Unknown Assignment'}</div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Number of Submissions
              </label>
              <div className="text-gray-900 font-semibold text-accent">{submissions.length} files</div>
            </div>
          </div>
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <div className="text-gray-900">{assignment?.description || 'No description available'}</div>
          </div>
        </div>

        {/* Assessment Criteria Summary */}
        {assessmentConfig?.criteria && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-text mb-4">Assessment Criteria</h2>
            <div className="space-y-3">
              {assessmentConfig.criteria.map((criterion: any, index: number) => (
                <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <div>
                    <div className="font-medium text-gray-900">{criterion.name}</div>
                    <div className="text-sm text-gray-600">{criterion.description}</div>
                  </div>
                  <div className="bg-accent text-white px-3 py-1 rounded-full text-sm font-medium">
                    {criterion.weight}%
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Submissions Overview */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-text mb-4">Submissions Overview</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {submissions.map((submission: any) => (
              <div key={submission.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-accent text-white rounded-full flex items-center justify-center text-sm font-medium">
                    {submission.student_name?.charAt(0) || '?'}
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">{submission.student_name}</div>
                    <div className="text-sm text-gray-500">{submission.file_type?.toUpperCase()}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Processing Configuration */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-text mb-4">Processing Configuration</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">AI Model</label>
              <div className="text-gray-900">Standard (OpenAI)</div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Priority</label>
              <div className="text-gray-900">Standard</div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Feedback Level</label>
              <div className="text-gray-900">Detailed</div>
            </div>
          </div>
        </div>

        {/* Processing Time Estimate */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">Processing Time Estimate</h3>
          <div className="text-blue-800">
            <div className="text-2xl font-bold">{estimatedTime} minutes</div>
            <div className="text-sm">for {submissions.length} submissions</div>
          </div>
        </div>

        {/* Submit Actions */}
        <div className="flex justify-between pt-6">
          <button
            onClick={() => router.push('/assessment/configuration')}
            className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors flex items-center space-x-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            <span>Back to Configuration</span>
          </button>
          
          <div className="flex space-x-3">
            <button
              onClick={handleSaveDraft}
              className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Save as Draft
            </button>
            <button
              onClick={handleBulkSubmit}
              disabled={isSubmitting || submissions.length === 0}
              className="px-8 py-2 bg-accent text-white rounded-md hover:bg-[#9A7649] transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Processing...</span>
                </>
              ) : (
                <>
                  <span>🚀</span>
                  <span>Submit Bulk Assessment</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
