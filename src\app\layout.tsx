import type { <PERSON>ada<PERSON> } from 'next';
import { <PERSON><PERSON> } from 'next/font/google';
import './globals.css';

// Import the client provider components
import NhostClientProvider from '@/components/providers/NhostClientProvider';
import { ApolloClientProvider } from '@/components/providers/ApolloClientProvider';

const roboto = Roboto({
  weight: ['300', '400', '500', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-roboto',
});

export const metadata: Metadata = {
  title: "Rubriq - AI-Powered Grading & Assessment",
  description: "Streamline your grading process with our AI-powered assessment platform",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${roboto.variable} antialiased bg-background text-text`}
      >
        <NhostClientProvider>
          <ApolloClientProvider>
            {children}
          </ApolloClientProvider>
        </NhostClientProvider>
      </body>
    </html>
  );
}
