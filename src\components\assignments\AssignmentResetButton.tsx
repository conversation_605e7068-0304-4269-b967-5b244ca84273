/**
 * Assignment Reset Button Component
 * Provides clean slate functionality for assignment reuse
 */

import { useState } from 'react';
import { Trash2, RotateCcw } from 'lucide-react';
import { useAssignmentReset, confirmAssignmentReset } from '@/hooks/useAssignmentReset';

interface AssignmentResetButtonProps {
  assignmentId: string;
  assignmentTitle: string;
  submissionCount: number;
  variant?: 'button' | 'dropdown' | 'icon';
  size?: 'sm' | 'md' | 'lg';
  onResetComplete?: (clearedCount: number) => void;
  className?: string;
}

export default function AssignmentResetButton({
  assignmentId,
  assignmentTitle,
  submissionCount,
  variant = 'button',
  size = 'md',
  onResetComplete,
  className = ''
}: AssignmentResetButtonProps) {
  const [showOptions, setShowOptions] = useState(false);
  const { isResetting, error, resetAssignment, clearError } = useAssignmentReset();

  const handleReset = async () => {
    if (submissionCount === 0) {
      alert('No submissions to clear for this assignment.');
      return;
    }

    const confirmed = confirmAssignmentReset(assignmentTitle, submissionCount);
    if (!confirmed) return;

    await resetAssignment({
      assignmentId,
      onSuccess: (clearedCount) => {
        setShowOptions(false);
        onResetComplete?.(clearedCount);
        alert(`✅ Successfully deleted ${clearedCount} submissions. Assignment now has a clean slate!`);
      },
      onError: (error) => {
        alert(`❌ Failed to reset assignment: ${error.message}`);
      }
    });
  };

  // Size classes
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base'
  };

  // Icon variant
  if (variant === 'icon') {
    return (
      <button
        onClick={handleReset}
        disabled={isResetting || submissionCount === 0}
        className={`p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors ${
          isResetting ? 'opacity-50 cursor-not-allowed' : ''
        } ${className}`}
        title={`Clear Assignment (${submissionCount} submissions)`}
      >
        {isResetting ? (
          <RotateCcw className="w-4 h-4 animate-spin" />
        ) : (
          <Trash2 className="w-4 h-4" />
        )}
      </button>
    );
  }

  // Button variant
  if (variant === 'button') {
    return (
      <button
        onClick={handleReset}
        disabled={isResetting || submissionCount === 0}
        className={`
          flex items-center space-x-2 bg-red-500 text-white rounded-lg hover:bg-red-600
          transition-colors disabled:opacity-50 disabled:cursor-not-allowed
          ${sizeClasses[size]} ${className}
        `}
      >
        {isResetting ? (
          <>
            <RotateCcw className="w-4 h-4 animate-spin" />
            <span>Clearing...</span>
          </>
        ) : (
          <>
            <Trash2 className="w-4 h-4" />
            <span>Clear Assignment</span>
            {submissionCount > 0 && (
              <span className="bg-red-600 text-xs px-2 py-1 rounded-full">
                {submissionCount}
              </span>
            )}
          </>
        )}
      </button>
    );
  }

  // Dropdown variant (for integration into existing menus)
  return (
    <button
      onClick={handleReset}
      disabled={isResetting || submissionCount === 0}
      className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded"
    >
      <Trash2 className="w-4 h-4" />
      <span>Clear Submissions ({submissionCount})</span>
    </button>
  );
}


