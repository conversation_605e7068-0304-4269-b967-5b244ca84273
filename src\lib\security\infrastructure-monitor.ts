/**
 * Infrastructure Security Monitoring System
 * Provides comprehensive monitoring of infrastructure security, performance, and compliance
 */

import { secretManager } from './secret-management';
import { httpsMonitor } from './https-security';
import { auditLogger } from './audit-logging';
import { securityMonitor } from './monitoring';

export interface InfrastructureSecurityStatus {
  overall: {
    status: 'secure' | 'warning' | 'critical';
    score: number;
    lastChecked: Date;
  };
  secrets: {
    status: 'secure' | 'warning' | 'critical';
    totalSecrets: number;
    missingRequired: number;
    rotationNeeded: number;
  };
  https: {
    status: 'secure' | 'warning' | 'critical';
    httpsEnabled: boolean;
    hstsEnabled: boolean;
    certificateValid: boolean;
  };
  network: {
    status: 'secure' | 'warning' | 'critical';
    corsConfigured: boolean;
    rateLimitingActive: boolean;
    ddosProtection: boolean;
  };
  audit: {
    status: 'secure' | 'warning' | 'critical';
    totalEvents: number;
    securityIncidents: number;
    complianceScore: number;
  };
  environment: {
    status: 'secure' | 'warning' | 'critical';
    nodeEnv: string;
    productionReady: boolean;
    configurationValid: boolean;
  };
}

export interface SecurityAlert {
  id: string;
  timestamp: Date;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'secrets' | 'https' | 'network' | 'audit' | 'environment' | 'general';
  title: string;
  description: string;
  recommendation: string;
  resolved: boolean;
}

export interface ComplianceStatus {
  gdprCompliant: boolean;
  ccpaCompliant: boolean;
  hipaaCompliant: boolean;
  sox404Compliant: boolean;
  iso27001Compliant: boolean;
  overallScore: number;
  issues: string[];
  recommendations: string[];
}

/**
 * Infrastructure Security Monitor
 */
export class InfrastructureSecurityMonitor {
  private alerts: SecurityAlert[] = [];
  private lastCheck: Date = new Date();

  /**
   * Perform comprehensive security check
   */
  async performSecurityCheck(): Promise<InfrastructureSecurityStatus> {
    this.lastCheck = new Date();

    // Check secrets management
    const secretsStatus = await this.checkSecretsStatus();
    
    // Check HTTPS/TLS security
    const httpsStatus = await this.checkHTTPSStatus();
    
    // Check network security
    const networkStatus = await this.checkNetworkStatus();
    
    // Check audit logging
    const auditStatus = await this.checkAuditStatus();
    
    // Check environment configuration
    const environmentStatus = await this.checkEnvironmentStatus();

    // Calculate overall score and status
    const scores = [
      secretsStatus.score,
      httpsStatus.score,
      networkStatus.score,
      auditStatus.score,
      environmentStatus.score
    ];
    
    const overallScore = Math.round(scores.reduce((a, b) => a + b, 0) / scores.length);
    const overallStatus = this.getStatusFromScore(overallScore);

    return {
      overall: {
        status: overallStatus,
        score: overallScore,
        lastChecked: this.lastCheck
      },
      secrets: {
        status: this.getStatusFromScore(secretsStatus.score),
        totalSecrets: secretsStatus.totalSecrets,
        missingRequired: secretsStatus.missingRequired,
        rotationNeeded: secretsStatus.rotationNeeded
      },
      https: {
        status: this.getStatusFromScore(httpsStatus.score),
        httpsEnabled: httpsStatus.httpsEnabled,
        hstsEnabled: httpsStatus.hstsEnabled,
        certificateValid: httpsStatus.certificateValid
      },
      network: {
        status: this.getStatusFromScore(networkStatus.score),
        corsConfigured: networkStatus.corsConfigured,
        rateLimitingActive: networkStatus.rateLimitingActive,
        ddosProtection: networkStatus.ddosProtection
      },
      audit: {
        status: this.getStatusFromScore(auditStatus.score),
        totalEvents: auditStatus.totalEvents,
        securityIncidents: auditStatus.securityIncidents,
        complianceScore: auditStatus.complianceScore
      },
      environment: {
        status: this.getStatusFromScore(environmentStatus.score),
        nodeEnv: environmentStatus.nodeEnv,
        productionReady: environmentStatus.productionReady,
        configurationValid: environmentStatus.configurationValid
      }
    };
  }

  /**
   * Get current security alerts
   */
  getSecurityAlerts(): SecurityAlert[] {
    return this.alerts.filter(alert => !alert.resolved);
  }

  /**
   * Add security alert
   */
  addAlert(alert: Omit<SecurityAlert, 'id' | 'timestamp' | 'resolved'>): void {
    const newAlert: SecurityAlert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      resolved: false,
      ...alert
    };

    this.alerts.push(newAlert);

    // Log critical alerts
    if (alert.severity === 'critical' || alert.severity === 'high') {
      console.error(`🚨 Security Alert [${alert.severity.toUpperCase()}]:`, {
        title: alert.title,
        description: alert.description,
        category: alert.category
      });
    }

    // Keep only recent alerts
    if (this.alerts.length > 100) {
      this.alerts = this.alerts.slice(-100);
    }
  }

  /**
   * Resolve security alert
   */
  resolveAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolved = true;
      return true;
    }
    return false;
  }

  /**
   * Check compliance status
   */
  checkComplianceStatus(): ComplianceStatus {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // GDPR compliance checks
    const gdprCompliant = this.checkGDPRCompliance(issues, recommendations);
    
    // CCPA compliance checks
    const ccpaCompliant = this.checkCCPACompliance(issues, recommendations);
    
    // Basic security compliance
    const basicCompliant = this.checkBasicSecurityCompliance(issues, recommendations);

    // Calculate overall compliance score
    const complianceFactors = [gdprCompliant, ccpaCompliant, basicCompliant];
    const overallScore = Math.round((complianceFactors.filter(Boolean).length / complianceFactors.length) * 100);

    return {
      gdprCompliant,
      ccpaCompliant,
      hipaaCompliant: false, // Not applicable for this application
      sox404Compliant: false, // Not applicable for this application
      iso27001Compliant: basicCompliant,
      overallScore,
      issues,
      recommendations
    };
  }

  private async checkSecretsStatus(): Promise<{
    score: number;
    totalSecrets: number;
    missingRequired: number;
    rotationNeeded: number;
  }> {
    const validation = secretManager.validateSecrets();
    const stats = secretManager.getSecretStats();

    let score = 100;
    
    // Deduct points for missing secrets
    score -= validation.missingSecrets.length * 20;
    
    // Deduct points for validation errors
    score -= validation.errors.length * 10;

    // Add alerts for critical issues
    if (validation.missingSecrets.length > 0) {
      this.addAlert({
        severity: 'critical',
        category: 'secrets',
        title: 'Missing Required Secrets',
        description: `${validation.missingSecrets.length} required secrets are missing`,
        recommendation: 'Configure all required environment variables'
      });
    }

    return {
      score: Math.max(0, score),
      totalSecrets: stats.totalSecrets,
      missingRequired: validation.missingSecrets.length,
      rotationNeeded: 0 // Simplified for this implementation
    };
  }

  private async checkHTTPSStatus(): Promise<{
    score: number;
    httpsEnabled: boolean;
    hstsEnabled: boolean;
    certificateValid: boolean;
  }> {
    const httpsStatus = await httpsMonitor.monitorHTTPSSecurity();
    
    let score = httpsStatus.securityScore;

    if (!httpsStatus.httpsEnabled && process.env.NODE_ENV === 'production') {
      this.addAlert({
        severity: 'critical',
        category: 'https',
        title: 'HTTPS Not Enabled',
        description: 'HTTPS is not enabled in production environment',
        recommendation: 'Enable HTTPS for all production traffic'
      });
    }

    return {
      score,
      httpsEnabled: httpsStatus.httpsEnabled,
      hstsEnabled: httpsStatus.hstsEnabled,
      certificateValid: true // Simplified
    };
  }

  private async checkNetworkStatus(): Promise<{
    score: number;
    corsConfigured: boolean;
    rateLimitingActive: boolean;
    ddosProtection: boolean;
  }> {
    let score = 100;
    
    // Check CORS configuration
    const corsConfigured = process.env.NEXT_PUBLIC_SITE_URL !== undefined;
    if (!corsConfigured) {
      score -= 20;
      this.addAlert({
        severity: 'medium',
        category: 'network',
        title: 'CORS Not Properly Configured',
        description: 'CORS origins are not properly configured',
        recommendation: 'Configure CORS origins for production'
      });
    }

    // Rate limiting is implemented
    const rateLimitingActive = true; // We have rate limiting middleware
    
    // DDoS protection (simplified check)
    const ddosProtection = process.env.NODE_ENV === 'production'; // Assume cloud provider handles this

    return {
      score,
      corsConfigured,
      rateLimitingActive,
      ddosProtection
    };
  }

  private async checkAuditStatus(): Promise<{
    score: number;
    totalEvents: number;
    securityIncidents: number;
    complianceScore: number;
  }> {
    const stats = auditLogger.getAuditStatistics();
    const securityIncidents = stats.eventsBySeverity.high + stats.eventsBySeverity.critical;
    
    let score = 100;
    
    // Deduct points for security incidents
    score -= Math.min(securityIncidents * 5, 30);

    // Bonus for having audit events (shows monitoring is active)
    if (stats.totalEvents > 0) {
      score += 10;
    }

    return {
      score: Math.max(0, Math.min(100, score)),
      totalEvents: stats.totalEvents,
      securityIncidents,
      complianceScore: score
    };
  }

  private async checkEnvironmentStatus(): Promise<{
    score: number;
    nodeEnv: string;
    productionReady: boolean;
    configurationValid: boolean;
  }> {
    const nodeEnv = process.env.NODE_ENV || 'unknown';
    let score = 100;
    
    // Check if environment is properly configured
    const productionReady = nodeEnv === 'production' && 
                           process.env.NEXT_PUBLIC_NHOST_SUBDOMAIN !== undefined &&
                           process.env.OPENAI_API_KEY !== undefined;

    if (nodeEnv === 'production' && !productionReady) {
      score -= 50;
      this.addAlert({
        severity: 'critical',
        category: 'environment',
        title: 'Production Environment Not Ready',
        description: 'Production environment is missing required configuration',
        recommendation: 'Configure all required environment variables for production'
      });
    }

    const configurationValid = secretManager.validateSecrets().isValid;
    if (!configurationValid) {
      score -= 30;
    }

    return {
      score,
      nodeEnv,
      productionReady,
      configurationValid
    };
  }

  private getStatusFromScore(score: number): 'secure' | 'warning' | 'critical' {
    if (score >= 80) return 'secure';
    if (score >= 60) return 'warning';
    return 'critical';
  }

  private checkGDPRCompliance(issues: string[], recommendations: string[]): boolean {
    let compliant = true;

    // Check for audit logging (required for GDPR)
    const auditStats = auditLogger.getAuditStatistics();
    if (auditStats.totalEvents === 0) {
      compliant = false;
      issues.push('No audit logging detected');
      recommendations.push('Implement comprehensive audit logging for GDPR compliance');
    }

    // Check for data encryption
    if (process.env.NODE_ENV === 'production' && !process.env.ENCRYPTION_KEY) {
      compliant = false;
      issues.push('Data encryption not configured');
      recommendations.push('Implement data encryption at rest and in transit');
    }

    return compliant;
  }

  private checkCCPACompliance(issues: string[], recommendations: string[]): boolean {
    // Similar to GDPR but with California-specific requirements
    return this.checkGDPRCompliance(issues, recommendations);
  }

  private checkBasicSecurityCompliance(issues: string[], recommendations: string[]): boolean {
    let compliant = true;

    // Check HTTPS in production
    if (process.env.NODE_ENV === 'production' && !process.env.NEXTAUTH_URL?.startsWith('https://')) {
      compliant = false;
      issues.push('HTTPS not enforced in production');
      recommendations.push('Enable HTTPS for all production traffic');
    }

    // Check for security headers
    // This is simplified - in reality you'd check actual HTTP responses
    
    return compliant;
  }
}

// Global infrastructure monitor instance
export const infrastructureMonitor = new InfrastructureSecurityMonitor();

/**
 * Helper functions
 */

export async function getInfrastructureStatus(): Promise<InfrastructureSecurityStatus> {
  return infrastructureMonitor.performSecurityCheck();
}

export function getSecurityAlerts(): SecurityAlert[] {
  return infrastructureMonitor.getSecurityAlerts();
}

export function getComplianceStatus(): ComplianceStatus {
  return infrastructureMonitor.checkComplianceStatus();
}

export function addSecurityAlert(alert: Omit<SecurityAlert, 'id' | 'timestamp' | 'resolved'>): void {
  infrastructureMonitor.addAlert(alert);
}
