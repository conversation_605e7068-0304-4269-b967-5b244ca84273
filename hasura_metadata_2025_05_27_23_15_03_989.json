{"resource_version": 43, "metadata": {"version": 3, "sources": [{"name": "default", "kind": "postgres", "tables": [{"table": {"name": "provider_requests", "schema": "auth"}, "configuration": {"column_config": {"id": {"custom_name": "id"}, "options": {"custom_name": "options"}}, "custom_column_names": {"id": "id", "options": "options"}, "custom_name": "authProviderRequests", "custom_root_fields": {"delete": "deleteAuthProviderRequests", "delete_by_pk": "deleteAuthProviderRequest", "insert": "insertAuthProviderRequests", "insert_one": "insertAuthProviderRequest", "select": "authProviderRequests", "select_aggregate": "authProviderRequestsAggregate", "select_by_pk": "authProviderRequest", "update": "updateAuthProviderRequests", "update_by_pk": "updateAuthProviderRequest"}}}, {"table": {"name": "providers", "schema": "auth"}, "configuration": {"column_config": {"id": {"custom_name": "id"}}, "custom_column_names": {"id": "id"}, "custom_name": "authProviders", "custom_root_fields": {"delete": "deleteAuthProviders", "delete_by_pk": "deleteAuthProvider", "insert": "insertAuthProviders", "insert_one": "insertAuthProvider", "select": "authProviders", "select_aggregate": "authProvidersAggregate", "select_by_pk": "authProvider", "update": "updateAuthProviders", "update_by_pk": "updateAuthProvider"}}, "array_relationships": [{"name": "userProviders", "using": {"foreign_key_constraint_on": {"column": "provider_id", "table": {"name": "user_providers", "schema": "auth"}}}}]}, {"table": {"name": "refresh_token_types", "schema": "auth"}, "is_enum": true, "configuration": {"column_config": {}, "custom_column_names": {}, "custom_name": "authRefreshTokenTypes", "custom_root_fields": {"delete": "deleteAuthRefreshTokenTypes", "delete_by_pk": "deleteAuthRefreshTokenType", "insert": "insertAuthRefreshTokenTypes", "insert_one": "insertAuthRefreshTokenType", "select": "authRefreshTokenTypes", "select_aggregate": "authRefreshTokenTypesAggregate", "select_by_pk": "authRefreshTokenType", "update": "updateAuthRefreshTokenTypes", "update_by_pk": "updateAuthRefreshTokenType"}}, "array_relationships": [{"name": "refreshTokens", "using": {"foreign_key_constraint_on": {"column": "type", "table": {"name": "refresh_tokens", "schema": "auth"}}}}]}, {"table": {"name": "refresh_tokens", "schema": "auth"}, "configuration": {"column_config": {"created_at": {"custom_name": "createdAt"}, "expires_at": {"custom_name": "expiresAt"}, "refresh_token_hash": {"custom_name": "refreshTokenHash"}, "user_id": {"custom_name": "userId"}}, "custom_column_names": {"created_at": "createdAt", "expires_at": "expiresAt", "refresh_token_hash": "refreshTokenHash", "user_id": "userId"}, "custom_name": "authRefreshTokens", "custom_root_fields": {"delete": "deleteAuthRefreshTokens", "delete_by_pk": "deleteAuthRefreshToken", "insert": "insertAuthRefreshTokens", "insert_one": "insertAuthRefreshToken", "select": "authRefreshTokens", "select_aggregate": "authRefreshTokensAggregate", "select_by_pk": "authRefreshToken", "update": "updateAuthRefreshTokens", "update_by_pk": "updateAuthRefreshToken"}}, "object_relationships": [{"name": "user", "using": {"foreign_key_constraint_on": "user_id"}}], "select_permissions": [{"role": "user", "permission": {"columns": ["id", "created_at", "expires_at", "metadata", "type", "user_id"], "filter": {"user_id": {"_eq": "X-Hasura-User-Id"}}}}], "delete_permissions": [{"role": "user", "permission": {"filter": {"_and": [{"user_id": {"_eq": "X-Hasura-User-Id"}}, {"type": {"_eq": "pat"}}]}}}]}, {"table": {"name": "roles", "schema": "auth"}, "configuration": {"column_config": {"role": {"custom_name": "role"}}, "custom_column_names": {"role": "role"}, "custom_name": "authRoles", "custom_root_fields": {"delete": "deleteAuthRoles", "delete_by_pk": "deleteAuthRole", "insert": "insertAuthRoles", "insert_one": "insertAuthRole", "select": "authRoles", "select_aggregate": "authRolesAggregate", "select_by_pk": "authRole", "update": "updateAuthRoles", "update_by_pk": "updateAuthRole"}}, "array_relationships": [{"name": "userRoles", "using": {"foreign_key_constraint_on": {"column": "role", "table": {"name": "user_roles", "schema": "auth"}}}}, {"name": "usersByDefaultRole", "using": {"foreign_key_constraint_on": {"column": "default_role", "table": {"name": "users", "schema": "auth"}}}}]}, {"table": {"name": "user_providers", "schema": "auth"}, "configuration": {"column_config": {"access_token": {"custom_name": "accessToken"}, "created_at": {"custom_name": "createdAt"}, "id": {"custom_name": "id"}, "provider_id": {"custom_name": "providerId"}, "provider_user_id": {"custom_name": "providerUserId"}, "refresh_token": {"custom_name": "refreshToken"}, "updated_at": {"custom_name": "updatedAt"}, "user_id": {"custom_name": "userId"}}, "custom_column_names": {"access_token": "accessToken", "created_at": "createdAt", "id": "id", "provider_id": "providerId", "provider_user_id": "providerUserId", "refresh_token": "refreshToken", "updated_at": "updatedAt", "user_id": "userId"}, "custom_name": "authUserProviders", "custom_root_fields": {"delete": "deleteAuthUserProviders", "delete_by_pk": "deleteAuthUserProvider", "insert": "insertAuthUserProviders", "insert_one": "insertAuthUserProvider", "select": "authUserProviders", "select_aggregate": "authUserProvidersAggregate", "select_by_pk": "authUserProvider", "update": "updateAuthUserProviders", "update_by_pk": "updateAuthUserProvider"}}, "object_relationships": [{"name": "provider", "using": {"foreign_key_constraint_on": "provider_id"}}, {"name": "user", "using": {"foreign_key_constraint_on": "user_id"}}]}, {"table": {"name": "user_roles", "schema": "auth"}, "configuration": {"column_config": {"created_at": {"custom_name": "createdAt"}, "id": {"custom_name": "id"}, "role": {"custom_name": "role"}, "user_id": {"custom_name": "userId"}}, "custom_column_names": {"created_at": "createdAt", "id": "id", "role": "role", "user_id": "userId"}, "custom_name": "authUserRoles", "custom_root_fields": {"delete": "deleteAuthUserRoles", "delete_by_pk": "deleteAuthUserRole", "insert": "insertAuthUserRoles", "insert_one": "insertAuthUserRole", "select": "authUserRoles", "select_aggregate": "authUserRolesAggregate", "select_by_pk": "authUserRole", "update": "updateAuthUserRoles", "update_by_pk": "updateAuthUserRole"}}, "object_relationships": [{"name": "roleByRole", "using": {"foreign_key_constraint_on": "role"}}, {"name": "user", "using": {"foreign_key_constraint_on": "user_id"}}]}, {"table": {"name": "user_security_keys", "schema": "auth"}, "configuration": {"column_config": {"credential_id": {"custom_name": "credentialId"}, "credential_public_key": {"custom_name": "credentialPublicKey"}, "id": {"custom_name": "id"}, "user_id": {"custom_name": "userId"}}, "custom_column_names": {"credential_id": "credentialId", "credential_public_key": "credentialPublicKey", "id": "id", "user_id": "userId"}, "custom_name": "authUserSecurityKeys", "custom_root_fields": {"delete": "deleteAuthUserSecurityKeys", "delete_by_pk": "deleteAuthUserSecurityKey", "insert": "insertAuthUserSecurityKeys", "insert_one": "insertAuthUserSecurityKey", "select": "authUserSecurityKeys", "select_aggregate": "authUserSecurityKeysAggregate", "select_by_pk": "authUserSecurityKey", "update": "updateAuthUserSecurityKeys", "update_by_pk": "updateAuthUserSecurityKey"}}, "object_relationships": [{"name": "user", "using": {"foreign_key_constraint_on": "user_id"}}]}, {"table": {"name": "users", "schema": "auth"}, "configuration": {"column_config": {"active_mfa_type": {"custom_name": "activeMfaType"}, "avatar_url": {"custom_name": "avatarUrl"}, "created_at": {"custom_name": "createdAt"}, "default_role": {"custom_name": "defaultRole"}, "disabled": {"custom_name": "disabled"}, "display_name": {"custom_name": "displayName"}, "email": {"custom_name": "email"}, "email_verified": {"custom_name": "emailVerified"}, "id": {"custom_name": "id"}, "is_anonymous": {"custom_name": "isAnonymous"}, "last_seen": {"custom_name": "lastSeen"}, "locale": {"custom_name": "locale"}, "new_email": {"custom_name": "newEmail"}, "otp_hash": {"custom_name": "otpHash"}, "otp_hash_expires_at": {"custom_name": "otpHashExpiresAt"}, "otp_method_last_used": {"custom_name": "otpMethodLastUsed"}, "password_hash": {"custom_name": "passwordHash"}, "phone_number": {"custom_name": "phoneNumber"}, "phone_number_verified": {"custom_name": "phoneNumberVerified"}, "ticket": {"custom_name": "ticket"}, "ticket_expires_at": {"custom_name": "ticketExpiresAt"}, "totp_secret": {"custom_name": "totpSecret"}, "updated_at": {"custom_name": "updatedAt"}, "webauthn_current_challenge": {"custom_name": "currentChallenge"}}, "custom_column_names": {"active_mfa_type": "activeMfaType", "avatar_url": "avatarUrl", "created_at": "createdAt", "default_role": "defaultRole", "disabled": "disabled", "display_name": "displayName", "email": "email", "email_verified": "emailVerified", "id": "id", "is_anonymous": "isAnonymous", "last_seen": "lastSeen", "locale": "locale", "new_email": "newEmail", "otp_hash": "otpHash", "otp_hash_expires_at": "otpHashExpiresAt", "otp_method_last_used": "otpMethodLastUsed", "password_hash": "passwordHash", "phone_number": "phoneNumber", "phone_number_verified": "phoneNumberVerified", "ticket": "ticket", "ticket_expires_at": "ticketExpiresAt", "totp_secret": "totpSecret", "updated_at": "updatedAt", "webauthn_current_challenge": "currentChallenge"}, "custom_name": "users", "custom_root_fields": {"delete": "deleteUsers", "delete_by_pk": "deleteUser", "insert": "insertUsers", "insert_one": "insertUser", "select": "users", "select_aggregate": "usersAggregate", "select_by_pk": "user", "update": "updateUsers", "update_by_pk": "updateUser"}}, "object_relationships": [{"name": "defaultRoleByRole", "using": {"foreign_key_constraint_on": "default_role"}}], "array_relationships": [{"name": "refreshTokens", "using": {"foreign_key_constraint_on": {"column": "user_id", "table": {"name": "refresh_tokens", "schema": "auth"}}}}, {"name": "roles", "using": {"foreign_key_constraint_on": {"column": "user_id", "table": {"name": "user_roles", "schema": "auth"}}}}, {"name": "securityKeys", "using": {"foreign_key_constraint_on": {"column": "user_id", "table": {"name": "user_security_keys", "schema": "auth"}}}}, {"name": "userProviders", "using": {"foreign_key_constraint_on": {"column": "user_id", "table": {"name": "user_providers", "schema": "auth"}}}}]}, {"table": {"name": "assessment_criteria", "schema": "public"}, "object_relationships": [{"name": "assignment", "using": {"foreign_key_constraint_on": "assignment_id"}}, {"name": "criteria_template", "using": {"foreign_key_constraint_on": "template_id"}}], "array_relationships": [{"name": "per_criterion_feedbacks", "using": {"foreign_key_constraint_on": {"column": "criterion_id", "table": {"name": "per_criterion_feedback", "schema": "public"}}}}], "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["order", "weight", "criterion_name", "description", "assignment_id", "id", "template_id"]}, "comment": ""}], "select_permissions": [{"role": "user", "permission": {"columns": ["order", "weight", "criterion_name", "description", "assignment_id", "id", "template_id"], "filter": {}}, "comment": ""}], "update_permissions": [{"role": "user", "permission": {"columns": ["order", "weight", "criterion_name", "description", "assignment_id", "id", "template_id"], "filter": {}, "check": {}}, "comment": ""}]}, {"table": {"name": "assessment_results", "schema": "public"}, "object_relationships": [{"name": "student_submission", "using": {"foreign_key_constraint_on": "submission_id"}}], "array_relationships": [{"name": "per_criterion_feedbacks", "using": {"foreign_key_constraint_on": {"column": "assessment_result_id", "table": {"name": "per_criterion_feedback", "schema": "public"}}}}], "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["reviewed", "exported_pdf_path", "general_comments", "overall_grade", "created_at", "id", "submission_id"]}, "comment": ""}], "select_permissions": [{"role": "user", "permission": {"columns": ["reviewed", "exported_pdf_path", "general_comments", "overall_grade", "created_at", "id", "submission_id"], "filter": {}}, "comment": ""}], "update_permissions": [{"role": "user", "permission": {"columns": ["reviewed", "exported_pdf_path", "general_comments", "overall_grade", "created_at", "id", "submission_id"], "filter": {}, "check": {}}, "comment": ""}]}, {"table": {"name": "assignments", "schema": "public"}, "object_relationships": [{"name": "user", "using": {"foreign_key_constraint_on": "user_id"}}], "array_relationships": [{"name": "assessment_criteria", "using": {"foreign_key_constraint_on": {"column": "assignment_id", "table": {"name": "assessment_criteria", "schema": "public"}}}}, {"name": "student_submissions", "using": {"foreign_key_constraint_on": {"column": "assignment_id", "table": {"name": "student_submissions", "schema": "public"}}}}], "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["description", "status", "title", "created_at", "id", "user_id"]}, "comment": ""}], "select_permissions": [{"role": "user", "permission": {"columns": ["description", "status", "title", "created_at", "id", "user_id"], "filter": {}}, "comment": ""}], "update_permissions": [{"role": "user", "permission": {"columns": ["description", "status", "title", "created_at", "id", "user_id"], "filter": {}, "check": {}}, "comment": ""}]}, {"table": {"name": "criteria_templates", "schema": "public"}, "object_relationships": [{"name": "user", "using": {"foreign_key_constraint_on": "user_id"}}], "array_relationships": [{"name": "assessment_criteria", "using": {"foreign_key_constraint_on": {"column": "template_id", "table": {"name": "assessment_criteria", "schema": "public"}}}}], "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["criteria", "template_name", "created_at", "id", "user_id"]}, "comment": ""}], "select_permissions": [{"role": "user", "permission": {"columns": ["criteria", "template_name", "created_at", "id", "user_id"], "filter": {}}, "comment": ""}], "update_permissions": [{"role": "user", "permission": {"columns": ["criteria", "template_name", "created_at", "id", "user_id"], "filter": {}, "check": null}, "comment": ""}]}, {"table": {"name": "per_criterion_feedback", "schema": "public"}, "object_relationships": [{"name": "assessment_criterium", "using": {"foreign_key_constraint_on": "criterion_id"}}, {"name": "assessment_result", "using": {"foreign_key_constraint_on": "assessment_result_id"}}], "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["score", "feedback_text", "assessment_result_id", "criterion_id", "id"]}, "comment": ""}], "select_permissions": [{"role": "user", "permission": {"columns": ["score", "feedback_text", "assessment_result_id", "criterion_id", "id"], "filter": {}}, "comment": ""}], "update_permissions": [{"role": "user", "permission": {"columns": ["score", "feedback_text", "assessment_result_id", "criterion_id", "id"], "filter": {}, "check": {}}, "comment": ""}]}, {"table": {"name": "student_submissions", "schema": "public"}, "object_relationships": [{"name": "assignment", "using": {"foreign_key_constraint_on": "assignment_id"}}], "array_relationships": [{"name": "assessment_results", "using": {"foreign_key_constraint_on": {"column": "submission_id", "table": {"name": "assessment_results", "schema": "public"}}}}], "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["file_path", "file_type", "processing_status", "student_name", "upload_date", "assignment_id", "id"]}, "comment": ""}], "select_permissions": [{"role": "user", "permission": {"columns": ["file_path", "file_type", "processing_status", "student_name", "upload_date", "assignment_id", "id"], "filter": {}}, "comment": ""}], "update_permissions": [{"role": "user", "permission": {"columns": ["file_path", "file_type", "processing_status", "student_name", "upload_date", "assignment_id", "id"], "filter": {}, "check": {}}, "comment": ""}]}, {"table": {"name": "buckets", "schema": "storage"}, "configuration": {"column_config": {"cache_control": {"custom_name": "cacheControl"}, "created_at": {"custom_name": "createdAt"}, "download_expiration": {"custom_name": "downloadExpiration"}, "id": {"custom_name": "id"}, "max_upload_file_size": {"custom_name": "maxUploadFileSize"}, "min_upload_file_size": {"custom_name": "minUploadFileSize"}, "presigned_urls_enabled": {"custom_name": "presignedUrlsEnabled"}, "updated_at": {"custom_name": "updatedAt"}}, "custom_column_names": {"cache_control": "cacheControl", "created_at": "createdAt", "download_expiration": "downloadExpiration", "id": "id", "max_upload_file_size": "maxUploadFileSize", "min_upload_file_size": "minUploadFileSize", "presigned_urls_enabled": "presignedUrlsEnabled", "updated_at": "updatedAt"}, "custom_name": "buckets", "custom_root_fields": {"delete": "deleteBuckets", "delete_by_pk": "deleteBucket", "insert": "insertBuckets", "insert_one": "insertBucket", "select": "buckets", "select_aggregate": "bucketsAggregate", "select_by_pk": "bucket", "update": "updateBuckets", "update_by_pk": "updateBucket"}}, "array_relationships": [{"name": "files", "using": {"foreign_key_constraint_on": {"column": "bucket_id", "table": {"name": "files", "schema": "storage"}}}}], "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["presigned_urls_enabled", "download_expiration", "max_upload_file_size", "min_upload_file_size", "cache_control", "id", "created_at", "updated_at"]}, "comment": ""}], "select_permissions": [{"role": "user", "permission": {"columns": ["presigned_urls_enabled", "download_expiration", "max_upload_file_size", "min_upload_file_size", "cache_control", "id", "created_at", "updated_at"], "filter": {}}, "comment": ""}], "update_permissions": [{"role": "user", "permission": {"columns": ["presigned_urls_enabled", "download_expiration", "max_upload_file_size", "min_upload_file_size", "cache_control", "id", "created_at", "updated_at"], "filter": {}, "check": {}}, "comment": ""}]}, {"table": {"name": "files", "schema": "storage"}, "configuration": {"column_config": {"bucket_id": {"custom_name": "bucketId"}, "created_at": {"custom_name": "createdAt"}, "etag": {"custom_name": "etag"}, "id": {"custom_name": "id"}, "is_uploaded": {"custom_name": "isUploaded"}, "metadata": {"custom_name": "metadata"}, "mime_type": {"custom_name": "mimeType"}, "name": {"custom_name": "name"}, "size": {"custom_name": "size"}, "updated_at": {"custom_name": "updatedAt"}, "uploaded_by_user_id": {"custom_name": "uploadedByUserId"}}, "custom_column_names": {"bucket_id": "bucketId", "created_at": "createdAt", "etag": "etag", "id": "id", "is_uploaded": "isUploaded", "metadata": "metadata", "mime_type": "mimeType", "name": "name", "size": "size", "updated_at": "updatedAt", "uploaded_by_user_id": "uploadedByUserId"}, "custom_name": "files", "custom_root_fields": {"delete": "deleteFiles", "delete_by_pk": "deleteFile", "insert": "insertFiles", "insert_one": "insertFile", "select": "files", "select_aggregate": "filesAggregate", "select_by_pk": "file", "update": "updateFiles", "update_by_pk": "updateFile"}}, "object_relationships": [{"name": "bucket", "using": {"foreign_key_constraint_on": "bucket_id"}}], "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["is_uploaded", "size", "metadata", "bucket_id", "etag", "mime_type", "name", "created_at", "updated_at", "id", "uploaded_by_user_id"]}, "comment": ""}], "select_permissions": [{"role": "user", "permission": {"columns": ["is_uploaded", "size", "metadata", "bucket_id", "etag", "mime_type", "name", "created_at", "updated_at", "id", "uploaded_by_user_id"], "filter": {}}, "comment": ""}], "update_permissions": [{"role": "user", "permission": {"columns": ["is_uploaded", "size", "metadata", "bucket_id", "etag", "mime_type", "name", "created_at", "updated_at", "id", "uploaded_by_user_id"], "filter": {}, "check": {}}, "comment": ""}]}, {"table": {"name": "virus", "schema": "storage"}, "configuration": {"column_config": {"created_at": {"custom_name": "createdAt"}, "file_id": {"custom_name": "fileId"}, "filename": {"custom_name": "filename"}, "id": {"custom_name": "id"}, "updated_at": {"custom_name": "updatedAt"}, "user_session": {"custom_name": "userSession"}, "virus": {"custom_name": "virus"}}, "custom_column_names": {"created_at": "createdAt", "file_id": "fileId", "filename": "filename", "id": "id", "updated_at": "updatedAt", "user_session": "userSession", "virus": "virus"}, "custom_name": "virus", "custom_root_fields": {"delete": "deleteViruses", "delete_by_pk": "deleteVirus", "insert": "insertViruses", "insert_one": "insertVirus", "select": "viruses", "select_aggregate": "virusesAggregate", "select_by_pk": "virus", "update": "updateViruses", "update_by_pk": "updateVirus"}}, "object_relationships": [{"name": "file", "using": {"foreign_key_constraint_on": "file_id"}}]}], "configuration": {"connection_info": {"database_url": {"from_env": "HASURA_GRAPHQL_DATABASE_URL"}, "isolation_level": "read-committed", "pool_settings": {"connection_lifetime": 600, "idle_timeout": 180, "max_connections": 50, "retries": 1}, "use_prepared_statements": true}}}]}}