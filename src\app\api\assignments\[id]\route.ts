import { NextRequest, NextResponse } from 'next/server';
import { getServerApolloClient } from '@/lib/apollo-client';
import { gql } from '@apollo/client';

// Validation helper
function validateUUID(uuid: string): string {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(uuid)) {
    throw new Error('Invalid UUID format');
  }
  return uuid;
}

// GraphQL query using admin client (bypasses allowlist)
const GET_ASSIGNMENT_BY_ID_ADMIN = gql`
  query GetAssignmentByIdAdmin($id: uuid!) {
    assignments_by_pk(id: $id) {
      id
      title
      description
      status
      created_at
      user_id
    }
  }
`;

// GET - Fetch single assignment by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const assignmentId = params.id;

    if (!assignmentId) {
      return NextResponse.json(
        { success: false, error: 'Missing assignment ID' },
        { status: 400 }
      );
    }

    const validatedId = validateUUID(assignmentId);

    console.log('🔍 Fetching assignment by ID:', validatedId);

    // Use server-side Apollo client with admin secret
    const apolloClient = getServerApolloClient();
    
    const result = await apolloClient.query({
      query: GET_ASSIGNMENT_BY_ID_ADMIN,
      variables: {
        id: validatedId,
      },
    });

    const assignment = result.data?.assignments_by_pk;

    if (!assignment) {
      return NextResponse.json(
        { success: false, error: 'Assignment not found' },
        { status: 404 }
      );
    }

    console.log('✅ Assignment fetched successfully:', assignment.id);

    return NextResponse.json({
      success: true,
      assignment: assignment
    });

  } catch (error) {
    console.error('❌ Failed to fetch assignment:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch assignment' 
      },
      { status: 500 }
    );
  }
}
