# Nhost Development Policy & Best Practices

## 🎯 Core Architecture Pattern

**ALWAYS use API routes with admin secret instead of direct GraphQL queries from the client.**

```
✅ PREFERRED: Client → API Routes (admin secret) → Hasura → Database
❌ AVOID:    Client → Direct GraphQL (allowlist restrictions) → Hasura → Database
```

## 🚀 Why This Pattern Works

### ✅ Advantages of API Routes + Admin Secret:

1. **No Allowlist Management** - Eliminates "query is not allowed" errors
2. **Full Server-Side Control** - Custom validation, business logic, error handling
3. **Better Security** - Admin secret never exposed to client
4. **Enhanced Flexibility** - Combine queries, add caching, rate limiting
5. **Easier Debugging** - Server-side logging and error tracking
6. **Faster Deployments** - No GraphQL compilation issues
7. **Consistent Patterns** - Same approach across all features

### ❌ Problems with Direct GraphQL:

1. **Allowlist Restrictions** - Must manually maintain permitted queries
2. **Permission Complexity** - Row-level security can be tricky
3. **Limited Control** - Can't add custom logic or validation
4. **Client Exposure** - GraphQL endpoint and queries visible to client
5. **Debugging Difficulty** - Harder to trace issues across client/server

## 📋 Implementation Guidelines

### 1. API Route Structure

```typescript
// src/app/api/[resource]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerApolloClient } from '@/lib/apollo-client';
import { gql } from '@apollo/client';

export async function GET(request: NextRequest) {
  try {
    // Use server Apollo client with admin secret
    const apolloClient = getServerApolloClient();
    
    const result = await apolloClient.query({
      query: YOUR_QUERY,
      variables: { ... }
    });

    return NextResponse.json({
      success: true,
      data: result.data
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
```

### 2. Client-Side Data Fetching

```typescript
// In React components
const [data, setData] = useState(null);
const [loading, setLoading] = useState(true);
const [error, setError] = useState(null);

useEffect(() => {
  const fetchData = async () => {
    try {
      const response = await fetch('/api/your-endpoint');
      const result = await response.json();
      
      if (result.success) {
        setData(result.data);
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  fetchData();
}, [dependencies]);
```

### 3. Error Handling Standards

```typescript
// Consistent error response format
return NextResponse.json({
  success: false,
  error: 'User-friendly error message',
  details: process.env.NODE_ENV === 'development' ? fullError : undefined
}, { status: appropriateStatusCode });
```

## 🛠️ Migration Strategy

### When Converting Direct GraphQL to API Routes:

1. **Create API route** in `src/app/api/[resource]/route.ts`
2. **Use admin GraphQL client** with `getServerApolloClient()`
3. **Update component** to use `fetch()` instead of `useQuery()`
4. **Add proper error handling** and loading states
5. **Test thoroughly** before removing old GraphQL queries
6. **Remove unused GraphQL** queries and imports

### Example Migration:

```typescript
// BEFORE (Direct GraphQL)
const { data, loading, error } = useQuery(GET_ASSIGNMENT_BY_ID, {
  variables: { id: assignmentId }
});

// AFTER (API Route)
const [assignment, setAssignment] = useState(null);
const [loading, setLoading] = useState(true);

useEffect(() => {
  fetch(`/api/assignments/${assignmentId}`)
    .then(res => res.json())
    .then(result => {
      if (result.success) setAssignment(result.assignment);
    })
    .finally(() => setLoading(false));
}, [assignmentId]);
```

## 📊 Proven Success Cases

### ✅ Working Features (Using API Routes):
- Assignments list (`/api/assignments`)
- Assignment details (`/api/assignments/[id]`)
- Assignment creation (`/api/assignments`)
- Analytics dashboard (`/api/analytics`)
- Bulk assessment (`/api/bulk-assess`)

### 🔧 Fixed Issues:
- **Assignment detail pages** - Converted from direct GraphQL to API routes
- **"Query not allowed" errors** - Eliminated by using admin secret
- **Deployment speed** - Improved from 3+ minutes to 1 minute

## 🎯 Development Workflow

1. **Always start with API routes** for new features
2. **Use admin secret** for all server-side GraphQL operations
3. **Implement consistent error handling** across all endpoints
4. **Add proper validation** and business logic in API routes
5. **Test with real data** before deploying
6. **Monitor performance** and add caching where needed

## 🚨 Red Flags to Avoid

- ❌ Direct `useQuery()` calls to Hasura from client components
- ❌ Managing GraphQL allowlists manually
- ❌ Exposing admin secrets to client-side code
- ❌ Inconsistent error handling patterns
- ❌ Missing validation in API routes

## 📈 Benefits Observed

- **Faster development** - No allowlist management
- **Better reliability** - Consistent error handling
- **Improved security** - Server-side validation
- **Easier debugging** - Centralized logging
- **Smoother deployments** - No GraphQL compilation issues

---

**This policy was established based on real-world experience fixing assignment detail page issues and observing the success of existing API route implementations.**
