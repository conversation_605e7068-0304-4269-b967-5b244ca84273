import { NextRequest, NextResponse } from 'next/server';
import { getServerApolloClient } from '@/lib/apollo-client';
import { gql } from '@apollo/client';
import { withRateLimit } from '@/lib/middleware/rateLimiter';
import { withSecurity, securityConfigs } from '@/lib/middleware/security';
import { sanitizeString } from '@/lib/middleware/validation';
import { createAuthContext, AuthenticationError } from '@/lib/auth/server';

// Simple UUID validation function
function validateUUID(uuid: string): string {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuid || typeof uuid !== 'string' || !uuidRegex.test(uuid)) {
    throw new Error('Invalid UUID format');
  }
  return uuid;
}

// GraphQL mutations using admin client (bypasses allowlist)
const CREATE_ASSIGNMENT_ADMIN = gql`
  mutation CreateAssignmentAdmin(
    $title: String!
    $description: String!
    $user_id: uuid!
  ) {
    insert_assignments_one(object: {
      title: $title
      description: $description
      user_id: $user_id
    }) {
      id
      title
      description
      status
      created_at
      user_id
    }
  }
`;

const GET_USER_ASSIGNMENTS_ADMIN = gql`
  query GetUserAssignmentsAdmin($user_id: uuid!) {
    assignments(
      where: { user_id: { _eq: $user_id } }
      order_by: { created_at: desc }
    ) {
      id
      title
      description
      status
      created_at
      user_id
    }
  }
`;

// POST - Create new assignment
const createAssignmentHandler = async (request: NextRequest) => {
  try {
    // Get authentication context
    const auth = createAuthContext(request);

    if (!auth.isAuthenticated) {
      throw new AuthenticationError('Authentication required to create assignments');
    }

    const body = await request.json();
    const { title, description } = body;

    // Validate required fields
    if (!title || !description) {
      return NextResponse.json(
        { error: 'Missing required fields: title, description' },
        { status: 400 }
      );
    }

    // Use authenticated user's ID instead of accepting user_id from request
    const authenticatedUserId = auth.user!.id;

    // Sanitize inputs
    const sanitizedTitle = sanitizeString(title);
    const sanitizedDescription = sanitizeString(description);

    console.log('🎯 Creating assignment via API route:', {
      title: sanitizedTitle,
      description: sanitizedDescription,
      user_id: authenticatedUserId
    });

    // Use server-side Apollo client with admin secret
    const apolloClient = getServerApolloClient();

    const result = await apolloClient.mutate({
      mutation: CREATE_ASSIGNMENT_ADMIN,
      variables: {
        title: sanitizedTitle,
        description: sanitizedDescription,
        user_id: authenticatedUserId,
      },
    });

    console.log('✅ Assignment created successfully:', result.data);

    return NextResponse.json({
      success: true,
      assignment: result.data.insert_assignments_one
    });

  } catch (error: any) {
    console.error('❌ Assignment creation failed:', error);

    // Handle authentication errors
    if (error instanceof AuthenticationError) {
      return NextResponse.json(
        { error: error.message, code: 'AUTH_REQUIRED' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to create assignment',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
};

// GET - Fetch user's assignments
const getAssignmentsHandler = async (request: NextRequest) => {
  try {
    // Get authentication context
    const auth = createAuthContext(request);

    if (!auth.isAuthenticated) {
      throw new AuthenticationError('Authentication required to fetch assignments');
    }

    // Use authenticated user's ID instead of accepting user_id from query params
    const authenticatedUserId = auth.user!.id;

    console.log('🔍 Fetching assignments for authenticated user:', authenticatedUserId);

    // Use server-side Apollo client with admin secret
    const apolloClient = getServerApolloClient();

    const result = await apolloClient.query({
      query: GET_USER_ASSIGNMENTS_ADMIN,
      variables: {
        user_id: authenticatedUserId,
      },
    });

    console.log('✅ Assignments fetched successfully:', result.data.assignments.length);

    return NextResponse.json({
      success: true,
      assignments: result.data.assignments
    });

  } catch (error: any) {
    console.error('❌ Failed to fetch assignments:', error);

    // Handle authentication errors
    if (error instanceof AuthenticationError) {
      return NextResponse.json(
        { error: error.message, code: 'AUTH_REQUIRED' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to fetch assignments',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
};

// Apply security middleware and rate limiting
export const POST = withSecurity(
  withRateLimit(createAssignmentHandler, 'general'),
  {
    ...securityConfigs.general,
    enableCORS: true // Explicitly enable CORS for assignment creation
  }
);

export const GET = withSecurity(
  withRateLimit(getAssignmentsHandler, 'general'),
  securityConfigs.general
);

// OPTIONS handled by security middleware
