import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useMutation, gql } from '@apollo/client';
import { useUserData } from '@nhost/nextjs';
import { useMultipleFilesUpload } from '@nhost/nextjs';
import Button from '@/components/ui/Button';

const CREATE_SUBMISSION = gql`
  mutation CreateSubmission($assignment_id: uuid!, $file_ids: [submission_files_insert_input!]!) {
    insert_student_submissions_one(object: {
      assignment_id: $assignment_id,
      student_id: "X-Hasura-User-Id",
      status: "submitted",
      submitted_at: "now()",
      submission_files: {
        data: $file_ids
      }
    }) {
      id
      status
      submitted_at
      submission_files {
        id
        file_id
        file_name
      }
    }
  }
`;

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

const schema = yup.object().shape({
  files: yup.array().of(
    yup.mixed()
      .required('File is required')
      .test('fileSize', 'File size must be less than 10MB', (file: any) => {
        if (!file) return false;
        return file.size <= MAX_FILE_SIZE;
      })
      .test('fileType', 'Unsupported file type', (file: any) => {
        if (!file) return false;
        const type = file.type;
        const name = file.name.toLowerCase();

        // Check MIME types
        const validMimeTypes = [
          'image/',
          'text/',
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/zip',
          'application/x-zip-compressed'
        ];

        // Check file extensions for code files
        const validExtensions = [
          '.js', '.ts', '.jsx', '.tsx',
          '.py', '.java', '.cpp', '.c', '.h',
          '.html', '.css', '.scss', '.json',
          '.md', '.txt', '.csv'
        ];

        return validMimeTypes.some(mime => type.startsWith(mime)) ||
               validExtensions.some(ext => name.endsWith(ext));
      })
  ).min(1, 'At least one file is required'),
});

interface SubmissionFormProps {
  assignmentId: string;
}

interface SubmissionFormData {
  files: File[];
}

export default function SubmissionForm({ assignmentId }: SubmissionFormProps) {
  const user = useUserData();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const { upload, progress, isUploaded, isUploading, files: uploadedFilesInfo } = useMultipleFilesUpload();
  const [createSubmission] = useMutation(CREATE_SUBMISSION);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<SubmissionFormData>({
    defaultValues: {
      files: [],
    }
  });

  const onSubmit = async (data: SubmissionFormData) => {
    if (!user?.id) {
      alert('You must be logged in to submit assignments');
      return;
    }

    if (!data.files || data.files.length === 0) {
      alert('Please select at least one file to upload');
      return;
    }

    console.log('Starting upload with files:', data.files);
    setIsSubmitting(true);
    setUploadProgress(0); // Reset progress

    try {
      // Upload files to Nhost storage using useMultipleFilesUpload
      console.log('Uploading to bucket: default');
      const uploadResult = await upload({
        files: data.files,
        bucketId: 'default'
      });

      console.log('Upload initiated successfully', uploadResult);

    } catch (error) {
      console.error('Error triggering upload:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      alert(`Failed to start upload: ${errorMessage}`);
      setIsSubmitting(false);
      setUploadProgress(0); // Reset progress on error
    }
  };

  // Watch for changes in the upload hook's state to update UI and create submission record
  useEffect(() => {
    const createSubmissionRecord = async () => {
      console.log('Checking upload status:', { isUploaded, uploadedFilesInfo });

      if (isUploaded && uploadedFilesInfo && uploadedFilesInfo.length > 0) {
        console.log('Files uploaded successfully, creating submission record');

        try {
          // Prepare data for submission record using selected files
          // Note: uploadedFilesInfo contains complex Nhost state objects that are difficult to access
          const watchedFiles = watch('files');
          const submissionFilesData = watchedFiles?.map((file, index) => {
            return {
              file_id: `uploaded_${index}`, // Temporary ID since we can't access the actual upload result
              file_name: file.name,
              file_type: getFileType(file)
            };
          }) || [];

          console.log('Prepared submission data:', {
            assignment_id: assignmentId,
            file_ids: submissionFilesData
          });

          // Create submission record in the database
          const result = await createSubmission({
            variables: {
              assignment_id: assignmentId,
              file_ids: submissionFilesData,
            },
          });

          console.log('Submission result:', result);

          if (result.data) {
            alert('Submission created successfully!');
            window.location.href = `/submissions/${result.data.insert_student_submissions_one.id}`;
          }
        } catch (error) {
          console.error('Error creating submission record:', error);
          const errorMessage = error instanceof Error ? error.message : String(error);
          alert(`Failed to create submission record: ${errorMessage}`);
        } finally {
          setIsSubmitting(false);
          setUploadProgress(0);
        }
      }
    };

    createSubmissionRecord();
  }, [isUploaded, uploadedFilesInfo, assignmentId, createSubmission, watch]);

  // Helper function to determine file type
  const getFileType = (file: File | undefined): string => {
    if (!file) return 'other';

    const type = file.type;
    const name = file.name.toLowerCase();

    if (type.startsWith('image/')) return 'image';
    if (type.startsWith('text/') ||
        type.startsWith('application/pdf') ||
        type.startsWith('application/msword') ||
        type.startsWith('application/vnd.openxmlformats-officedocument.wordprocessingml.document') ||
        ['.js', '.ts', '.py', '.java', '.cpp', '.c', '.h', '.html', '.css', '.scss', '.json', '.md', '.txt', '.csv']
          .some(ext => name.endsWith(ext))) {
      return 'document';
    }
    return 'other';
  };

  // Update progress based on the hook's state
  useEffect(() => {
    console.log('Upload status:', { isUploading, isUploaded, progress });

    if (isUploading && typeof progress === 'number') {
      setUploadProgress(progress);
      console.log('Upload progress:', progress);
    } else if (isUploaded) {
      setUploadProgress(100);
      console.log('Upload completed, files:', uploadedFilesInfo);
    }
  }, [isUploading, isUploaded, progress, uploadedFilesInfo]);

  // Upload errors are handled in the try/catch block of onSubmit

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* File Upload Field */}
        <div>
          <label className="block text-sm font-medium text-text mb-2">
            Upload Files <span className="text-red-500">*</span>
          </label>
          <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
            <div className="space-y-1 text-center">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                stroke="currentColor"
                fill="none"
                viewBox="0 0 48 48"
                aria-hidden="true"
              >
                <path
                  d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                  strokeWidth={2}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              <div className="flex text-sm text-gray-600">
                <label
                  htmlFor="file-upload"
                  className="relative cursor-pointer bg-white rounded-md font-medium text-accent hover:text-accent/80 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-accent"
                >
                  <span>Upload files</span>
                  <input
                    id="file-upload"
                    type="file"
                    multiple
                    className="sr-only"
                    {...register('files')}
                    onChange={(e) => {
                      const files = Array.from(e.target.files || []);
                      setValue('files', files);
                    }}
                  />
                </label>
                <p className="pl-1">or drag and drop</p>
              </div>
              <p className="text-xs text-gray-500">
                Up to 10MB per file
              </p>
            </div>
          </div>
          {errors.files && (
            <p className="mt-2 text-sm text-red-600">{errors.files.message}</p>
          )}
        </div>

        {/* File Type Information */}
        <div className="bg-gray-50 rounded-md p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-2">Permitted file types:</h3>
          <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
            <div>
              <p className="font-medium mb-1">Images</p>
              <p className="text-gray-500">PNG, JPG, GIF</p>
            </div>
            <div>
              <p className="font-medium mb-1">Documents</p>
              <p className="text-gray-500">PDF, DOC, DOCX</p>
            </div>
            <div>
              <p className="font-medium mb-1">Code Files</p>
              <p className="text-gray-500">JS, TS, PY, JAVA, etc.</p>
            </div>
            <div>
              <p className="font-medium mb-1">Archives</p>
              <p className="text-gray-500">ZIP (containing any of the above)</p>
            </div>
          </div>
        </div>

        {/* Upload Progress */}
        {(isSubmitting || isUploading) && uploadProgress > 0 && uploadProgress <= 100 && (
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div
              className="bg-accent h-2.5 rounded-full transition-all duration-300"
              style={{ width: `${uploadProgress}%` }}
            ></div>
          </div>
        )}

        {/* Submit Button */}
        <div className="flex justify-end">
          <Button
            type="submit"
            disabled={isSubmitting || isUploading}
            variant="primary"
          >
            {(isSubmitting || isUploading) ? 'Submitting...' : 'Submit Assignment'}
          </Button>
        </div>
      </form>
    </div>
  );
}