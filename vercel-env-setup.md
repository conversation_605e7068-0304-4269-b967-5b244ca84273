# CRITICAL: Vercel Environment Variables Setup

## Required Environment Variables for Production

### 1. Nhost Configuration (REQUIRED)
```
NEXT_PUBLIC_NHOST_SUBDOMAIN=your-nhost-subdomain
NEXT_PUBLIC_NHOST_REGION=your-nhost-region
NHOST_GRAPHQL_URL=https://your-subdomain.hasura.your-region.nhost.run/v1/graphql
NHOST_FUNCTIONS_URL=https://your-subdomain.functions.your-region.nhost.run/v1
```

### 2. AI API Keys (REQUIRED)
```
OPENAI_API_KEY=sk-...
ANTHROPIC_API_KEY=sk-ant-...
OPENAI_ORG_ID=org-... (optional)
```

### 3. Security Settings
```
NODE_ENV=production
NEXTAUTH_SECRET=your-secure-random-string-32-chars-min
NEXTAUTH_URL=https://your-vercel-domain.vercel.app
```

## IMMEDIATE ACTIONS REQUIRED:

1. **Add ALL environment variables to Vercel dashboard**
2. **Verify API keys are valid and have sufficient credits**
3. **Test Nhost connection from Vercel**
4. **Set up proper CORS in Nhost for Vercel domain**

## Security Checklist:
- [ ] No hardcoded secrets in code
- [ ] API keys properly configured in Vercel
- [ ] Database permissions restricted to authenticated users
- [ ] File upload size limits enforced
- [ ] Rate limiting configured
