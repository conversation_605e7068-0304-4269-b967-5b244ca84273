import { NextRequest, NextResponse } from 'next/server';
import DOMPurify from 'isomorphic-dompurify';
import { validateFileSecuritySync, DEFAULT_FILE_SECURITY_CONFIG, type FileSecurityConfig } from '@/lib/security/file-security';

// Input validation and sanitization utilities
export class ValidationError extends Error {
  constructor(message: string, public field?: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

// Sanitize string input to prevent XSS
export function sanitizeString(input: string): string {
  if (typeof input !== 'string') {
    throw new ValidationError('Input must be a string');
  }

  // Remove HTML tags and dangerous characters
  return DOMPurify.sanitize(input, {
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: []
  }).trim();
}

// Validate and sanitize submission ID
export function validateSubmissionId(id: any): string {
  if (!id) {
    throw new ValidationError('Submission ID is required', 'submissionId');
  }

  if (typeof id !== 'string' && typeof id !== 'number') {
    throw new ValidationError('Submission ID must be a string or number', 'submissionId');
  }

  const sanitized = sanitizeString(String(id));

  // Check if it's a valid UUID or numeric ID
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  const numericRegex = /^\d+$/;

  if (!uuidRegex.test(sanitized) && !numericRegex.test(sanitized)) {
    throw new ValidationError('Invalid submission ID format', 'submissionId');
  }

  return sanitized;
}

// Validate student name
export function validateStudentName(name: any): string {
  if (!name) {
    throw new ValidationError('Student name is required', 'studentName');
  }

  if (typeof name !== 'string') {
    throw new ValidationError('Student name must be a string', 'studentName');
  }

  const sanitized = sanitizeString(name);

  if (sanitized.length < 2) {
    throw new ValidationError('Student name must be at least 2 characters', 'studentName');
  }

  if (sanitized.length > 100) {
    throw new ValidationError('Student name must be less than 100 characters', 'studentName');
  }

  // Only allow letters, spaces, hyphens, and apostrophes
  const nameRegex = /^[a-zA-Z\s\-']+$/;
  if (!nameRegex.test(sanitized)) {
    throw new ValidationError('Student name contains invalid characters', 'studentName');
  }

  return sanitized;
}

// Validate assignment title
export function validateAssignmentTitle(title: any): string {
  if (!title) {
    throw new ValidationError('Assignment title is required', 'title');
  }

  if (typeof title !== 'string') {
    throw new ValidationError('Assignment title must be a string', 'title');
  }

  const sanitized = sanitizeString(title);

  if (sanitized.length < 3) {
    throw new ValidationError('Assignment title must be at least 3 characters', 'title');
  }

  if (sanitized.length > 200) {
    throw new ValidationError('Assignment title must be less than 200 characters', 'title');
  }

  return sanitized;
}

// Validate assignment description
export function validateAssignmentDescription(description: any): string {
  if (!description) {
    throw new ValidationError('Assignment description is required', 'description');
  }

  if (typeof description !== 'string') {
    throw new ValidationError('Assignment description must be a string', 'description');
  }

  const sanitized = sanitizeString(description);

  if (sanitized.length < 10) {
    throw new ValidationError('Assignment description must be at least 10 characters', 'description');
  }

  if (sanitized.length > 2000) {
    throw new ValidationError('Assignment description must be less than 2000 characters', 'description');
  }

  return sanitized;
}

// Enhanced file validation with advanced security checks
export function validateFileUpload(
  file: File,
  config: FileSecurityConfig = DEFAULT_FILE_SECURITY_CONFIG
): {
  isValid: boolean;
  isSafe: boolean;
  errors: string[];
  warnings: string[];
  sanitizedName: string;
} {
  // Use the advanced file security validation
  const securityResult = validateFileSecuritySync(file, config);

  return {
    isValid: securityResult.isValid,
    isSafe: securityResult.isSafe,
    errors: securityResult.errors,
    warnings: securityResult.warnings,
    sanitizedName: securityResult.metadata.sanitizedName
  };
}

// Validate file type and size (legacy function)
export function validateFile(file: File): void {
  const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB

  if (!file) {
    throw new ValidationError('File is required', 'file');
  }

  if (file.size > MAX_FILE_SIZE) {
    throw new ValidationError(`File size exceeds 50MB limit (${Math.round(file.size / 1024 / 1024)}MB)`, 'file');
  }

  // Allowed file types
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/zip',
    'application/x-zip-compressed'
  ];

  const allowedExtensions = [
    '.pdf', '.doc', '.docx', '.txt', '.jpg', '.jpeg', '.png', '.gif', '.zip',
    '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cpp', '.c', '.h',
    '.html', '.css', '.scss', '.json', '.md', '.csv'
  ];

  const fileName = file.name.toLowerCase();
  const hasValidType = allowedTypes.includes(file.type);
  const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));

  if (!hasValidType && !hasValidExtension) {
    throw new ValidationError(`File type not allowed: ${file.type || 'unknown'}`, 'file');
  }

  // Check for potentially dangerous file names
  const dangerousPatterns = [
    /\.exe$/i, /\.bat$/i, /\.cmd$/i, /\.scr$/i, /\.pif$/i,
    /\.com$/i, /\.vbs$/i, /\.js$/i, /\.jar$/i, /\.php$/i
  ];

  if (dangerousPatterns.some(pattern => pattern.test(fileName))) {
    throw new ValidationError('File type not allowed for security reasons', 'file');
  }
}

// Validate assessment configuration
export function validateAssessmentConfig(config: any): any {
  if (!config || typeof config !== 'object') {
    throw new ValidationError('Assessment configuration is required', 'config');
  }

  // Validate AI provider
  if (config.aiProvider && !['openai', 'anthropic'].includes(config.aiProvider)) {
    throw new ValidationError('Invalid AI provider', 'config.aiProvider');
  }

  // Validate priority
  if (config.priority && !['standard', 'high', 'urgent'].includes(config.priority)) {
    throw new ValidationError('Invalid priority level', 'config.priority');
  }

  // Validate feedback level
  if (config.feedbackLevel && !['brief', 'standard', 'detailed'].includes(config.feedbackLevel)) {
    throw new ValidationError('Invalid feedback level', 'config.feedbackLevel');
  }

  return config;
}

// Middleware to validate request body
export function withValidation(
  handler: (request: NextRequest) => Promise<NextResponse>,
  validators: Record<string, (value: any) => any>
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    try {
      let body: any = {};

      // Parse request body if it exists
      if (request.headers.get('content-type')?.includes('application/json')) {
        try {
          body = await request.json();
        } catch (error) {
          return NextResponse.json(
            { error: 'Invalid JSON in request body' },
            { status: 400 }
          );
        }
      }

      // Apply validators
      const validatedData: any = {};
      for (const [field, validator] of Object.entries(validators)) {
        if (body[field] !== undefined) {
          validatedData[field] = validator(body[field]);
        }
      }

      // Add validated data to request
      (request as any).validatedData = validatedData;

      return await handler(request);

    } catch (error) {
      if (error instanceof ValidationError) {
        return NextResponse.json(
          {
            error: 'Validation failed',
            message: error.message,
            field: error.field
          },
          { status: 400 }
        );
      }

      console.error('Validation middleware error:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  };
}
