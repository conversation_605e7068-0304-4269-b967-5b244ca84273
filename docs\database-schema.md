# Database Schema for AI Grading and Assessment SaaS

This document outlines the proposed database schema for the AI Grading and Assessment SaaS platform. You can use this as a reference when setting up your tables in Nhost.

## Core Tables

### 1. Users

This table is automatically created by Nhost and stores user information.

```sql
-- This table is managed by <PERSON>hos<PERSON> Auth
-- Reference: auth.users
```

### 2. Organizations

```sql
CREATE TABLE organizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  name TEXT NOT NULL,
  description TEXT,
  logo_url TEXT
);
```

### 3. Organization Members

```sql
CREATE TABLE organization_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('admin', 'teacher', 'assistant')),
  UNIQUE (organization_id, user_id)
);
```

### 4. Courses

```sql
CREATE TABLE courses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  start_date DATE,
  end_date DATE
);
```

### 5. Assignments

```sql
CREATE TABLE assignments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  course_id UUID NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  due_date TIMESTAMPTZ,
  max_score INTEGER,
  assignment_type TEXT NOT NULL CHECK (assignment_type IN ('essay', 'code', 'mixed', 'image', 'other'))
);
```

### 6. Assessment Criteria

```sql
CREATE TABLE assessment_criteria (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  assignment_id UUID NOT NULL REFERENCES assignments(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  weight DECIMAL(5,2) NOT NULL CHECK (weight >= 0 AND weight <= 100),
  order_index INTEGER NOT NULL
);
```

### 7. Student Submissions

```sql
CREATE TABLE student_submissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  assignment_id UUID NOT NULL REFERENCES assignments(id) ON DELETE CASCADE,
  student_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  status TEXT NOT NULL CHECK (status IN ('draft', 'submitted', 'grading', 'graded')),
  submitted_at TIMESTAMPTZ,
  UNIQUE (assignment_id, student_id)
);
```

### 8. Submission Files

```sql
CREATE TABLE submission_files (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  submission_id UUID NOT NULL REFERENCES student_submissions(id) ON DELETE CASCADE,
  file_id UUID NOT NULL REFERENCES storage.files(id) ON DELETE CASCADE,
  file_type TEXT NOT NULL CHECK (file_type IN ('document', 'code', 'image', 'other')),
  file_name TEXT NOT NULL
);
```

### 9. AI Assessments

```sql
CREATE TABLE ai_assessments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  submission_id UUID NOT NULL REFERENCES student_submissions(id) ON DELETE CASCADE,
  overall_score DECIMAL(5,2),
  overall_feedback TEXT,
  status TEXT NOT NULL CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  processed_at TIMESTAMPTZ
);
```

### 10. Criteria Assessments

```sql
CREATE TABLE criteria_assessments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  ai_assessment_id UUID NOT NULL REFERENCES ai_assessments(id) ON DELETE CASCADE,
  criteria_id UUID NOT NULL REFERENCES assessment_criteria(id) ON DELETE CASCADE,
  score DECIMAL(5,2),
  feedback TEXT,
  UNIQUE (ai_assessment_id, criteria_id)
);
```

### 11. Teacher Reviews

```sql
CREATE TABLE teacher_reviews (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  ai_assessment_id UUID NOT NULL REFERENCES ai_assessments(id) ON DELETE CASCADE,
  teacher_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  overall_score DECIMAL(5,2),
  overall_feedback TEXT,
  status TEXT NOT NULL CHECK (status IN ('draft', 'published')),
  published_at TIMESTAMPTZ
);
```

## Permissions Setup

After creating these tables, you'll need to set up appropriate permissions in the Hasura console:

1. For the `public` role (unauthenticated users):
   - No access to any tables

2. For the `user` role (authenticated users):
   - Select access to their own data
   - Insert/update access to their own submissions

3. For the `teacher` role:
   - Full access to assignments, criteria, and reviews they create
   - Select access to submissions for their courses
   - Update access to review submissions

4. For the `admin` role:
   - Full access to all tables

## Next Steps

1. Create these tables in your Nhost project using the Hasura console
2. Set up appropriate permissions for each role
3. Create initial test data to verify the schema works as expected
4. Use the generated TypeScript types to build your UI components
