/**
 * Advanced File Security System
 * Provides comprehensive file validation, malware detection, and content inspection
 */

export interface FileSecurityResult {
  isValid: boolean;
  isSafe: boolean;
  errors: string[];
  warnings: string[];
  metadata: {
    originalName: string;
    sanitizedName: string;
    mimeType: string;
    size: number;
    hash?: string;
  };
}

export interface FileSecurityConfig {
  maxSize: number;
  allowedMimeTypes: string[];
  allowedExtensions: string[];
  enableMalwareScanning: boolean;
  enableContentInspection: boolean;
  quarantineOnSuspicious: boolean;
}

// Default security configuration
export const DEFAULT_FILE_SECURITY_CONFIG: FileSecurityConfig = {
  maxSize: 50 * 1024 * 1024, // 50MB
  allowedMimeTypes: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'text/csv',
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp'
  ],
  allowedExtensions: [
    '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
    '.txt', '.csv', '.jpg', '.jpeg', '.png', '.gif', '.webp'
  ],
  enableMalwareScanning: true,
  enableContentInspection: true,
  quarantineOnSuspicious: true
};

// Dangerous file signatures (magic bytes)
const DANGEROUS_SIGNATURES = [
  { signature: [0x4D, 0x5A], description: 'Windows Executable (PE)', extension: '.exe' },
  { signature: [0x50, 0x4B, 0x03, 0x04], description: 'ZIP Archive (potential)', extension: '.zip' },
  { signature: [0x7F, 0x45, 0x4C, 0x46], description: 'Linux Executable (ELF)', extension: '.elf' },
  { signature: [0xCA, 0xFE, 0xBA, 0xBE], description: 'Java Class File', extension: '.class' },
  { signature: [0xFE, 0xED, 0xFA, 0xCE], description: 'Mach-O Binary', extension: '.macho' }
];

// Suspicious patterns in file content
const SUSPICIOUS_PATTERNS = [
  /eval\s*\(/gi,
  /document\.write\s*\(/gi,
  /innerHTML\s*=/gi,
  /<script[^>]*>/gi,
  /javascript:/gi,
  /vbscript:/gi,
  /onload\s*=/gi,
  /onerror\s*=/gi,
  /base64,/gi,
  /cmd\.exe/gi,
  /powershell/gi,
  /\/bin\/sh/gi,
  /\/bin\/bash/gi
];

/**
 * Sanitize file name to prevent path traversal and other attacks
 */
export function sanitizeFileName(fileName: string): string {
  return fileName
    .replace(/[<>:"/\\|?*\x00-\x1f]/g, '') // Remove dangerous characters
    .replace(/^\.+/, '') // Remove leading dots
    .replace(/\.+$/, '') // Remove trailing dots
    .replace(/\s+/g, '_') // Replace spaces with underscores
    .substring(0, 255) // Limit length
    .toLowerCase();
}

/**
 * Validate file extension against whitelist
 */
export function validateFileExtension(fileName: string, allowedExtensions: string[]): boolean {
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  return allowedExtensions.includes(extension);
}

/**
 * Validate MIME type against whitelist
 */
export function validateMimeType(mimeType: string, allowedMimeTypes: string[]): boolean {
  return allowedMimeTypes.includes(mimeType.toLowerCase());
}

/**
 * Check file signature (magic bytes) for dangerous file types
 */
export function checkFileSignature(buffer: ArrayBuffer): { isDangerous: boolean; detectedType?: string } {
  const bytes = new Uint8Array(buffer.slice(0, 16)); // Check first 16 bytes
  
  for (const { signature, description } of DANGEROUS_SIGNATURES) {
    if (signature.every((byte, index) => bytes[index] === byte)) {
      return { isDangerous: true, detectedType: description };
    }
  }
  
  return { isDangerous: false };
}

/**
 * Scan file content for suspicious patterns
 */
export function scanFileContent(content: string): { isSuspicious: boolean; matches: string[] } {
  const matches: string[] = [];
  
  for (const pattern of SUSPICIOUS_PATTERNS) {
    const match = content.match(pattern);
    if (match) {
      matches.push(match[0]);
    }
  }
  
  return { isSuspicious: matches.length > 0, matches };
}

/**
 * Calculate file hash for integrity checking
 */
export async function calculateFileHash(buffer: ArrayBuffer): Promise<string> {
  const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

/**
 * Basic malware scanning using pattern detection
 */
export function basicMalwareScanning(buffer: ArrayBuffer, fileName: string): { isMalware: boolean; reason?: string } {
  // Check file signature
  const signatureCheck = checkFileSignature(buffer);
  if (signatureCheck.isDangerous) {
    return { isMalware: true, reason: `Dangerous file type detected: ${signatureCheck.detectedType}` };
  }
  
  // Check for suspicious file extensions with mismatched content
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  const bytes = new Uint8Array(buffer.slice(0, 4));
  
  // PDF should start with %PDF
  if (extension === '.pdf' && !(bytes[0] === 0x25 && bytes[1] === 0x50 && bytes[2] === 0x44 && bytes[3] === 0x46)) {
    return { isMalware: true, reason: 'PDF file with invalid header' };
  }
  
  // Check for embedded executables in documents
  const content = new TextDecoder('utf-8', { fatal: false }).decode(buffer.slice(0, 1024));
  const contentScan = scanFileContent(content);
  if (contentScan.isSuspicious) {
    return { isMalware: true, reason: `Suspicious content patterns: ${contentScan.matches.join(', ')}` };
  }
  
  return { isMalware: false };
}

/**
 * Comprehensive file security validation
 */
export async function validateFileSecurityAsync(
  file: File,
  config: FileSecurityConfig = DEFAULT_FILE_SECURITY_CONFIG
): Promise<FileSecurityResult> {
  const errors: string[] = [];
  const warnings: string[] = [];
  let isValid = true;
  let isSafe = true;
  
  // Basic validations
  if (file.size > config.maxSize) {
    errors.push(`File size ${file.size} exceeds maximum allowed size ${config.maxSize}`);
    isValid = false;
  }
  
  if (!validateMimeType(file.type, config.allowedMimeTypes)) {
    errors.push(`MIME type ${file.type} is not allowed`);
    isValid = false;
  }
  
  if (!validateFileExtension(file.name, config.allowedExtensions)) {
    errors.push(`File extension is not allowed`);
    isValid = false;
  }
  
  // Sanitize file name
  const sanitizedName = sanitizeFileName(file.name);
  if (sanitizedName !== file.name) {
    warnings.push('File name was sanitized for security');
  }
  
  // Read file content for advanced validation
  let buffer: ArrayBuffer;
  let hash: string | undefined;
  
  try {
    buffer = await file.arrayBuffer();
    
    if (config.enableContentInspection) {
      // Calculate file hash
      hash = await calculateFileHash(buffer);
      
      // Check file signature
      const signatureCheck = checkFileSignature(buffer);
      if (signatureCheck.isDangerous) {
        errors.push(`Dangerous file type detected: ${signatureCheck.detectedType}`);
        isValid = false;
        isSafe = false;
      }
      
      // Basic malware scanning
      if (config.enableMalwareScanning) {
        const malwareCheck = basicMalwareScanning(buffer, file.name);
        if (malwareCheck.isMalware) {
          errors.push(`Potential malware detected: ${malwareCheck.reason}`);
          isValid = false;
          isSafe = false;
        }
      }
    }
  } catch (error) {
    errors.push('Failed to read file content for security validation');
    isValid = false;
    isSafe = false;
  }
  
  return {
    isValid,
    isSafe,
    errors,
    warnings,
    metadata: {
      originalName: file.name,
      sanitizedName,
      mimeType: file.type,
      size: file.size,
      hash
    }
  };
}

/**
 * Synchronous file security validation (limited checks)
 */
export function validateFileSecuritySync(
  file: File,
  config: FileSecurityConfig = DEFAULT_FILE_SECURITY_CONFIG
): Omit<FileSecurityResult, 'metadata'> & { metadata: Omit<FileSecurityResult['metadata'], 'hash'> } {
  const errors: string[] = [];
  const warnings: string[] = [];
  let isValid = true;
  let isSafe = true;
  
  // Basic validations
  if (file.size > config.maxSize) {
    errors.push(`File size ${file.size} exceeds maximum allowed size ${config.maxSize}`);
    isValid = false;
  }
  
  if (!validateMimeType(file.type, config.allowedMimeTypes)) {
    errors.push(`MIME type ${file.type} is not allowed`);
    isValid = false;
  }
  
  if (!validateFileExtension(file.name, config.allowedExtensions)) {
    errors.push(`File extension is not allowed`);
    isValid = false;
  }
  
  // Sanitize file name
  const sanitizedName = sanitizeFileName(file.name);
  if (sanitizedName !== file.name) {
    warnings.push('File name was sanitized for security');
  }
  
  return {
    isValid,
    isSafe,
    errors,
    warnings,
    metadata: {
      originalName: file.name,
      sanitizedName,
      mimeType: file.type,
      size: file.size
    }
  };
}

/**
 * File quarantine system
 */
export class FileQuarantine {
  private quarantinedFiles: Map<string, { file: File; reason: string; timestamp: Date }> = new Map();
  
  quarantineFile(fileId: string, file: File, reason: string): void {
    this.quarantinedFiles.set(fileId, {
      file,
      reason,
      timestamp: new Date()
    });
    
    console.warn(`🚨 File quarantined: ${file.name} - Reason: ${reason}`);
  }
  
  isQuarantined(fileId: string): boolean {
    return this.quarantinedFiles.has(fileId);
  }
  
  getQuarantinedFiles(): Array<{ id: string; name: string; reason: string; timestamp: Date }> {
    return Array.from(this.quarantinedFiles.entries()).map(([id, data]) => ({
      id,
      name: data.file.name,
      reason: data.reason,
      timestamp: data.timestamp
    }));
  }
  
  releaseFile(fileId: string): boolean {
    return this.quarantinedFiles.delete(fileId);
  }
}

// Global quarantine instance
export const fileQuarantine = new FileQuarantine();
