'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useUserData, useAccessToken } from '@nhost/nextjs';
import Button from '@/components/ui/Button';
import { useRouter } from 'next/navigation';

const schema = yup.object().shape({
  title: yup.string().required('Assignment title is required'),
  description: yup.string().required('Assignment description is required'),
  studentName: yup.string().required('Student name is required'),
});

interface AssignmentFormData {
  title: string;
  description: string;
  studentName: string;
}

interface AssignmentFormProps {}

export default function AssignmentForm({}: AssignmentFormProps) {
  const user = useUserData();
  const accessToken = useAccessToken();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm<AssignmentFormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      title: '',
      description: '',
      studentName: '',
    }
  });

  const onSubmit = async (data: AssignmentFormData) => {
    if (!user?.id) {
      alert('You must be logged in to create assignments');
      return;
    }

    if (!accessToken) {
      alert('Authentication token not available. Please try logging out and back in.');
      return;
    }

    setIsSubmitting(true);
    try {
      // Store student name for later use when they submit
      localStorage.setItem('currentStudentName', data.studentName);

      console.log('🚀 Creating assignment via API route...');

      const response = await fetch('/api/assignments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          title: data.title,
          description: data.description,
          user_id: user.id,
        }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        const assignmentId = result.assignment.id;
        alert('Assignment created successfully! You can now have students upload their work.');
        reset();

        // Redirect to student upload page for this assignment
        router.push(`/student-upload?assignmentId=${assignmentId}`);
      } else {
        throw new Error(result.error || 'Failed to create assignment');
      }
    } catch (error) {
      console.error('Error creating assignment:', error);
      alert(`Failed to create assignment: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Student Name Field */}
        <div>
          <label htmlFor="studentName" className="block text-sm font-medium text-text mb-2">
            Student Name <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="studentName"
            {...register('studentName')}
            className="block w-full rounded-md border border-gray-300 py-2 px-3 bg-white shadow-sm focus:ring-2 focus:ring-accent focus:border-accent"
            placeholder="Enter student name"
          />
          {errors.studentName && (
            <p className="mt-1 text-sm text-red-600">{errors.studentName.message}</p>
          )}
        </div>

        {/* Assignment Title Field */}
        <div>
          <label htmlFor="title" className="block text-sm font-medium text-text mb-2">
            Assignment Title <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="title"
            {...register('title')}
            className="block w-full rounded-md border border-gray-300 py-2 px-3 bg-white shadow-sm focus:ring-2 focus:ring-accent focus:border-accent"
            placeholder="Enter assignment title"
          />
          {errors.title && (
            <p className="mt-1 text-sm text-red-600">{errors.title.message}</p>
          )}
        </div>

        {/* Assignment Description Field */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-text mb-2">
            Assignment Description <span className="text-red-500">*</span>
          </label>
          <textarea
            id="description"
            rows={6}
            {...register('description')}
            className="block w-full rounded-md border border-gray-300 py-2 px-3 bg-white shadow-sm focus:ring-2 focus:ring-accent focus:border-accent"
            placeholder="Enter assignment description and requirements"
          />
          {errors.description && (
            <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
          )}
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <Button
            type="submit"
            disabled={isSubmitting}
            variant="primary"
          >
            {isSubmitting ? 'Creating Assignment...' : 'Create Assignment'}
          </Button>
        </div>
      </form>
    </div>
  );
}
