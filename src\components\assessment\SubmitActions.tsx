import { CheckCircleIcon } from '@heroicons/react/24/outline';

interface SubmitActionsProps {
  onSubmit: () => void;
  onSaveDraft: () => void;
  onBack: () => void;
  isSubmitting: boolean;
}

export default function SubmitActions({ 
  onSubmit, 
  onSaveDraft, 
  onBack, 
  isSubmitting 
}: SubmitActionsProps) {
  return (
    <div className="space-y-6">
      {/* Ready to Proceed Section */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <div className="flex items-start space-x-3">
          <CheckCircleIcon className="w-6 h-6 text-green-600 mt-0.5 flex-shrink-0" />
          <div className="flex-1">
            <h3 className="text-lg font-medium text-green-900 mb-2">
              Ready to proceed?
            </h3>
            <p className="text-green-800 mb-4">
              Submit your assessment to begin processing your student submissions.
            </p>
            <button
              onClick={onSubmit}
              disabled={isSubmitting}
              className={`inline-flex items-center px-6 py-3 rounded-md font-medium transition-colors ${
                isSubmitting
                  ? 'bg-gray-400 text-white cursor-not-allowed'
                  : 'bg-green-600 text-white hover:bg-green-700'
              }`}
            >
              {isSubmitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing...
                </>
              ) : (
                'Submit Assessment'
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Bottom Navigation */}
      <div className="flex items-center justify-between pt-6 border-t border-gray-200">
        <button
          onClick={onBack}
          disabled={isSubmitting}
          className="flex items-center space-x-2 px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          <span>Back</span>
        </button>

        <div className="flex items-center space-x-4">
          <button
            onClick={onSaveDraft}
            disabled={isSubmitting}
            className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Save as Draft
          </button>
          <button
            onClick={onSubmit}
            disabled={isSubmitting}
            className={`px-6 py-2 rounded-md font-medium transition-colors ${
              isSubmitting
                ? 'bg-gray-400 text-white cursor-not-allowed'
                : 'bg-accent text-white hover:bg-[#9A7649]'
            }`}
          >
            {isSubmitting ? 'Processing...' : 'Submit Assessment'}
          </button>
        </div>
      </div>
    </div>
  );
}
