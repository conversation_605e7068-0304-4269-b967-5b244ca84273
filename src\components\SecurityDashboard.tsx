'use client';

import React, { useState, useEffect } from 'react';
import { Shield, AlertTriangle, Activity, Lock, Eye, EyeOff } from 'lucide-react';

interface SecurityMetrics {
  totalEvents: number;
  eventsByType: {
    rateLimitViolations: number;
    authFailures: number;
    suspiciousUploads: number;
    apiErrors: number;
    corsViolations: number;
  };
  suspiciousActivity: {
    rateLimitAbuse: boolean;
    authFailureSpike: boolean;
    suspiciousUploads: boolean;
  };
  lastUpdated: string;
}

export default function SecurityDashboard() {
  const [metrics, setMetrics] = useState<SecurityMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    fetchSecurityMetrics();
    
    // Refresh every 30 seconds
    const interval = setInterval(fetchSecurityMetrics, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchSecurityMetrics = async () => {
    try {
      const response = await fetch('/api/security-metrics');
      if (response.ok) {
        const data = await response.json();
        setMetrics(data);
        setError(null);
      } else {
        throw new Error('Failed to fetch security metrics');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (hasIssue: boolean) => {
    return hasIssue ? 'text-red-600 bg-red-100' : 'text-green-600 bg-green-100';
  };

  const getStatusIcon = (hasIssue: boolean) => {
    return hasIssue ? (
      <AlertTriangle className="w-4 h-4" />
    ) : (
      <Shield className="w-4 h-4" />
    );
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={() => setIsVisible(true)}
          className="bg-gray-800 text-white p-3 rounded-full shadow-lg hover:bg-gray-700 transition-colors"
          title="Show Security Dashboard"
        >
          <Shield className="w-5 h-5" />
        </button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-white rounded-lg shadow-xl border border-gray-200 w-80">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <Shield className="w-5 h-5 text-blue-600" />
          <h3 className="font-medium text-gray-900">Security Monitor</h3>
        </div>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-gray-600"
        >
          <EyeOff className="w-4 h-4" />
        </button>
      </div>

      {/* Content */}
      <div className="p-4">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Activity className="w-5 h-5 animate-spin text-blue-600" />
            <span className="ml-2 text-sm text-gray-600">Loading...</span>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <AlertTriangle className="w-8 h-8 text-red-500 mx-auto mb-2" />
            <p className="text-sm text-red-600">{error}</p>
            <button
              onClick={fetchSecurityMetrics}
              className="mt-2 text-xs text-blue-600 hover:text-blue-800"
            >
              Retry
            </button>
          </div>
        ) : metrics ? (
          <div className="space-y-4">
            {/* Overall Status */}
            <div className="grid grid-cols-3 gap-2 text-xs">
              <div className={`p-2 rounded-md flex items-center space-x-1 ${getStatusColor(metrics.suspiciousActivity.rateLimitAbuse)}`}>
                {getStatusIcon(metrics.suspiciousActivity.rateLimitAbuse)}
                <span>Rate Limits</span>
              </div>
              <div className={`p-2 rounded-md flex items-center space-x-1 ${getStatusColor(metrics.suspiciousActivity.authFailureSpike)}`}>
                {getStatusIcon(metrics.suspiciousActivity.authFailureSpike)}
                <span>Auth</span>
              </div>
              <div className={`p-2 rounded-md flex items-center space-x-1 ${getStatusColor(metrics.suspiciousActivity.suspiciousUploads)}`}>
                {getStatusIcon(metrics.suspiciousActivity.suspiciousUploads)}
                <span>Uploads</span>
              </div>
            </div>

            {/* Event Counts */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-700">Recent Events (1h)</h4>
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-600">Rate Limit Violations:</span>
                  <span className={metrics.eventsByType.rateLimitViolations > 0 ? 'text-red-600 font-medium' : 'text-gray-900'}>
                    {metrics.eventsByType.rateLimitViolations}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Auth Failures:</span>
                  <span className={metrics.eventsByType.authFailures > 0 ? 'text-red-600 font-medium' : 'text-gray-900'}>
                    {metrics.eventsByType.authFailures}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Suspicious Uploads:</span>
                  <span className={metrics.eventsByType.suspiciousUploads > 0 ? 'text-red-600 font-medium' : 'text-gray-900'}>
                    {metrics.eventsByType.suspiciousUploads}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">API Errors:</span>
                  <span className={metrics.eventsByType.apiErrors > 0 ? 'text-yellow-600 font-medium' : 'text-gray-900'}>
                    {metrics.eventsByType.apiErrors}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">CORS Violations:</span>
                  <span className={metrics.eventsByType.corsViolations > 0 ? 'text-red-600 font-medium' : 'text-gray-900'}>
                    {metrics.eventsByType.corsViolations}
                  </span>
                </div>
              </div>
            </div>

            {/* Total Events */}
            <div className="pt-2 border-t border-gray-200">
              <div className="flex justify-between items-center text-sm">
                <span className="text-gray-600">Total Events:</span>
                <span className="font-medium text-gray-900">{metrics.totalEvents}</span>
              </div>
              <div className="flex justify-between items-center text-xs text-gray-500 mt-1">
                <span>Last Updated:</span>
                <span>{new Date(metrics.lastUpdated).toLocaleTimeString()}</span>
              </div>
            </div>

            {/* Alerts */}
            {(metrics.suspiciousActivity.rateLimitAbuse || 
              metrics.suspiciousActivity.authFailureSpike || 
              metrics.suspiciousActivity.suspiciousUploads) && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <div className="flex items-center space-x-2 mb-2">
                  <AlertTriangle className="w-4 h-4 text-red-600" />
                  <span className="text-sm font-medium text-red-800">Security Alert</span>
                </div>
                <div className="text-xs text-red-700 space-y-1">
                  {metrics.suspiciousActivity.rateLimitAbuse && (
                    <div>• High rate limit violations detected</div>
                  )}
                  {metrics.suspiciousActivity.authFailureSpike && (
                    <div>• Authentication failure spike detected</div>
                  )}
                  {metrics.suspiciousActivity.suspiciousUploads && (
                    <div>• Suspicious file uploads detected</div>
                  )}
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex space-x-2">
              <button
                onClick={fetchSecurityMetrics}
                className="flex-1 bg-blue-600 text-white text-xs py-2 px-3 rounded-md hover:bg-blue-700 transition-colors"
              >
                Refresh
              </button>
              <button
                onClick={() => window.open('/admin/security-logs', '_blank')}
                className="flex-1 bg-gray-100 text-gray-700 text-xs py-2 px-3 rounded-md hover:bg-gray-200 transition-colors"
              >
                View Logs
              </button>
            </div>
          </div>
        ) : null}
      </div>
    </div>
  );
}
