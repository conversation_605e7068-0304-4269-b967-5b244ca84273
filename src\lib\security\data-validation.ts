/**
 * Advanced Data Validation & Sanitization System
 * Provides comprehensive input validation, SQL injection prevention, and XSS protection
 */

import DOMPurify from 'isomorphic-dompurify';

export interface ValidationResult {
  isValid: boolean;
  sanitizedValue: any;
  errors: string[];
  warnings: string[];
}

export interface ValidationRule {
  name: string;
  test: (value: any) => boolean;
  message: string;
  severity: 'error' | 'warning';
}

// SQL Injection patterns
const SQL_INJECTION_PATTERNS = [
  /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE|UNION|SCRIPT)\b)/gi,
  /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi,
  /('|(\\)|(;)|(--)|(\|)|(\*)|(%)|(\+)|(=))/g,
  /(\/\*[\s\S]*?\*\/)/g,
  /(\bxp_\w+)/gi,
  /(\bsp_\w+)/gi,
  /(\bCAST\s*\()/gi,
  /(\bCONVERT\s*\()/gi,
  /(\bCHAR\s*\()/gi,
  /(\bASCII\s*\()/gi
];

// XSS patterns
const XSS_PATTERNS = [
  /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
  /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
  /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
  /<embed\b[^<]*>/gi,
  /<link\b[^<]*>/gi,
  /<meta\b[^<]*>/gi,
  /javascript:/gi,
  /vbscript:/gi,
  /data:text\/html/gi,
  /on\w+\s*=/gi,
  /expression\s*\(/gi,
  /url\s*\(/gi,
  /@import/gi
];

// NoSQL Injection patterns
const NOSQL_INJECTION_PATTERNS = [
  /\$where/gi,
  /\$ne/gi,
  /\$gt/gi,
  /\$lt/gi,
  /\$gte/gi,
  /\$lte/gi,
  /\$in/gi,
  /\$nin/gi,
  /\$regex/gi,
  /\$exists/gi,
  /\$type/gi,
  /\$mod/gi,
  /\$all/gi,
  /\$size/gi,
  /\$elemMatch/gi,
  /\$slice/gi
];

// LDAP Injection patterns
const LDAP_INJECTION_PATTERNS = [
  /[()&|!]/g,
  /\*\*/g,
  /\\\*/g,
  /\\\(/g,
  /\\\)/g,
  /\\\\/g,
  /\\\0/g
];

/**
 * Check for SQL injection patterns
 */
export function detectSQLInjection(input: string): { detected: boolean; patterns: string[] } {
  const matches: string[] = [];
  
  for (const pattern of SQL_INJECTION_PATTERNS) {
    const match = input.match(pattern);
    if (match) {
      matches.push(...match);
    }
  }
  
  return { detected: matches.length > 0, patterns: matches };
}

/**
 * Check for XSS patterns
 */
export function detectXSS(input: string): { detected: boolean; patterns: string[] } {
  const matches: string[] = [];
  
  for (const pattern of XSS_PATTERNS) {
    const match = input.match(pattern);
    if (match) {
      matches.push(...match);
    }
  }
  
  return { detected: matches.length > 0, patterns: matches };
}

/**
 * Check for NoSQL injection patterns
 */
export function detectNoSQLInjection(input: string): { detected: boolean; patterns: string[] } {
  const matches: string[] = [];
  
  for (const pattern of NOSQL_INJECTION_PATTERNS) {
    const match = input.match(pattern);
    if (match) {
      matches.push(...match);
    }
  }
  
  return { detected: matches.length > 0, patterns: matches };
}

/**
 * Check for LDAP injection patterns
 */
export function detectLDAPInjection(input: string): { detected: boolean; patterns: string[] } {
  const matches: string[] = [];
  
  for (const pattern of LDAP_INJECTION_PATTERNS) {
    const match = input.match(pattern);
    if (match) {
      matches.push(...match);
    }
  }
  
  return { detected: matches.length > 0, patterns: matches };
}

/**
 * Advanced string sanitization
 */
export function sanitizeStringAdvanced(input: string, options: {
  allowHTML?: boolean;
  allowedTags?: string[];
  maxLength?: number;
  removeSQL?: boolean;
  removeXSS?: boolean;
  removeNoSQL?: boolean;
  removeLDAP?: boolean;
} = {}): string {
  if (typeof input !== 'string') {
    return '';
  }
  
  let sanitized = input;
  
  // Remove SQL injection patterns
  if (options.removeSQL !== false) {
    for (const pattern of SQL_INJECTION_PATTERNS) {
      sanitized = sanitized.replace(pattern, '');
    }
  }
  
  // Remove NoSQL injection patterns
  if (options.removeNoSQL !== false) {
    for (const pattern of NOSQL_INJECTION_PATTERNS) {
      sanitized = sanitized.replace(pattern, '');
    }
  }
  
  // Remove LDAP injection patterns
  if (options.removeLDAP !== false) {
    for (const pattern of LDAP_INJECTION_PATTERNS) {
      sanitized = sanitized.replace(pattern, '');
    }
  }
  
  // XSS protection
  if (options.removeXSS !== false) {
    if (options.allowHTML) {
      // Use DOMPurify with allowed tags
      sanitized = DOMPurify.sanitize(sanitized, {
        ALLOWED_TAGS: options.allowedTags || ['b', 'i', 'em', 'strong', 'p', 'br'],
        ALLOWED_ATTR: []
      });
    } else {
      // Remove all HTML
      sanitized = DOMPurify.sanitize(sanitized, {
        ALLOWED_TAGS: [],
        ALLOWED_ATTR: []
      });
    }
  }
  
  // Trim and limit length
  sanitized = sanitized.trim();
  if (options.maxLength && sanitized.length > options.maxLength) {
    sanitized = sanitized.substring(0, options.maxLength);
  }
  
  return sanitized;
}

/**
 * Validate email address
 */
export function validateEmail(email: string): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  if (!email || typeof email !== 'string') {
    errors.push('Email is required and must be a string');
    return { isValid: false, sanitizedValue: '', errors, warnings };
  }
  
  const sanitized = sanitizeStringAdvanced(email, { maxLength: 254 });
  
  // Basic email regex (RFC 5322 compliant)
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
  
  if (!emailRegex.test(sanitized)) {
    errors.push('Invalid email format');
  }
  
  // Check for suspicious patterns
  const sqlCheck = detectSQLInjection(sanitized);
  if (sqlCheck.detected) {
    errors.push('Email contains suspicious SQL patterns');
  }
  
  const xssCheck = detectXSS(sanitized);
  if (xssCheck.detected) {
    errors.push('Email contains suspicious XSS patterns');
  }
  
  return {
    isValid: errors.length === 0,
    sanitizedValue: sanitized,
    errors,
    warnings
  };
}

/**
 * Validate UUID
 */
export function validateUUID(uuid: string): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  if (!uuid || typeof uuid !== 'string') {
    errors.push('UUID is required and must be a string');
    return { isValid: false, sanitizedValue: '', errors, warnings };
  }
  
  const sanitized = sanitizeStringAdvanced(uuid, { maxLength: 36 });
  
  // UUID v4 regex
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  
  if (!uuidRegex.test(sanitized)) {
    errors.push('Invalid UUID format');
  }
  
  return {
    isValid: errors.length === 0,
    sanitizedValue: sanitized,
    errors,
    warnings
  };
}

/**
 * Validate text input with comprehensive security checks
 */
export function validateTextInput(
  input: string,
  options: {
    minLength?: number;
    maxLength?: number;
    allowHTML?: boolean;
    allowedTags?: string[];
    required?: boolean;
    pattern?: RegExp;
    patternMessage?: string;
  } = {}
): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  if (!input || typeof input !== 'string') {
    if (options.required) {
      errors.push('Input is required');
    }
    return { isValid: !options.required, sanitizedValue: '', errors, warnings };
  }
  
  // Sanitize input
  const sanitized = sanitizeStringAdvanced(input, {
    allowHTML: options.allowHTML,
    allowedTags: options.allowedTags,
    maxLength: options.maxLength
  });
  
  // Length validation
  if (options.minLength && sanitized.length < options.minLength) {
    errors.push(`Input must be at least ${options.minLength} characters long`);
  }
  
  if (options.maxLength && sanitized.length > options.maxLength) {
    errors.push(`Input must be no more than ${options.maxLength} characters long`);
  }
  
  // Pattern validation
  if (options.pattern && !options.pattern.test(sanitized)) {
    errors.push(options.patternMessage || 'Input does not match required pattern');
  }
  
  // Security checks
  const sqlCheck = detectSQLInjection(sanitized);
  if (sqlCheck.detected) {
    errors.push('Input contains suspicious SQL injection patterns');
  }
  
  const xssCheck = detectXSS(sanitized);
  if (xssCheck.detected) {
    errors.push('Input contains suspicious XSS patterns');
  }
  
  const nosqlCheck = detectNoSQLInjection(sanitized);
  if (nosqlCheck.detected) {
    warnings.push('Input contains NoSQL query operators');
  }
  
  // Check for sanitization changes
  if (sanitized !== input) {
    warnings.push('Input was sanitized for security');
  }
  
  return {
    isValid: errors.length === 0,
    sanitizedValue: sanitized,
    errors,
    warnings
  };
}

/**
 * Validate JSON input
 */
export function validateJSONInput(input: string): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  if (!input || typeof input !== 'string') {
    errors.push('JSON input is required');
    return { isValid: false, sanitizedValue: null, errors, warnings };
  }
  
  let parsed: any;
  try {
    parsed = JSON.parse(input);
  } catch (error) {
    errors.push('Invalid JSON format');
    return { isValid: false, sanitizedValue: null, errors, warnings };
  }
  
  // Check for suspicious patterns in JSON string
  const sqlCheck = detectSQLInjection(input);
  if (sqlCheck.detected) {
    errors.push('JSON contains suspicious SQL patterns');
  }
  
  const xssCheck = detectXSS(input);
  if (xssCheck.detected) {
    errors.push('JSON contains suspicious XSS patterns');
  }
  
  return {
    isValid: errors.length === 0,
    sanitizedValue: parsed,
    errors,
    warnings
  };
}

/**
 * Create validation middleware for API routes
 */
export function createValidationMiddleware(validators: Record<string, (value: any) => ValidationResult>) {
  return async (request: Request): Promise<{ isValid: boolean; errors: string[]; sanitizedData: any }> => {
    const errors: string[] = [];
    const sanitizedData: any = {};
    
    let body: any = {};
    
    // Parse request body
    try {
      const text = await request.text();
      if (text) {
        body = JSON.parse(text);
      }
    } catch (error) {
      errors.push('Invalid JSON in request body');
      return { isValid: false, errors, sanitizedData: {} };
    }
    
    // Apply validators
    for (const [field, validator] of Object.entries(validators)) {
      if (body[field] !== undefined) {
        const result = validator(body[field]);
        if (!result.isValid) {
          errors.push(...result.errors.map(err => `${field}: ${err}`));
        } else {
          sanitizedData[field] = result.sanitizedValue;
        }
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      sanitizedData
    };
  };
}
