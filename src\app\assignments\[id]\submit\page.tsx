'use client';

import { useEffect, useState } from 'react';
import { useAuthenticationStatus, useUserData } from '@nhost/nextjs';
import { useRouter } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import SubmissionForm from '@/components/submissions/BasicSubmissionForm';

export default function SubmitAssignmentPage({ params }: { params: { id: string } }) {
  const { isAuthenticated, isLoading } = useAuthenticationStatus();
  const router = useRouter();
  const assignmentId = params.id;

  // State for assignment data
  const [assignment, setAssignment] = useState<any>(null);
  const [assignmentLoading, setAssignmentLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch assignment details using API route (bypasses allowlist)
  useEffect(() => {
    if (!isAuthenticated || !assignmentId) {
      setAssignmentLoading(false);
      return;
    }

    const fetchAssignment = async () => {
      try {
        setAssignmentLoading(true);
        setError(null);

        const response = await fetch(`/api/assignments/${assignmentId}`);
        const result = await response.json();

        if (response.ok && result.success) {
          setAssignment(result.assignment);
        } else {
          throw new Error(result.error || 'Failed to fetch assignment');
        }
      } catch (err) {
        console.error('❌ Failed to fetch assignment:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setAssignmentLoading(false);
      }
    };

    fetchAssignment();
  }, [isAuthenticated, assignmentId]);

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/sign-in');
    }
  }, [isLoading, isAuthenticated, router]);

  if (isLoading || assignmentLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent mx-auto mb-4"></div>
            <p className="text-gray-600">Loading assignment details...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect in the useEffect
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <div className="max-w-md mx-auto">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h1 className="text-2xl font-light mb-4 text-gray-900">Error Loading Assignment</h1>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={() => router.push('/assignments')}
              className="bg-accent text-white px-6 py-2 rounded-full hover:bg-accent/90 transition-colors"
            >
              Back to Assignments
            </button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!assignment) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <div className="max-w-md mx-auto">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h1 className="text-2xl font-light mb-4 text-gray-900">Assignment Not Found</h1>
            <p className="text-gray-600 mb-6">The assignment you're looking for doesn't exist or you don't have access to it.</p>
            <button
              onClick={() => router.push('/assignments')}
              className="bg-accent text-white px-6 py-2 rounded-full hover:bg-accent/90 transition-colors"
            >
              Back to Assignments
            </button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto">
        <SubmissionForm
          assignmentId={assignmentId}
          assignmentTitle={assignment.title}
          assignmentDescription={assignment.description || ''}
        />
      </div>
    </DashboardLayout>
  );
}
