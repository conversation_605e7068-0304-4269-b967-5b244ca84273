import Anthropic from '@anthropic-ai/sdk';
import {
  AssessmentConfig,
  AssessmentResult,
  Submission,
  AIProviderResponse,
  TokenUsage,
  AIAssessmentError,
  RateLimitError,
  QuotaExceededError,
  InvalidResponseError
} from '@/types/ai-assessment';
import { getAIConfig, calculateCost } from '../config';

export class AnthropicProvider {
  private client: Anthropic;
  private config: ReturnType<typeof getAIConfig>['anthropic'];

  constructor() {
    const aiConfig = getAIConfig();
    this.config = aiConfig.anthropic;

    this.client = new Anthropic({
      apiKey: this.config.apiKey,
    });
  }

  async assessSubmission(
    submission: Submission,
    config: AssessmentConfig
  ): Promise<AIProviderResponse> {
    const startTime = Date.now();

    try {
      console.log(`🤖 Starting Anthropic assessment for submission ${submission.id}`);

      const prompt = this.buildPrompt(submission, config);

      const response = await this.client.messages.create({
        model: this.config.defaultModel,
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
        messages: [
          { role: 'user', content: prompt }
        ],
      });

      const processingTime = Date.now() - startTime;

      // Extract token usage
      const usage = response.usage;
      const tokenUsage: TokenUsage = {
        inputTokens: usage.input_tokens || 0,
        outputTokens: usage.output_tokens || 0,
        totalTokens: (usage.input_tokens || 0) + (usage.output_tokens || 0),
        estimatedCost: calculateCost(
          'anthropic',
          this.config.defaultModel,
          usage.input_tokens || 0,
          usage.output_tokens || 0
        ),
      };

      // Parse the response
      const content = response.content[0];
      if (!content || content.type !== 'text') {
        throw new InvalidResponseError('anthropic', response);
      }

      // Extract JSON from the response (Claude sometimes wraps JSON in markdown)
      const jsonMatch = content.text.match(/```json\n([\s\S]*?)\n```/) ||
                       content.text.match(/\{[\s\S]*\}/);

      if (!jsonMatch) {
        throw new InvalidResponseError('anthropic', content.text);
      }

      const assessmentData = JSON.parse(jsonMatch[1] || jsonMatch[0]);
      const result = this.parseAssessmentResponse(
        assessmentData,
        submission,
        config,
        processingTime,
        tokenUsage
      );

      console.log(`✅ Anthropic assessment completed in ${processingTime}ms`);
      console.log(`💰 Cost: $${tokenUsage.estimatedCost.toFixed(4)}`);

      return {
        success: true,
        result,
        tokenUsage,
      };

    } catch (error: any) {
      console.error('❌ Anthropic assessment failed:', error);
      return this.handleError(error);
    }
  }

  private buildPrompt(submission: Submission, config: AssessmentConfig): string {
    // DEBUG: Log assignment context being sent to AI
    console.log(`🎯 ANTHROPIC PROMPT ASSIGNMENT CONTEXT:`);
    console.log(`📝 Assignment Title: "${submission.assignment.title}"`);
    console.log(`📄 Assignment Description: "${submission.assignment.description}"`);
    console.log(`🆔 Assignment ID: ${submission.assignment.id}`);

    const criteriaText = config.criteria
      .map(c => `- ID: ${c.id} | ${c.name} (${c.weight}%): ${c.description}`)
      .join('\n');

    const featuresText = Object.entries(config.features)
      .filter(([_, enabled]) => enabled)
      .map(([feature, _]) => feature.replace(/([A-Z])/g, ' $1').toLowerCase())
      .join(', ');

    return `You are an expert educator with deep knowledge across multiple academic disciplines. I need you to assess a student submission with careful attention to detail and nuanced understanding.

ASSIGNMENT CONTEXT:
- Assignment: ${submission.assignment.title}
- Description: ${submission.assignment.description}

SUBMISSION DETAILS:
- Student: ${submission.studentName}
- File: ${submission.fileName}
- Type: ${submission.fileType}

ASSESSMENT CRITERIA:
${criteriaText}

SUBMISSION CONTENT:
${submission.content}

ASSESSMENT REQUIREMENTS:
Please provide a comprehensive assessment that includes:

1. **Overall Grade**: Using ${config.assessmentType} scale
2. **Detailed Scoring**: Score each criterion individually (0-100)
3. **Nuanced Feedback**: Provide thoughtful, specific feedback for each criterion
4. **Holistic Analysis**: Consider the work as a whole, noting connections between criteria
5. **Constructive Guidance**: Highlight strengths and provide actionable improvement suggestions
6. **Confidence Assessment**: Rate your confidence in this assessment (0-1)

${featuresText ? `ADDITIONAL ANALYSIS: Please also analyze for ${featuresText}.` : ''}

FEEDBACK STYLE: ${config.feedbackLevel}
${config.feedbackLevel === 'detailed' ? 'Provide comprehensive, nuanced feedback with specific examples and detailed explanations.' : ''}
${config.feedbackLevel === 'brief' ? 'Provide focused, essential feedback points.' : ''}
${config.feedbackLevel === 'standard' ? 'Provide balanced feedback with good detail and clarity.' : ''}

Please format your response as JSON within code blocks:

\`\`\`json
{
  "overall_grade": "A",
  "overall_score": 85,
  "criteria_scores": [
    {
      "criteria_id": "USE_THE_EXACT_ID_FROM_CRITERIA_LIST_ABOVE",
      "criteria_name": "USE_THE_EXACT_NAME_FROM_CRITERIA_LIST_ABOVE",
      "score": 85,
      "max_score": 100,
      "feedback": "Detailed feedback for this specific criterion...",
      "strengths": ["Specific strength 1", "Specific strength 2"],
      "improvements": ["Specific improvement 1", "Specific improvement 2"]
    }
  ],
  "strengths": ["Overall strength 1", "Overall strength 2"],
  "areas_for_improvement": ["Area 1", "Area 2"],
  "suggestions": ["Specific suggestion 1", "Specific suggestion 2"],
  "feedback_summary": "Comprehensive summary of the assessment...",
  "confidence": 0.85
}
\`\`\`

CRITICAL: For each criterion in the criteria_scores array, you MUST use the exact ID and name from the ASSESSMENT CRITERIA list above. Do not make up new IDs or names.

Focus on providing thoughtful, nuanced assessment that recognizes both the technical and creative aspects of the work.`;
  }

  private parseAssessmentResponse(
    data: any,
    submission: Submission,
    config: AssessmentConfig,
    processingTime: number,
    tokenUsage: TokenUsage
  ): AssessmentResult {
    // Transform AI criteria_scores to match CriteriaFeedback interface
    const detailedFeedback = (data.criteria_scores || []).map((criteriaScore: any) => ({
      criteriaId: criteriaScore.criteria_id || criteriaScore.criteriaId || '',
      criteriaName: criteriaScore.criteria_name || criteriaScore.criteriaName || '',
      score: criteriaScore.score || 0,
      maxScore: criteriaScore.max_score || criteriaScore.maxScore || 100,
      feedback: criteriaScore.feedback || '',
      strengths: criteriaScore.strengths || [],
      improvements: criteriaScore.improvements || []
    }));

    console.log('🔄 Transformed Anthropic criteria scores:');
    detailedFeedback.forEach((feedback: any, index: number) => {
      console.log(`  ${index + 1}. ID: "${feedback.criteriaId}", Name: "${feedback.criteriaName}", Score: ${feedback.score}`);
    });

    return {
      id: `assessment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      submissionId: submission.id,
      assignmentId: submission.assignment.id,
      overallGrade: data.overall_grade || 'N/A',
      overallScore: data.overall_score || 0,
      feedbackSummary: data.feedback_summary || '',
      detailedFeedback: detailedFeedback,
      strengths: data.strengths || [],
      areasForImprovement: data.areas_for_improvement || [],
      suggestions: data.suggestions || [],
      confidenceScore: data.confidence || 0,
      processingTimeMs: processingTime,
      tokenUsage,
      aiProvider: 'anthropic',
      modelUsed: this.config.defaultModel,
      createdAt: new Date(),
    };
  }

  private handleError(error: any): AIProviderResponse {
    if (error.status === 429) {
      const retryAfter = error.headers?.['retry-after'];
      throw new RateLimitError('anthropic', retryAfter ? parseInt(retryAfter) : undefined);
    }

    if (error.status === 402 || error.message?.includes('credit')) {
      throw new QuotaExceededError('anthropic');
    }

    if (error instanceof SyntaxError) {
      throw new InvalidResponseError('anthropic', error.message);
    }

    throw new AIAssessmentError(
      error.message || 'Unknown Anthropic error',
      'anthropic',
      error.status,
      [500, 502, 503, 504].includes(error.status)
    );
  }
}
