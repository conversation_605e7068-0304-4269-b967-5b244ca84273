/**
 * React Hook for Filtered Submissions
 * 
 * Provides a consistent interface for fetching and filtering student submissions
 * across all components in the application.
 */

import { useQuery } from '@apollo/client';
import { useAuthenticationStatus } from '@nhost/nextjs';
import { 
  useSubmissionFilter, 
  FILTER_PRESETS, 
  type SubmissionFilterOptions,
  type StudentSubmission 
} from '@/lib/submission-filters';
import { 
  GET_ASSIGNMENT_SUBMISSIONS, 
  GET_ALL_SUBMISSIONS,
  GET_RECENT_SUBMISSIONS,
  getSubmissionQuery 
} from '@/lib/gql/submission-queries';

export interface UseFilteredSubmissionsOptions {
  /** Assignment ID to filter by */
  assignmentId?: string;
  /** Query context - determines which GraphQL query to use */
  context?: 'assignment' | 'all' | 'recent' | 'with-results' | 'admin';
  /** Preset filter configuration */
  preset?: keyof typeof FILTER_PRESETS;
  /** Skip the query entirely */
  skip?: boolean;
  /** Filter options */
  statusFilter?: string[];
  limit?: number;
  sortOrder?: 'asc' | 'desc';
}

export interface UseFilteredSubmissionsResult {
  /** Filtered submissions */
  submissions: StudentSubmission[];
  /** All submissions before filtering */
  allSubmissions: StudentSubmission[];
  /** Loading state */
  loading: boolean;
  /** Error state */
  error: any;
  /** Filter summary text */
  filterSummary: string | null;
  /** Whether any submissions were filtered out */
  hasFiltered: boolean;
  /** Total count before filtering */
  totalCount: number;
  /** Count after filtering */
  filteredCount: number;
  /** Refetch function */
  refetch: () => void;
  /** Filter breakdown */
  filterBreakdown: {
    wrongAssignment: number;
    statusFiltered: number;
  };
}

/**
 * Hook to fetch and filter student submissions
 * 
 * @param options Configuration options for fetching and filtering
 * @returns Filtered submissions with metadata
 */
export function useFilteredSubmissions(
  options: UseFilteredSubmissionsOptions = {}
): UseFilteredSubmissionsResult {
  const { isAuthenticated } = useAuthenticationStatus();
  
  const {
    assignmentId,
    context = 'all',
    preset,
    skip = false,
    ...filterOptions
  } = options;

  // Determine which query to use
  const query = getSubmissionQuery(context);
  
  // Prepare query variables
  const variables: any = {};
  if (assignmentId && (context === 'assignment' || context === 'with-results')) {
    variables.assignment_id = assignmentId;
  }

  // Execute GraphQL query
  const { data, loading, error, refetch } = useQuery(query, {
    variables,
    skip: skip || !isAuthenticated,
    onError: (error) => {
      console.error('GraphQL Query Error in useFilteredSubmissions:', error);
    },
    onCompleted: (data) => {
      console.log('Submissions query completed:', {
        context,
        assignmentId,
        count: data?.student_submissions?.length || 0,
        submissions: data?.student_submissions
      });
    }
  });

  // Get all submissions from query result
  const allSubmissions: StudentSubmission[] = data?.student_submissions || [];

  // Apply preset if specified
  let finalFilterOptions: SubmissionFilterOptions = { ...filterOptions };
  if (preset && FILTER_PRESETS[preset]) {
    const presetOptions = FILTER_PRESETS[preset];
    
    // Copy properties from preset, handling each property individually to avoid type errors
    finalFilterOptions = {
      ...finalFilterOptions,
      sortOrder: presetOptions.sortOrder
    };
    
    // Handle optional properties
    if ('statusFilter' in presetOptions && presetOptions.statusFilter) {
      finalFilterOptions.statusFilter = [...presetOptions.statusFilter];
    }
    
    if ('limit' in presetOptions && presetOptions.limit) {
      finalFilterOptions.limit = presetOptions.limit;
    }
    
    // Allow overrides from filterOptions
    finalFilterOptions = {
      ...finalFilterOptions,
      ...filterOptions
    };
  }

  // Add assignment ID to filter options if provided
  if (assignmentId) {
    finalFilterOptions.assignmentId = assignmentId;
  }

  // Apply filtering
  const filterResult = useSubmissionFilter(allSubmissions, finalFilterOptions);

  return {
    submissions: filterResult.submissions,
    allSubmissions,
    loading,
    error,
    filterSummary: filterResult.summary,
    hasFiltered: filterResult.hasFiltered,
    totalCount: filterResult.totalCount,
    filteredCount: filterResult.submissions.length,
    refetch,
    filterBreakdown: filterResult.filterBreakdown
  };
}

/**
 * Convenience hook for assignment-specific submissions
 */
export function useAssignmentSubmissions(assignmentId: string, options: Omit<UseFilteredSubmissionsOptions, 'assignmentId' | 'context'> = {}) {
  return useFilteredSubmissions({
    ...options,
    assignmentId,
    context: 'assignment',
    preset: 'ASSIGNMENT_FOCUSED'
  });
}

/**
 * Convenience hook for bulk assessment workflows
 */
export function useBulkAssessmentSubmissions(assignmentId: string, options: Omit<UseFilteredSubmissionsOptions, 'assignmentId' | 'preset'> = {}) {
  return useFilteredSubmissions({
    ...options,
    assignmentId,
    context: 'assignment',
    preset: 'BULK_ASSESSMENT'
  });
}

/**
 * Convenience hook for recent activity
 */
export function useRecentSubmissions(options: Omit<UseFilteredSubmissionsOptions, 'context' | 'preset'> = {}) {
  return useFilteredSubmissions({
    ...options,
    context: 'recent',
    preset: 'RECENT_ACTIVITY'
  });
}

/**
 * Convenience hook for admin/debugging (shows all data)
 */
export function useAllSubmissionsAdmin(options: Omit<UseFilteredSubmissionsOptions, 'context' | 'preset'> = {}) {
  return useFilteredSubmissions({
    ...options,
    context: 'admin',
    preset: 'ADMIN_VIEW'
  });
}
