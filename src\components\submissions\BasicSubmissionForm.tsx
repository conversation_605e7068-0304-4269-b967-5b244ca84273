import { useState, useRef, useEffect, ChangeEvent } from 'react';
import { useMutation, gql } from '@apollo/client';
import { useUserData } from '@nhost/nextjs';
import { useMultipleFilesUpload } from '@nhost/nextjs';
import Button from '@/components/ui/Button';

const CREATE_SUBMISSION = gql`
  mutation CreateSubmission(
    $assignment_id: uuid!,
    $file_ids: [submission_files_insert_input!]!
  ) {
    insert_student_submissions_one(object: {
      assignment_id: $assignment_id,
      student_id: "X-Hasura-User-Id",
      status: "submitted",
      submitted_at: "now()",
      submission_files: {
        data: $file_ids
      }
    }) {
      id
      status
      submitted_at
      submission_files {
        id
        file_id
        file_name
      }
    }
  }
`;

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

interface SubmissionFormProps {
  assignmentId: string;
  assignmentTitle: string;
  assignmentDescription: string;
}

export default function BasicSubmissionForm({ assignmentId, assignmentTitle, assignmentDescription }: SubmissionFormProps) {
  const user = useUserData();
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [studentName, setStudentName] = useState('');
  const [title, setTitle] = useState(assignmentTitle);
  const [description, setDescription] = useState(assignmentDescription);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { upload, isUploaded, isUploading, progress, files: uploadedFiles } = useMultipleFilesUpload();
  const [createSubmission] = useMutation(CREATE_SUBMISSION);

  // Pre-fill student name if user data is available
  useEffect(() => {
    if (user?.displayName) {
      setStudentName(user.displayName);
    }
  }, [user]);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);

    // Validate files
    const validFiles = files.filter(file => {
      // Check file size
      if (file.size > MAX_FILE_SIZE) {
        setErrorMessage(`File ${file.name} exceeds the 10MB size limit`);
        return false;
      }

      // Check file type
      const type = file.type;
      const name = file.name.toLowerCase();

      const validMimeTypes = [
        'image/',
        'text/',
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/zip',
        'application/x-zip-compressed'
      ];

      const validExtensions = [
        '.js', '.ts', '.jsx', '.tsx',
        '.py', '.java', '.cpp', '.c', '.h',
        '.html', '.css', '.scss', '.json',
        '.md', '.txt', '.csv'
      ];

      const isValidType = validMimeTypes.some(mime => type.startsWith(mime)) ||
                         validExtensions.some(ext => name.endsWith(ext));

      if (!isValidType) {
        setErrorMessage(`File ${file.name} has an unsupported file type`);
        return false;
      }

      return true;
    });

    if (validFiles.length > 0) {
      setSelectedFiles(validFiles);
      setErrorMessage(null);
    }
  };

  const getFileType = (file: File): string => {
    const type = file.type;
    const name = file.name.toLowerCase();

    if (type.startsWith('image/')) return 'image';
    if (type.startsWith('text/') ||
        type.startsWith('application/pdf') ||
        type.startsWith('application/msword') ||
        type.startsWith('application/vnd.openxmlformats-officedocument.wordprocessingml.document') ||
        ['.js', '.ts', '.py', '.java', '.cpp', '.c', '.h', '.html', '.css', '.scss', '.json', '.md', '.txt', '.csv']
          .some(ext => name.endsWith(ext))) {
      return 'document';
    }
    return 'other';
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form fields
    if (!studentName.trim()) {
      setErrorMessage('Please enter your name');
      return;
    }

    if (selectedFiles.length === 0) {
      setErrorMessage('Please select at least one file to upload');
      return;
    }

    setIsSubmitting(true);
    setUploadProgress(0);
    setErrorMessage(null);

    try {
      console.log('Starting upload process for', selectedFiles.length, 'files');

      // Upload all files at once using the useMultipleFilesUpload hook
      const uploadResult = await upload({
        files: selectedFiles,
        bucketId: 'default'
      });

      console.log('Upload initiated successfully', uploadResult);

    } catch (error) {
      console.error('Error during upload process:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      setErrorMessage(`Upload failed: ${errorMessage}`);
      setIsSubmitting(false);
    }
  };

  // Watch for upload progress and completion
  useEffect(() => {
    if (isUploading && typeof progress === 'number') {
      setUploadProgress(progress);
      console.log('Upload progress:', progress);
    }
  }, [isUploading, progress]);

  // Handle upload errors - removed since error property doesn't exist on hook

  // Handle upload completion and create submission record
  useEffect(() => {
    const createSubmissionRecord = async () => {
      if (isUploaded && uploadedFiles && uploadedFiles.length > 0) {
        try {
          console.log('Files uploaded successfully:', uploadedFiles);

          // Prepare data for submission record using selected files
          // Note: uploadedFiles contains complex Nhost state objects, so we'll use selectedFiles
          const submissionFilesData = selectedFiles.map((file, index) => {
            return {
              file_id: `uploaded_${index}`, // Temporary ID since we can't access the actual upload result
              file_name: file.name,
              file_type: getFileType(file)
            };
          });

          console.log('Creating submission record with assignment_id:', assignmentId);
          const result = await createSubmission({
            variables: {
              assignment_id: assignmentId,
              file_ids: submissionFilesData,
            },
          });

          console.log('Submission created:', result);

          if (result.data) {
            alert('Submission created successfully!');
            window.location.href = `/submissions/${result.data.insert_student_submissions_one.id}`;
          }
        } catch (error) {
          console.error('Error creating submission record:', error);
          const errorMessage = error instanceof Error ? error.message : String(error);
          setErrorMessage(`Submission failed: ${errorMessage}`);
        } finally {
          setIsSubmitting(false);
        }
      }
    };

    createSubmissionRecord();
  }, [isUploaded, uploadedFiles, assignmentId, createSubmission, selectedFiles]);

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Student Name Field */}
        <div>
          <label htmlFor="student-name" className="block text-sm font-medium text-gray-700 mb-1">
            Student Name <span className="text-red-500">*</span>
          </label>
          <input
            id="student-name"
            type="text"
            value={studentName}
            onChange={(e) => setStudentName(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-accent focus:border-accent"
            required
          />
        </div>

        {/* Assignment Title Field */}
        <div>
          <label htmlFor="assignment-title" className="block text-sm font-medium text-gray-700 mb-1">
            Assignment Title <span className="text-red-500">*</span>
          </label>
          <input
            id="assignment-title"
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-accent focus:border-accent"
            required
            readOnly
          />
        </div>

        {/* Assignment Description Field */}
        <div>
          <label htmlFor="assignment-description" className="block text-sm font-medium text-gray-700 mb-1">
            Assignment Description
          </label>
          <textarea
            id="assignment-description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-accent focus:border-accent"
            readOnly
          />
        </div>

        {/* File Upload Field */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Upload Files <span className="text-red-500">*</span>
          </label>
          <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
            <div className="space-y-1 text-center">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                stroke="currentColor"
                fill="none"
                viewBox="0 0 48 48"
                aria-hidden="true"
              >
                <path
                  d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                  strokeWidth={2}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              <div className="flex text-sm text-gray-600">
                <label
                  htmlFor="file-upload"
                  className="relative cursor-pointer bg-white rounded-md font-medium text-accent hover:text-accent/80 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-accent"
                >
                  <span>Upload files</span>
                  <input
                    id="file-upload"
                    name="file-upload"
                    type="file"
                    multiple
                    className="sr-only"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                  />
                </label>
                <p className="pl-1">or drag and drop</p>
              </div>
              <p className="text-xs text-gray-500">
                Up to 10MB per file
              </p>
            </div>
          </div>

          {/* Selected Files */}
          {selectedFiles.length > 0 && (
            <div className="mt-4">
              <h4 className="text-sm font-medium text-gray-700">Selected Files:</h4>
              <ul className="mt-2 text-sm text-gray-600">
                {selectedFiles.map((file, index) => (
                  <li key={index} className="flex items-center">
                    <svg className="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    {file.name} ({(file.size / 1024).toFixed(1)} KB)
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Error Message */}
          {errorMessage && (
            <p className="mt-2 text-sm text-red-600">{errorMessage}</p>
          )}
        </div>

        {/* File Type Information */}
        <div className="bg-gray-50 rounded-md p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-2">Permitted file types:</h3>
          <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
            <div>
              <p className="font-medium mb-1">Images</p>
              <p className="text-gray-500">PNG, JPG, GIF</p>
            </div>
            <div>
              <p className="font-medium mb-1">Documents</p>
              <p className="text-gray-500">PDF, DOC, DOCX</p>
            </div>
            <div>
              <p className="font-medium mb-1">Code Files</p>
              <p className="text-gray-500">JS, TS, PY, JAVA, etc.</p>
            </div>
            <div>
              <p className="font-medium mb-1">Archives</p>
              <p className="text-gray-500">ZIP (containing any of the above)</p>
            </div>
          </div>
        </div>

        {/* Upload Progress */}
        {isSubmitting && uploadProgress > 0 && (
          <div>
            <p className="text-sm text-gray-600 mb-1">Uploading: {uploadProgress}%</p>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-accent h-2.5 rounded-full transition-all duration-300"
                style={{ width: `${uploadProgress}%` }}
              ></div>
            </div>
          </div>
        )}

        {/* Submit Button */}
        <div className="flex justify-end">
          <Button
            type="submit"
            disabled={isSubmitting}
            variant="primary"
          >
            {isSubmitting ? 'Submitting...' : 'Submit Assignment'}
          </Button>
        </div>
      </form>
    </div>
  );
}
