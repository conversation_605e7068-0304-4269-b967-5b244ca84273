import React from 'react';

interface ProgressIndicatorProps {
  currentStep: number;
}

const steps = [
  { number: 1, title: 'Student Submission', completed: true },
  { number: 2, title: 'Configuration', completed: false },
  { number: 3, title: 'Review & Submit', completed: false }
];

export default function ProgressIndicator({ currentStep }: ProgressIndicatorProps) {
  return (
    <div className="flex items-center justify-center space-x-8 py-6">
      {steps.map((step, index) => (
        <div key={step.number} className="flex items-center">
          {/* Step Circle */}
          <div className="flex items-center">
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                step.number < currentStep
                  ? 'bg-green-500 text-white' // Completed
                  : step.number === currentStep
                  ? 'bg-orange-500 text-white' // Current
                  : 'bg-gray-200 text-gray-600' // Future
              }`}
            >
              {step.number < currentStep ? (
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              ) : (
                step.number
              )}
            </div>
            
            {/* Step Title */}
            <span
              className={`ml-3 text-sm font-medium ${
                step.number === currentStep
                  ? 'text-orange-600'
                  : step.number < currentStep
                  ? 'text-green-600'
                  : 'text-gray-500'
              }`}
            >
              {step.title}
            </span>
          </div>

          {/* Connector Line */}
          {index < steps.length - 1 && (
            <div
              className={`w-16 h-0.5 mx-4 ${
                step.number < currentStep ? 'bg-green-500' : 'bg-gray-200'
              }`}
            />
          )}
        </div>
      ))}
    </div>
  );
}
