/**
 * Authenticated Fetch Utility
 * Provides helper functions for making authenticated API requests
 */

import { useAccessToken } from '@nhost/nextjs';

/**
 * Custom hook for authenticated fetch requests
 */
export function useAuthenticatedFetch() {
  const accessToken = useAccessToken();

  const authenticatedFetch = async (
    url: string,
    options: RequestInit = {}
  ): Promise<Response> => {
    if (!accessToken) {
      throw new Error('No access token available');
    }

    const headers = {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json',
      ...options.headers,
    };

    return fetch(url, {
      ...options,
      headers,
    });
  };

  const authenticatedFetchJSON = async (
    url: string,
    options: RequestInit = {}
  ): Promise<any> => {
    const response = await authenticatedFetch(url, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    return response.json();
  };

  return {
    authenticatedFetch,
    authenticatedFetchJSON,
    hasToken: !!accessToken
  };
}

/**
 * Standalone authenticated fetch function (for use outside React components)
 */
export async function makeAuthenticatedRequest(
  url: string,
  token: string,
  options: RequestInit = {}
): Promise<Response> {
  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
    ...options.headers,
  };

  return fetch(url, {
    ...options,
    headers,
  });
}

/**
 * Helper to create authenticated fetch options
 */
export function createAuthHeaders(token: string): HeadersInit {
  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  };
}

/**
 * API response wrapper with error handling
 */
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
}

/**
 * Typed API request helper
 */
export async function apiRequest<T = any>(
  url: string,
  token: string,
  options: RequestInit = {}
): Promise<APIResponse<T>> {
  try {
    const response = await makeAuthenticatedRequest(url, token, options);
    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: data.error || `HTTP ${response.status}`,
        code: data.code
      };
    }

    return {
      success: true,
      data
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Common API endpoints with authentication
 */
export class AuthenticatedAPI {
  constructor(private token: string) {}

  async getAssignments(): Promise<APIResponse> {
    return apiRequest('/api/assignments', this.token);
  }

  async createAssignment(data: { title: string; description: string }): Promise<APIResponse> {
    return apiRequest('/api/assignments', this.token, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  async getSubmissions(assignmentId: string): Promise<APIResponse> {
    return apiRequest(`/api/submissions?assignment_id=${assignmentId}`, this.token);
  }

  async createSubmission(data: any): Promise<APIResponse> {
    return apiRequest('/api/submissions', this.token, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  async resetAssignment(assignmentId: string): Promise<APIResponse> {
    return apiRequest('/api/assignment-reset', this.token, {
      method: 'POST',
      body: JSON.stringify({ assignmentId, resetType: 'delete' })
    });
  }

  async uploadFile(file: File, bucketId: string = 'default'): Promise<APIResponse> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('bucketId', bucketId);

    return apiRequest('/api/upload', this.token, {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': `Bearer ${this.token}`,
        // Don't set Content-Type for FormData - let browser set it with boundary
      }
    });
  }
}

/**
 * React hook for authenticated API operations
 */
export function useAuthenticatedAPI() {
  const accessToken = useAccessToken();

  if (!accessToken) {
    return null;
  }

  return new AuthenticatedAPI(accessToken);
}

/**
 * Error handling for authentication failures
 */
export function handleAuthError(error: any): string {
  if (error?.code === 'AUTH_REQUIRED') {
    return 'Authentication required. Please sign in and try again.';
  }
  
  if (error?.code === 'INVALID_TOKEN') {
    return 'Your session has expired. Please sign in again.';
  }
  
  if (error?.code === 'INSUFFICIENT_PERMISSIONS') {
    return 'You do not have permission to perform this action.';
  }

  return error?.message || 'An error occurred. Please try again.';
}

/**
 * Check if error is authentication-related
 */
export function isAuthError(error: any): boolean {
  const authCodes = ['AUTH_REQUIRED', 'INVALID_TOKEN', 'INSUFFICIENT_PERMISSIONS'];
  return authCodes.includes(error?.code);
}

/**
 * Retry wrapper for authentication errors
 */
export async function withAuthRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 1
): Promise<T> {
  let lastError: any;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      if (!isAuthError(error) || attempt === maxRetries) {
        throw error;
      }
      
      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
    }
  }

  throw lastError;
}
