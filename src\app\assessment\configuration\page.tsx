'use client';

import { useState, useEffect } from 'react';
import { useAuthenticationStatus, useUserData } from '@nhost/nextjs';
import { useRouter, useSearchParams } from 'next/navigation';
import { useQuery, gql } from '@apollo/client';
import DashboardLayout from '@/components/layout/DashboardLayout';
import ProgressIndicator from '@/components/assessment/ProgressIndicator';
import TemplateSelector from '@/components/assessment/TemplateSelector';
import CriteriaBuilder from '@/components/assessment/CriteriaBuilder';

// GraphQL query to get submissions for the assignment
const GET_ASSIGNMENT_SUBMISSIONS = gql`
  query GetAssignmentSubmissions($assignment_id: uuid!) {
    student_submissions(where: { assignment_id: { _eq: $assignment_id } }) {
      id
      student_name
      file_path
      file_type
      upload_date
    }
  }
`;

export default function AssessmentConfigurationPage() {
  const { isAuthenticated, isLoading } = useAuthenticationStatus();
  const user = useUserData();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get assignment ID and mode from URL params
  const assignmentId = searchParams.get('assignmentId') || searchParams.get('assignment');
  const assessmentMode = searchParams.get('mode') || 'single';
  const isBulkMode = assessmentMode === 'bulk';



  // Query to get submissions for this assignment
  const { data: submissionsData, loading: submissionsLoading } = useQuery(
    GET_ASSIGNMENT_SUBMISSIONS,
    {
      variables: { assignment_id: assignmentId },
      skip: !assignmentId || !isAuthenticated,
      onCompleted: (data) => {
        // Data loaded successfully
      }
    }
  );

  // Get template from URL params or localStorage, fallback to null
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);

  // Template-specific criteria
  const templateCriteria = {
    'reading-comprehension': [
      {
        id: '1',
        name: 'Reading Comprehension',
        description: 'Demonstrates understanding of main ideas, supporting details, and text structure.',
        weight: 30
      },
      {
        id: '2',
        name: 'Analysis & Interpretation',
        description: 'Shows ability to analyze text meaning, make inferences, and draw conclusions.',
        weight: 25
      },
      {
        id: '3',
        name: 'Critical Thinking',
        description: 'Evaluates arguments, identifies bias, and makes connections to prior knowledge.',
        weight: 25
      },
      {
        id: '4',
        name: 'Written Response Quality',
        description: 'Clear, organized written responses with proper grammar and vocabulary.',
        weight: 20
      }
    ],
    'essay-analysis': [
      {
        id: '1',
        name: 'Thesis & Argumentation',
        description: 'Presents a clear, focused thesis statement and develops a coherent argument with strong supporting evidence.',
        weight: 25
      },
      {
        id: '2',
        name: 'Organization & Structure',
        description: 'Demonstrates logical organization with effective transitions between ideas. Introduction engages reader and conclusion provides closure.',
        weight: 20
      },
      {
        id: '3',
        name: 'Language & Style',
        description: 'Uses precise vocabulary, varied sentence structure, and appropriate tone for the intended audience and purpose.',
        weight: 20
      },
      {
        id: '4',
        name: 'Evidence & Support',
        description: 'Uses relevant, credible sources and examples to support arguments effectively.',
        weight: 20
      },
      {
        id: '5',
        name: 'Grammar & Mechanics',
        description: 'Demonstrates mastery of grammar, punctuation, and spelling conventions.',
        weight: 15
      }
    ],
    'creative-writing': [
      {
        id: '1',
        name: 'Creativity & Originality',
        description: 'Shows imaginative thinking, unique perspective, and original ideas.',
        weight: 30
      },
      {
        id: '2',
        name: 'Narrative Structure',
        description: 'Effective plot development, pacing, and story organization.',
        weight: 25
      },
      {
        id: '3',
        name: 'Character Development',
        description: 'Well-developed, believable characters with clear motivations.',
        weight: 20
      },
      {
        id: '4',
        name: 'Style & Voice',
        description: 'Distinctive writing style and consistent narrative voice.',
        weight: 25
      }
    ],
    'map-analysis': [
      {
        id: '1',
        name: 'Spatial Understanding',
        description: 'Demonstrates ability to read and interpret maps, scales, and geographic coordinates.',
        weight: 30
      },
      {
        id: '2',
        name: 'Geographic Features',
        description: 'Identifies and analyzes physical and human geographic features accurately.',
        weight: 25
      },
      {
        id: '3',
        name: 'Data Interpretation',
        description: 'Interprets geographic data, patterns, and relationships from maps and charts.',
        weight: 25
      },
      {
        id: '4',
        name: 'Critical Analysis',
        description: 'Evaluates geographic information and draws logical conclusions.',
        weight: 20
      }
    ],
    'regional-study': [
      {
        id: '1',
        name: 'Regional Knowledge',
        description: 'Demonstrates comprehensive understanding of regional characteristics and features.',
        weight: 25
      },
      {
        id: '2',
        name: 'Cultural Understanding',
        description: 'Shows awareness of cultural practices, traditions, and social structures.',
        weight: 25
      },
      {
        id: '3',
        name: 'Environmental Analysis',
        description: 'Analyzes human-environment interactions and their impacts.',
        weight: 25
      },
      {
        id: '4',
        name: 'Comparative Analysis',
        description: 'Compares and contrasts different regions effectively.',
        weight: 25
      }
    ],
    'visual-analysis': [
      {
        id: '1',
        name: 'Visual Elements',
        description: 'Identifies and analyzes use of color, line, shape, texture, and composition.',
        weight: 30
      },
      {
        id: '2',
        name: 'Artistic Techniques',
        description: 'Recognizes and evaluates artistic methods, media, and technical skills.',
        weight: 25
      },
      {
        id: '3',
        name: 'Interpretation & Meaning',
        description: 'Interprets artistic intent, symbolism, and cultural context.',
        weight: 25
      },
      {
        id: '4',
        name: 'Critical Evaluation',
        description: 'Provides thoughtful critique and personal response to artwork.',
        weight: 20
      }
    ],
    'creative-portfolio': [
      {
        id: '1',
        name: 'Creativity & Originality',
        description: 'Demonstrates innovative thinking and unique artistic vision.',
        weight: 30
      },
      {
        id: '2',
        name: 'Technical Skill',
        description: 'Shows mastery of artistic techniques and media handling.',
        weight: 25
      },
      {
        id: '3',
        name: 'Artistic Development',
        description: 'Evidence of growth, experimentation, and artistic progression.',
        weight: 25
      },
      {
        id: '4',
        name: 'Presentation & Reflection',
        description: 'Professional presentation and thoughtful artist statements.',
        weight: 20
      }
    ],
    'literary-analysis': [
      {
        id: '1',
        name: 'Textual Analysis',
        description: 'Demonstrates deep understanding of literary text and its components.',
        weight: 25
      },
      {
        id: '2',
        name: 'Literary Devices',
        description: 'Identifies and analyzes use of literary techniques and devices.',
        weight: 25
      },
      {
        id: '3',
        name: 'Theme & Meaning',
        description: 'Interprets themes, symbolism, and deeper meanings in literature.',
        weight: 25
      },
      {
        id: '4',
        name: 'Critical Thinking',
        description: 'Provides insightful analysis and supports arguments with textual evidence.',
        weight: 25
      }
    ],
    'comparative-literature': [
      {
        id: '1',
        name: 'Comparative Analysis',
        description: 'Effectively compares and contrasts multiple literary works.',
        weight: 30
      },
      {
        id: '2',
        name: 'Thematic Connections',
        description: 'Identifies and analyzes common themes across different works.',
        weight: 25
      },
      {
        id: '3',
        name: 'Cultural Context',
        description: 'Understands and compares cultural and historical contexts.',
        weight: 25
      },
      {
        id: '4',
        name: 'Synthesis & Evaluation',
        description: 'Synthesizes information and evaluates literary merit across works.',
        weight: 20
      }
    ],
    'problem-solving': [
      {
        id: '1',
        name: 'Mathematical Reasoning',
        description: 'Demonstrates logical thinking and mathematical reasoning in problem-solving approaches.',
        weight: 30
      },
      {
        id: '2',
        name: 'Problem Strategy',
        description: 'Selects and applies appropriate mathematical strategies and methods.',
        weight: 25
      },
      {
        id: '3',
        name: 'Calculations & Accuracy',
        description: 'Performs calculations correctly with attention to precision and detail.',
        weight: 25
      },
      {
        id: '4',
        name: 'Mathematical Communication',
        description: 'Clearly explains mathematical thinking and justifies solutions.',
        weight: 20
      }
    ],
    'mathematical-modeling': [
      {
        id: '1',
        name: 'Real-World Application',
        description: 'Effectively translates real-world problems into mathematical models.',
        weight: 30
      },
      {
        id: '2',
        name: 'Model Construction',
        description: 'Creates appropriate mathematical representations and equations.',
        weight: 25
      },
      {
        id: '3',
        name: 'Data Analysis',
        description: 'Analyzes and interprets data to support mathematical conclusions.',
        weight: 25
      },
      {
        id: '4',
        name: 'Model Validation',
        description: 'Tests and evaluates the effectiveness of mathematical models.',
        weight: 20
      }
    ],
    'lab-report': [
      {
        id: '1',
        name: 'Experimental Design',
        description: 'Demonstrates understanding of scientific method and experimental procedures.',
        weight: 25
      },
      {
        id: '2',
        name: 'Data Collection & Analysis',
        description: 'Accurately records observations and analyzes experimental data.',
        weight: 25
      },
      {
        id: '3',
        name: 'Scientific Reasoning',
        description: 'Draws logical conclusions based on evidence and scientific principles.',
        weight: 25
      },
      {
        id: '4',
        name: 'Lab Report Quality',
        description: 'Presents findings clearly with proper scientific writing and formatting.',
        weight: 25
      }
    ],
    'scientific-inquiry': [
      {
        id: '1',
        name: 'Hypothesis Formation',
        description: 'Develops testable hypotheses based on scientific knowledge and observations.',
        weight: 25
      },
      {
        id: '2',
        name: 'Investigation Skills',
        description: 'Designs and conducts systematic scientific investigations.',
        weight: 25
      },
      {
        id: '3',
        name: 'Evidence Evaluation',
        description: 'Critically evaluates scientific evidence and identifies patterns.',
        weight: 25
      },
      {
        id: '4',
        name: 'Scientific Communication',
        description: 'Communicates scientific findings using appropriate terminology and formats.',
        weight: 25
      }
    ],
    'historical-analysis': [
      {
        id: '1',
        name: 'Source Analysis',
        description: 'Critically analyzes primary and secondary historical sources for reliability and bias.',
        weight: 30
      },
      {
        id: '2',
        name: 'Historical Context',
        description: 'Demonstrates understanding of historical periods, events, and their significance.',
        weight: 25
      },
      {
        id: '3',
        name: 'Cause & Effect',
        description: 'Identifies and analyzes historical causes, consequences, and connections.',
        weight: 25
      },
      {
        id: '4',
        name: 'Historical Argumentation',
        description: 'Constructs well-supported historical arguments using evidence.',
        weight: 20
      }
    ],
    'historical-research': [
      {
        id: '1',
        name: 'Research Methodology',
        description: 'Applies appropriate historical research methods and techniques.',
        weight: 25
      },
      {
        id: '2',
        name: 'Source Evaluation',
        description: 'Evaluates credibility, perspective, and limitations of historical sources.',
        weight: 25
      },
      {
        id: '3',
        name: 'Evidence Synthesis',
        description: 'Synthesizes multiple sources to construct historical narratives.',
        weight: 25
      },
      {
        id: '4',
        name: 'Historical Interpretation',
        description: 'Develops and supports original historical interpretations.',
        weight: 25
      }
    ],
    'programming-project': [
      {
        id: '1',
        name: 'Code Quality & Structure',
        description: 'Writes clean, well-organized, and maintainable code with proper structure.',
        weight: 30
      },
      {
        id: '2',
        name: 'Problem Solving',
        description: 'Demonstrates computational thinking and effective problem-solving approaches.',
        weight: 25
      },
      {
        id: '3',
        name: 'Functionality & Testing',
        description: 'Creates working solutions that meet requirements and handles edge cases.',
        weight: 25
      },
      {
        id: '4',
        name: 'Documentation & Comments',
        description: 'Provides clear documentation and meaningful code comments.',
        weight: 20
      }
    ],
    'algorithm-design': [
      {
        id: '1',
        name: 'Algorithmic Thinking',
        description: 'Demonstrates logical step-by-step approach to problem decomposition.',
        weight: 30
      },
      {
        id: '2',
        name: 'Efficiency & Optimization',
        description: 'Considers time and space complexity in algorithm design.',
        weight: 25
      },
      {
        id: '3',
        name: 'Algorithm Implementation',
        description: 'Successfully translates algorithmic concepts into working code.',
        weight: 25
      },
      {
        id: '4',
        name: 'Analysis & Evaluation',
        description: 'Analyzes algorithm performance and evaluates alternative approaches.',
        weight: 20
      }
    ]
  };

  // Define the criteria type
  type CriteriaItem = {
    id: string;
    name: string;
    description: string;
    weight: number;
  };

  const [criteria, setCriteria] = useState<CriteriaItem[]>([]);

  // Initialize template from URL params or localStorage
  useEffect(() => {
    const urlTemplate = searchParams.get('template');
    const storedTemplate = localStorage.getItem('selectedTemplate');

    const templateToUse = urlTemplate || storedTemplate || 'historical-analysis';

    if (templateCriteria[templateToUse as keyof typeof templateCriteria]) {
      setSelectedTemplate(templateToUse);
    } else {
      setSelectedTemplate('historical-analysis');
    }
  }, [searchParams]);

  // Update criteria when template changes
  useEffect(() => {
    if (selectedTemplate && templateCriteria[selectedTemplate as keyof typeof templateCriteria]) {
      const newCriteria = templateCriteria[selectedTemplate as keyof typeof templateCriteria];
      setCriteria(newCriteria);

      // Save to localStorage for persistence
      localStorage.setItem('selectedTemplate', selectedTemplate);
    }
  }, [selectedTemplate]);

  // Temporarily disable auth check for testing
  // useEffect(() => {
  //   if (!isLoading && !isAuthenticated) {
  //     router.push('/auth/sign-in');
  //   }
  // }, [isAuthenticated, isLoading, router]);

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Loading...</div>
        </div>
      </DashboardLayout>
    );
  }

  // Temporarily allow access without authentication for testing
  // if (!isAuthenticated) {
  //   return null;
  // }

  return (
    <DashboardLayout>
      <div className="max-w-6xl mx-auto p-6 space-y-8">
        {/* Header */}
        <div>
          <div className="flex items-center space-x-3 mb-2">
            <h1 className="text-3xl font-semibold text-text">
              Assessment Configuration & Rubric
            </h1>
            {isBulkMode && (
              <span className="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium">
                🚀 Bulk Mode
              </span>
            )}
          </div>
          <p className="text-gray-600">
            {isBulkMode
              ? `Configure assessment settings for ${submissionsData?.student_submissions?.length || 0} submissions`
              : 'Configure assessment settings and select a rubric'
            }
          </p>
        </div>

        {/* Progress Indicator */}
        <ProgressIndicator currentStep={2} />



        {/* Assessment Template Section */}
        <TemplateSelector
          selectedTemplate={selectedTemplate}
          onTemplateSelect={(templateId) => {
            setSelectedTemplate(templateId);
          }}
        />

        {/* Assessment Criteria Section */}
        {selectedTemplate && criteria.length > 0 ? (
          <CriteriaBuilder
            criteria={criteria}
            onCriteriaChange={setCriteria}
          />
        ) : (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
            <p className="text-gray-600">Please select a template to configure criteria</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between pt-6">
          <button
            onClick={() => router.push('/student-upload')}
            className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors flex items-center space-x-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            <span>Back to Submission</span>
          </button>

          {/* Fork: Show different options based on submission count and mode */}
          {submissionsData?.student_submissions?.length > 1 || isBulkMode ? (
            // Multiple submissions - show both options
            <div className="flex space-x-3">
              <button
                onClick={() => {
                  // Save configuration to localStorage for individual review
                  const configData = {
                    selectedTemplate,
                    criteria,
                    timestamp: new Date().toISOString()
                  };
                  localStorage.setItem('assessmentConfig', JSON.stringify(configData));
                  router.push(`/assessment/review?assignment=${assignmentId}`);
                }}
                className="px-6 py-2 border border-accent text-accent rounded-md hover:bg-accent hover:text-white transition-colors flex items-center space-x-2"
                disabled={!selectedTemplate || criteria.length === 0}
              >
                <span>Review Individual</span>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4" />
                </svg>
              </button>
              <button
                onClick={() => {
                  // Save configuration to localStorage for bulk review
                  const configData = {
                    selectedTemplate,
                    criteria,
                    timestamp: new Date().toISOString(),
                    bulkMode: true
                  };
                  localStorage.setItem('assessmentConfig', JSON.stringify(configData));
                  router.push('/assessment/bulk-review');
                }}
                className="px-6 py-2 bg-accent text-white rounded-md hover:bg-[#9A7649] transition-colors flex items-center space-x-2"
                disabled={!selectedTemplate || criteria.length === 0}
              >
                <span>Review Bulk ({submissionsData?.student_submissions?.length} files)</span>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          ) : (
            // Single submission - show regular review button
            <button
              onClick={() => {
                // Save configuration to localStorage for review page
                const configData = {
                  selectedTemplate,
                  criteria,
                  timestamp: new Date().toISOString()
                };
                localStorage.setItem('assessmentConfig', JSON.stringify(configData));
                router.push(`/assessment/review?assignment=${assignmentId}`);
              }}
              className="px-6 py-2 bg-accent text-white rounded-md hover:bg-[#9A7649] transition-colors flex items-center space-x-2"
              disabled={!selectedTemplate || criteria.length === 0}
            >
              <span>Continue to Review</span>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
