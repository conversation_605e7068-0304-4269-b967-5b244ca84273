import React from 'react';
import { format, formatDistanceToNow } from 'date-fns';
import { RecentActivity } from '@/lib/analytics/types';
import Link from 'next/link';

interface RecentActivityFeedProps {
  activities: RecentActivity[];
  loading?: boolean;
}

export default function RecentActivityFeed({ activities, loading = false }: RecentActivityFeedProps) {
  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-32 mb-4"></div>
          {[1, 2, 3, 4, 5].map((index) => (
            <div key={index} className="flex items-center space-x-4 py-3 border-b border-gray-100 last:border-b-0">
              <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
              <div className="w-16 h-6 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!activities || activities.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium mb-4">Recent Activity</h3>
        <div className="flex items-center justify-center h-32 text-gray-500">
          <div className="text-center">
            <span className="text-3xl mb-2 block">📋</span>
            <p>No recent activity</p>
          </div>
        </div>
      </div>
    );
  }

  const getActivityIcon = (type: string, status?: string): string => {
    switch (type) {
      case 'assessment':
        return '🧠';
      case 'submission':
        return status === 'pending' ? '⏳' : '📤';
      case 'assignment':
        return '📝';
      default:
        return '📋';
    }
  };

  const getStatusColor = (status: string): string => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getGradeColor = (grade?: string): string => {
    if (!grade) return 'bg-gray-100 text-gray-800';
    
    switch (grade.toUpperCase()) {
      case 'A':
        return 'bg-green-100 text-green-800';
      case 'B':
        return 'bg-blue-100 text-blue-800';
      case 'C':
        return 'bg-yellow-100 text-yellow-800';
      case 'D':
        return 'bg-orange-100 text-orange-800';
      case 'F':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium">Recent Activity</h3>
        <span className="text-sm text-gray-500">
          Last {activities.length} activities
        </span>
      </div>
      
      <div className="space-y-4">
        {activities.map((activity) => (
          <div key={activity.id} className="flex items-start space-x-4 p-3 hover:bg-gray-50 rounded-lg transition-colors">
            {/* Activity Icon */}
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                <span className="text-lg">{getActivityIcon(activity.type, activity.status)}</span>
              </div>
            </div>
            
            {/* Activity Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {activity.title}
                </p>
                <div className="flex items-center space-x-2">
                  {activity.grade && (
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getGradeColor(activity.grade)}`}>
                      {activity.grade}
                    </span>
                  )}
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(activity.status)}`}>
                    {activity.status}
                  </span>
                </div>
              </div>
              
              <div className="mt-1 flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  {activity.studentName && (
                    <span className="font-medium">{activity.studentName}</span>
                  )}
                  {activity.studentName && activity.assignmentTitle && ' • '}
                  {activity.assignmentTitle && (
                    <span>{activity.assignmentTitle}</span>
                  )}
                </div>
                
                <div className="text-xs text-gray-500">
                  <span title={format(activity.timestamp, 'PPpp')}>
                    {formatDistanceToNow(activity.timestamp, { addSuffix: true })}
                  </span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {activities.length >= 10 && (
        <div className="mt-4 pt-4 border-t">
          <Link 
            href="/analytics/activity" 
            className="text-sm text-accent hover:text-accent-dark font-medium"
          >
            View all activity →
          </Link>
        </div>
      )}
    </div>
  );
}
