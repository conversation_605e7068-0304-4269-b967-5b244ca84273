'use client';

import { useEffect } from 'react';
import { useAuthenticationStatus } from '@nhost/nextjs';
import { useRouter } from 'next/navigation';
import { useQuery, gql } from '@apollo/client';
import DashboardLayout from '@/components/layout/DashboardLayout';
import Link from 'next/link';
import { formatDate } from '@/lib/utils';

const GET_USER_SUBMISSIONS = gql`
  query GetUserSubmissions {
    student_submissions(
      order_by: { upload_date: desc }
    ) {
      id
      processing_status
      upload_date
      student_name
      assignment {
        id
        title
      }
    }
  }
`;

export default function SubmissionsPage() {
  const { isAuthenticated, isLoading } = useAuthenticationStatus();
  const router = useRouter();

  const { data, loading: submissionsLoading } = useQuery(GET_USER_SUBMISSIONS, {
    skip: !isAuthenticated,
  });

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/sign-in');
    }
  }, [isLoading, isAuthenticated, router]);

  if (isLoading || submissionsLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[50vh]">
          <p>Loading...</p>
        </div>
      </DashboardLayout>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect in the useEffect
  }

  const submissions = data?.student_submissions || [];

  // Function to get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-200 text-gray-800';
      case 'submitted':
        return 'bg-blue-100 text-blue-800';
      case 'grading':
        return 'bg-yellow-100 text-yellow-800';
      case 'graded':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <DashboardLayout>
      <div className="mb-8">
        <h1 className="text-3xl font-light mb-2">My Submissions</h1>
        <p className="text-gray-600">
          View all your assignment submissions
        </p>
      </div>

      {submissions.length === 0 ? (
        <div className="bg-white rounded-lg shadow p-6 text-center">
          <h2 className="text-xl font-medium mb-2">No submissions yet</h2>
          <p className="text-gray-600 mb-4">
            You haven't submitted any assignments yet.
          </p>
          <Link
            href="/assignments"
            className="text-accent hover:text-accent/80 font-medium"
          >
            Browse assignments
          </Link>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Assignment
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Student
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Uploaded
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="relative px-6 py-3">
                  <span className="sr-only">View</span>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {submissions.map((submission: {
                id: string;
                processing_status: string;
                upload_date: string;
                student_name: string;
                assignment: {
                  id: string;
                  title: string;
                }
              }) => (
                <tr key={submission.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {submission.assignment.title}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">
                      {submission.student_name || 'N/A'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">
                      {submission.upload_date ? formatDate(submission.upload_date) : 'Not uploaded'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(submission.processing_status)}`}>
                      {submission.processing_status.charAt(0).toUpperCase() + submission.processing_status.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <Link
                      href={`/submissions/${submission.id}`}
                      className="text-accent hover:text-accent/80"
                    >
                      View
                    </Link>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </DashboardLayout>
  );
}
