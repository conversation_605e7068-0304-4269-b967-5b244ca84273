import { useState } from 'react';
import { useResetPassword } from '@nhost/nextjs';
import Link from 'next/link';
import Button from '@/components/ui/Button';

export function ResetPassword() {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);

  const { resetPassword, isLoading, isError, error } = useResetPassword();

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const result = await resetPassword(email);
    if (!result.error) {
      setIsSubmitted(true);
    }
  };

  if (isSubmitted) {
    return (
      <div className="w-full text-center">
        <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
          <p className="text-green-800">
            If an account exists with this email, we've sent instructions to reset your password.
          </p>
        </div>
        <Link href="/auth/sign-in" className="text-accent hover:text-accent/80">
          Return to sign in
        </Link>
      </div>
    );
  }

  return (
    <div className="w-full">
      <form className="space-y-6" onSubmit={handleSubmit}>
        <div>
          <label htmlFor="email" className="block text-sm font-medium mb-2">
            Email address
          </label>
          <input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="block w-full rounded-md border border-gray-300 py-2 px-3 bg-white shadow-sm focus:ring-2 focus:ring-accent focus:border-accent"
          />
        </div>

        {isError && (
          <div className="text-red-500 text-sm">
            {error?.message || 'Error resetting password'}
          </div>
        )}

        <div className="pt-2">
          <Button
            type="submit"
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? 'Sending...' : 'Reset Password'}
          </Button>
        </div>

        <div className="text-center text-sm mt-6">
          Remember your password?{' '}
          <Link href="/auth/sign-in" className="text-accent hover:text-accent/80">
            Sign in
          </Link>
        </div>
      </form>
    </div>
  );
}
