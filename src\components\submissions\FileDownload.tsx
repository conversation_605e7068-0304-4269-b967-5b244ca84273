import { useState, useEffect } from 'react';
import { nhost } from '@/lib/nhost';

interface FileDownloadProps {
  fileId: string;
  fileName: string;
  fileType: string;
}

export default function FileDownload({ fileId, fileName, fileType }: FileDownloadProps) {
  const [downloadUrl, setDownloadUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const getDownloadUrl = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await nhost.storage.getPresignedUrl({
          fileId,
        });

        if (response.error) {
          throw new Error(response.error.message);
        }

        setDownloadUrl(response.presignedUrl.url);
      } catch (err) {
        console.error('Error getting download URL:', err);
        setError(err instanceof Error ? err.message : 'Failed to get download URL');
      } finally {
        setIsLoading(false);
      }
    };

    getDownloadUrl();
  }, [fileId]);

  // Get file icon based on file type
  const getFileIcon = () => {
    switch (fileType) {
      case 'image':
        return (
          <svg className="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        );
      case 'document':
        return (
          <svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
          </svg>
        );
      case 'code':
        return (
          <svg className="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
          </svg>
        );
      default:
        return (
          <svg className="w-6 h-6 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        );
    }
  };

  return (
    <div className="flex items-center justify-between py-3 px-4 hover:bg-gray-50 border-b border-gray-200 last:border-b-0">
      <div className="flex items-center">
        {getFileIcon()}
        <span className="ml-3 text-gray-700">{fileName}</span>
      </div>

      {isLoading ? (
        <span className="text-sm text-gray-400">Loading...</span>
      ) : error ? (
        <span className="text-sm text-red-500" title={error}>Error</span>
      ) : (
        <a
          href={downloadUrl || '#'}
          download={fileName}
          className="text-sm font-medium text-accent hover:text-accent/80"
          target="_blank"
          rel="noopener noreferrer"
        >
          Download
        </a>
      )}
    </div>
  );
}
