'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useAuthenticationStatus, useUserData, useSignOut } from '@nhost/nextjs';
import { useRouter } from 'next/navigation';
import Button from '../ui/Button';

export default function Header() {
  const { isAuthenticated, isLoading } = useAuthenticationStatus();
  const user = useUserData();
  const { signOut } = useSignOut();
  const router = useRouter();

  const handleSignOut = async () => {
    await signOut();
    router.push('/');
  };

  return (
    <header className="py-4 px-6 md:px-12 flex justify-between items-center">
      <div className="flex items-center">
        <Link href="/" className="inline-block">
          <Image
            src="/Rubriq_logo_final.png"
            alt="Rubriq"
            width={150}
            height={45}
            className="h-12 w-auto"
          />
        </Link>
      </div>
      
      <nav className="hidden md:flex items-center space-x-8">
        <Link href="/features" className="hover:text-accent transition-colors">
          Features
        </Link>
        <Link href="/pricing" className="hover:text-accent transition-colors">
          Pricing
        </Link>
        {isAuthenticated && (
          <Link href="/dashboard" className="hover:text-accent transition-colors">
            Dashboard
          </Link>
        )}
      </nav>
      
      <div className="flex items-center space-x-4">
        {isLoading ? (
          <div className="w-20 h-10 bg-lightGray animate-pulse rounded-full"></div>
        ) : isAuthenticated ? (
          <div className="flex items-center space-x-4">
            <span className="hidden md:inline text-sm">
              {user?.displayName || user?.email}
            </span>
            <Button 
              variant="outline" 
              onClick={handleSignOut}
              className="py-2 px-4"
            >
              Sign Out
            </Button>
          </div>
        ) : (
          <Link href="/auth/sign-in">
            <Button variant="primary" className="py-2 px-6">
              Login
            </Button>
          </Link>
        )}
      </div>
    </header>
  );
}
