'use client';

import { useEffect } from 'react';
import { useAuthenticationStatus, useUserData } from '@nhost/nextjs';
import { useRouter } from 'next/navigation';
import { useQuery, gql } from '@apollo/client';
import DashboardLayout from '@/components/layout/DashboardLayout';
import Button from '@/components/ui/Button';
import Link from 'next/link';
import AssignmentCard from '@/components/assignments/AssignmentCard';

// GraphQL query to fetch user's assignments - using exact same structure as CREATE_ASSIGNMENT
const GET_USER_ASSIGNMENTS = gql`
  query GetUserAssignments($user_id: uuid!) {
    assignments(where: { user_id: { _eq: $user_id } }) {
      id
      title
      description
      status
      created_at
    }
  }
`;

export default function AssignmentsPage() {
  const { isAuthenticated, isLoading } = useAuthenticationStatus();
  const user = useUserData();
  const router = useRouter();

  // Fetch user's assignments
  const { data, loading: assignmentsLoading, error } = useQuery(GET_USER_ASSIGNMENTS, {
    variables: { user_id: user?.id },
    skip: !isAuthenticated || !user?.id,
  });



  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/sign-in');
    }
  }, [isLoading, isAuthenticated, router]);

  if (isLoading || assignmentsLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[50vh]">
          <p>Loading...</p>
        </div>
      </DashboardLayout>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect in the useEffect
  }

  const assignments = data?.assignments || [];

  return (
    <DashboardLayout>


      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-light mb-2">Assignments</h1>
          <p className="text-gray-600">
            Manage your assignments and view submissions.
          </p>
        </div>
        <Link href="/assignments/manage">
          <Button variant="primary">Manage Assignments</Button>
        </Link>
      </div>

      {assignments.length === 0 ? (
        <div className="bg-white rounded-lg shadow p-12 text-center">
          <h2 className="text-2xl font-light mb-4">No Assignments Yet</h2>
          <p className="mb-6">You haven't created any assignments yet. Get started by creating your first assignment.</p>
          <Link href="/assignments/manage">
            <Button variant="primary">Create Your First Assignment</Button>
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {assignments.map((assignment: any) => (
            <AssignmentCard
              key={assignment.id}
              id={assignment.id}
              title={assignment.title}
              description={assignment.description}
              status={assignment.status}
              createdAt={assignment.created_at}
            />
          ))}
        </div>
      )}
    </DashboardLayout>
  );
}
