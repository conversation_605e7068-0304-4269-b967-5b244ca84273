import Link from 'next/link';

interface AssignmentCardProps {
  id: string;
  title: string;
  description: string;
  status: string;
  createdAt?: string;
}



export default function AssignmentCard({
  id,
  title,
  description,
  status,
  createdAt,
}: AssignmentCardProps) {
  // Helper function to get status color
  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'published':
        return 'bg-green-100 text-green-800';
      case 'archived':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Link href={`/assignments/${id}`} className="block">
      <div className="bg-white rounded-lg shadow p-6 h-full hover:shadow-lg transition-shadow">
        <div className="mb-4">
          <h3 className="text-xl font-medium">{title}</h3>
        </div>





        <p className="text-gray-700 line-clamp-3 mb-4">
          {description}
        </p>

        <div className="flex justify-between items-center">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(status)}`}>
            {status || 'Draft'}
          </span>
        </div>
      </div>
    </Link>
  );
}
