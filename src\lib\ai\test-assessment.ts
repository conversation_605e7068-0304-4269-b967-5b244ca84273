/**
 * Test script for AI Assessment System
 * This can be used to test the AI assessment functionality
 */

import { AIService } from './AIService';
import { AssessmentConfig, Submission } from '@/types/ai-assessment';

// Test data
const mockSubmission: Submission = {
  id: 'test-submission-1',
  studentName: '<PERSON>',
  fileName: 'lab-report.pdf',
  fileType: 'pdf',
  content: `
Lab Report: Chemical Reactions and Energy Changes

Introduction:
This experiment investigates the relationship between chemical reactions and energy changes. We hypothesize that exothermic reactions will release heat energy, while endothermic reactions will absorb heat energy from the surroundings.

Materials:
- Sodium hydroxide (NaOH)
- Hydrochloric acid (HCl)
- Thermometer
- Beakers
- Measuring cylinders

Method:
1. Measured 50ml of 1M HCl into a beaker
2. Recorded initial temperature
3. Added 2g of NaOH slowly while stirring
4. Recorded temperature changes every 30 seconds for 5 minutes
5. Repeated experiment 3 times for reliability

Results:
Initial temperature: 22°C
Maximum temperature reached: 35°C
Temperature change: +13°C

The reaction was clearly exothermic as evidenced by the significant temperature increase.

Conclusion:
The hypothesis was supported. The reaction between NaOH and HCl is exothermic, releasing 13°C worth of heat energy. This demonstrates the principle of energy conservation in chemical reactions.

Areas for improvement:
- Could have used more precise measuring equipment
- Should have controlled room temperature better
- More trials would improve reliability
`,
  uploadDate: new Date(),
  assignment: {
    id: 'test-assignment-1',
    title: 'Chemistry Lab Report: Chemical Reactions',
    description: 'Investigate and report on chemical reactions and energy changes in laboratory experiments.'
  },
  criteria: [
    {
      id: 'experimental-design',
      name: 'Experimental Design',
      description: 'Quality of experimental methodology and procedure',
      weight: 25
    },
    {
      id: 'data-analysis',
      name: 'Data Analysis',
      description: 'Accuracy and interpretation of results',
      weight: 25
    },
    {
      id: 'scientific-reasoning',
      name: 'Scientific Reasoning',
      description: 'Use of scientific principles and logical thinking',
      weight: 25
    },
    {
      id: 'presentation',
      name: 'Presentation',
      description: 'Clarity of writing and organization',
      weight: 25
    }
  ]
};

const mockConfig: AssessmentConfig = {
  submissionId: 'test-submission-1',
  aiModel: 'standard', // Test with OpenAI first
  priority: 'standard',
  feedbackLevel: 'detailed',
  assessmentType: 'rubric',
  criteria: [
    {
      id: 'experimental-design',
      name: 'Experimental Design',
      description: 'Quality of experimental methodology and procedure',
      weight: 25
    },
    {
      id: 'data-analysis',
      name: 'Data Analysis',
      description: 'Accuracy and interpretation of results',
      weight: 25
    },
    {
      id: 'scientific-reasoning',
      name: 'Scientific Reasoning',
      description: 'Use of scientific principles and logical thinking',
      weight: 25
    },
    {
      id: 'presentation',
      name: 'Presentation',
      description: 'Clarity of writing and organization',
      weight: 25
    }
  ],
  features: {
    plagiarismCheck: false, // Disable for testing
    grammarCheck: true,
    contentAnalysis: true,
    citationCheck: false,
    codeAnalysis: false
  }
};

/**
 * Test the AI assessment system
 */
export async function testAIAssessment(): Promise<void> {
  console.log('🧪 Starting AI Assessment Test');
  console.log('================================');

  try {
    // Validate configuration
    console.log('📋 Validating configuration...');
    AIService.validateConfig(mockConfig);
    console.log('✅ Configuration valid');

    // Estimate processing time
    const estimatedTime = AIService.estimateProcessingTime(mockConfig);
    console.log(`⏱️ Estimated processing time: ${estimatedTime} seconds`);

    // Create AI service
    console.log('🤖 Initializing AI service...');
    const aiService = new AIService();

    // Process assessment
    console.log('🚀 Starting assessment...');
    const startTime = Date.now();

    const result = await aiService.assessSubmission(mockSubmission, mockConfig);

    const actualTime = (Date.now() - startTime) / 1000;
    console.log(`✅ Assessment completed in ${actualTime.toFixed(1)} seconds`);

    // Display results
    console.log('\n📊 ASSESSMENT RESULTS');
    console.log('=====================');
    console.log(`Overall Grade: ${result.overallGrade}`);
    console.log(`Overall Score: ${result.overallScore}/100`);
    console.log(`AI Provider: ${result.aiProvider}`);
    console.log(`Model Used: ${result.modelUsed}`);
    console.log(`Confidence: ${(result.confidenceScore * 100).toFixed(0)}%`);
    console.log(`Processing Time: ${result.processingTimeMs}ms`);

    console.log('\n💰 TOKEN USAGE');
    console.log('===============');
    console.log(`Input Tokens: ${result.tokenUsage.inputTokens}`);
    console.log(`Output Tokens: ${result.tokenUsage.outputTokens}`);
    console.log(`Total Tokens: ${result.tokenUsage.totalTokens}`);
    console.log(`Estimated Cost: $${result.tokenUsage.estimatedCost.toFixed(4)}`);

    console.log('\n📝 FEEDBACK SUMMARY');
    console.log('===================');
    console.log(result.feedbackSummary);

    console.log('\n💪 STRENGTHS');
    console.log('============');
    result.strengths.forEach((strength, index) => {
      console.log(`${index + 1}. ${strength}`);
    });

    console.log('\n🎯 AREAS FOR IMPROVEMENT');
    console.log('========================');
    result.areasForImprovement.forEach((area, index) => {
      console.log(`${index + 1}. ${area}`);
    });

    console.log('\n💡 SUGGESTIONS');
    console.log('==============');
    result.suggestions.forEach((suggestion, index) => {
      console.log(`${index + 1}. ${suggestion}`);
    });

    console.log('\n📋 DETAILED CRITERIA FEEDBACK');
    console.log('==============================');
    result.detailedFeedback.forEach((feedback) => {
      console.log(`\n${feedback.criteriaName}: ${feedback.score}/${feedback.maxScore}`);
      console.log(`Feedback: ${feedback.feedback}`);
      if (feedback.strengths.length > 0) {
        console.log(`Strengths: ${feedback.strengths.join(', ')}`);
      }
      if (feedback.improvements.length > 0) {
        console.log(`Improvements: ${feedback.improvements.join(', ')}`);
      }
    });

    console.log('\n🎉 Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}

/**
 * Test both AI providers
 */
export async function testBothProviders(): Promise<void> {
  console.log('🔄 Testing both AI providers...\n');

  // Test OpenAI (Standard)
  console.log('🟢 Testing OpenAI (Standard Model)');
  console.log('===================================');
  await testAIAssessment();

  console.log('\n\n');

  // Test Anthropic (Comprehensive)
  console.log('🟣 Testing Anthropic (Comprehensive Model)');
  console.log('==========================================');
  const anthropicConfig = { ...mockConfig, aiModel: 'comprehensive' as const };

  try {
    const aiService = new AIService();
    const result = await aiService.assessSubmission(mockSubmission, anthropicConfig);

    console.log(`✅ Anthropic assessment completed`);
    console.log(`Overall Grade: ${result.overallGrade}`);
    console.log(`Overall Score: ${result.overallScore}/100`);
    console.log(`Confidence: ${(result.confidenceScore * 100).toFixed(0)}%`);
    console.log(`Cost: $${result.tokenUsage.estimatedCost.toFixed(4)}`);

  } catch (error) {
    console.error('❌ Anthropic test failed:', error);
  }

  console.log('\n🏁 All tests completed!');
}

// Export for use in browser console or Node.js
if (typeof window !== 'undefined') {
  // Browser environment
  (window as any).testAIAssessment = testAIAssessment;
  (window as any).testBothProviders = testBothProviders;
  console.log('🌐 AI Assessment test functions available in browser console:');
  console.log('- testAIAssessment()');
  console.log('- testBothProviders()');
}
