# Cycle-Based Filtering Implementation Guide

## Database Schema Design

### 1. Core Tables Structure

```sql
-- Assessment cycles table
CREATE TABLE assessment_cycles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL, -- e.g., "Fall 2024", "Spring 2025"
  code VARCHAR(50) UNIQUE NOT NULL, -- e.g., "2024-FALL", "2025-SPRING"
  start_date TIMESTAMP NOT NULL,
  end_date TIMESTAMP NOT NULL,
  is_active BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  -- Ensure only one active cycle
  CONSTRAINT only_one_active_cycle EXCLUDE (is_active WITH =) WHERE (is_active = true)
);

-- Modified submissions table
CREATE TABLE submissions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  cycle_id UUID NOT NULL REFERENCES assessment_cycles(id),
  student_id UUID NOT NULL REFERENCES users(id),
  assignment_id UUID NOT NULL REFERENCES assignments(id),
  content TEXT,
  grade DECIMAL(5,2),
  feedback TEXT,
  status VARCHAR(50) DEFAULT 'pending', -- pending, graded, returned
  submitted_at TIMESTAMP DEFAULT NOW(),
  graded_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  -- Prevent duplicate submissions per student per assignment per cycle
  CONSTRAINT unique_submission_per_cycle UNIQUE (cycle_id, student_id, assignment_id)
);

-- Assignments table (also cycle-aware)
CREATE TABLE assignments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  cycle_id UUID NOT NULL REFERENCES assessment_cycles(id),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  due_date TIMESTAMP,
  max_grade DECIMAL(5,2) DEFAULT 100,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_submissions_cycle_id ON submissions(cycle_id);
CREATE INDEX idx_submissions_student_cycle ON submissions(student_id, cycle_id);
CREATE INDEX idx_assignments_cycle_id ON assignments(cycle_id);
```

## Nhost Configuration

### 2. GraphQL Permissions and Relationships

```yaml
# hasura/metadata/databases/default/tables/public_submissions.yaml
table:
  name: submissions
  schema: public

object_relationships:
  - name: cycle
    using:
      foreign_key_constraint_on: cycle_id
  - name: student
    using:
      foreign_key_constraint_on: student_id
  - name: assignment
    using:
      foreign_key_constraint_on: assignment_id

select_permissions:
  - role: student
    permission:
      columns: '*'
      filter:
        _and:
          - student_id: { _eq: X-Hasura-User-Id }
          - cycle:
              is_active: { _eq: true }
      
  - role: teacher
    permission:
      columns: '*'
      filter:
        cycle:
          is_active: { _eq: true }
          
  - role: admin
    permission:
      columns: '*'
      filter: {}
```

### 3. Custom Hasura Actions

```javascript
// nhost/functions/cycle-management.js
export default async (req, res) => {
  const { action, cycleData } = req.body.input;
  
  switch (action) {
    case 'START_NEW_CYCLE':
      // Deactivate current cycle
      await nhost.graphql.request(`
        mutation DeactivateCycles {
          update_assessment_cycles(
            where: { is_active: { _eq: true } },
            _set: { is_active: false }
          ) {
            affected_rows
          }
        }
      `);
      
      // Create and activate new cycle
      const newCycle = await nhost.graphql.request(`
        mutation CreateCycle($cycle: assessment_cycles_insert_input!) {
          insert_assessment_cycles_one(object: $cycle) {
            id
            code
            name
          }
        }
      `, {
        cycle: {
          ...cycleData,
          is_active: true
        }
      });
      
      return res.json({ cycle: newCycle.insert_assessment_cycles_one });
      
    case 'ARCHIVE_CYCLE':
      // Implementation for archiving
      break;
  }
};
```

## Next.js Implementation

### 4. Context Provider for Cycle Management

```typescript
// contexts/CycleContext.tsx
import { createContext, useContext, useState, useEffect } from 'react';
import { useQuery, useMutation } from '@apollo/client';
import { GET_ACTIVE_CYCLE, START_NEW_CYCLE } from '@/graphql/queries';

interface Cycle {
  id: string;
  name: string;
  code: string;
  startDate: Date;
  endDate: Date;
  isActive: boolean;
}

interface CycleContextType {
  currentCycle: Cycle | null;
  loading: boolean;
  startNewCycle: (cycleData: Partial<Cycle>) => Promise<void>;
  refreshCycle: () => void;
}

const CycleContext = createContext<CycleContextType | null>(null);

export const CycleProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { data, loading, refetch } = useQuery(GET_ACTIVE_CYCLE);
  const [startCycleMutation] = useMutation(START_NEW_CYCLE);
  
  const currentCycle = data?.assessment_cycles?.[0] || null;
  
  const startNewCycle = async (cycleData: Partial<Cycle>) => {
    try {
      await startCycleMutation({
        variables: { cycleData },
        refetchQueries: [{ query: GET_ACTIVE_CYCLE }]
      });
    } catch (error) {
      console.error('Failed to start new cycle:', error);
      throw error;
    }
  };
  
  return (
    <CycleContext.Provider 
      value={{ 
        currentCycle, 
        loading, 
        startNewCycle,
        refreshCycle: refetch 
      }}
    >
      {children}
    </CycleContext.Provider>
  );
};

export const useCycle = () => {
  const context = useContext(CycleContext);
  if (!context) {
    throw new Error('useCycle must be used within CycleProvider');
  }
  return context;
};
```

### 5. GraphQL Queries

```typescript
// graphql/queries.ts
import { gql } from '@apollo/client';

export const GET_ACTIVE_CYCLE = gql`
  query GetActiveCycle {
    assessment_cycles(where: { is_active: { _eq: true } }) {
      id
      name
      code
      start_date
      end_date
      is_active
    }
  }
`;

export const GET_CYCLE_SUBMISSIONS = gql`
  query GetCycleSubmissions($cycleId: uuid!) {
    submissions(
      where: { cycle_id: { _eq: $cycleId } }
      order_by: { submitted_at: desc }
    ) {
      id
      content
      grade
      feedback
      status
      submitted_at
      graded_at
      student {
        id
        display_name
        email
      }
      assignment {
        id
        title
        max_grade
      }
    }
  }
`;

export const GET_STUDENT_SUBMISSIONS = gql`
  query GetStudentSubmissions($cycleId: uuid!, $studentId: uuid!) {
    submissions(
      where: {
        _and: [
          { cycle_id: { _eq: $cycleId } },
          { student_id: { _eq: $studentId } }
        ]
      }
    ) {
      id
      content
      grade
      feedback
      status
      assignment {
        title
        due_date
      }
    }
  }
`;
```

### 6. Submissions Page Component

```tsx
// pages/submissions.tsx
import { useQuery } from '@apollo/client';
import { useCycle } from '@/contexts/CycleContext';
import { GET_CYCLE_SUBMISSIONS } from '@/graphql/queries';
import { SubmissionCard } from '@/components/SubmissionCard';
import { CycleSelector } from '@/components/CycleSelector';
import { useState } from 'react';

export default function SubmissionsPage() {
  const { currentCycle } = useCycle();
  const [viewMode, setViewMode] = useState<'current' | 'historical'>('current');
  const [selectedCycleId, setSelectedCycleId] = useState(currentCycle?.id);
  
  const { data, loading, error } = useQuery(GET_CYCLE_SUBMISSIONS, {
    variables: { cycleId: selectedCycleId },
    skip: !selectedCycleId
  });
  
  if (!currentCycle && viewMode === 'current') {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">No Active Assessment Cycle</h1>
        <p>Please contact an administrator to start a new assessment cycle.</p>
      </div>
    );
  }
  
  return (
    <div className="p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Submissions</h1>
        
        <div className="flex gap-4 items-center">
          <div className="flex gap-2">
            <button
              onClick={() => {
                setViewMode('current');
                setSelectedCycleId(currentCycle?.id);
              }}
              className={`px-4 py-2 rounded ${
                viewMode === 'current' 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-gray-200'
              }`}
            >
              Current Cycle
            </button>
            <button
              onClick={() => setViewMode('historical')}
              className={`px-4 py-2 rounded ${
                viewMode === 'historical' 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-gray-200'
              }`}
            >
              Historical
            </button>
          </div>
          
          {viewMode === 'historical' && (
            <CycleSelector 
              onSelect={setSelectedCycleId}
              currentValue={selectedCycleId}
            />
          )}
        </div>
      </div>
      
      {viewMode === 'current' && currentCycle && (
        <div className="bg-blue-50 p-4 rounded mb-6">
          <p className="text-sm text-blue-800">
            Current Cycle: <strong>{currentCycle.name}</strong> 
            ({new Date(currentCycle.startDate).toLocaleDateString()} - 
            {new Date(currentCycle.endDate).toLocaleDateString()})
          </p>
        </div>
      )}
      
      {loading && <p>Loading submissions...</p>}
      {error && <p>Error loading submissions: {error.message}</p>}
      
      <div className="grid gap-4">
        {data?.submissions?.map((submission) => (
          <SubmissionCard key={submission.id} submission={submission} />
        ))}
        
        {data?.submissions?.length === 0 && (
          <p className="text-gray-500 text-center py-8">
            No submissions found for this cycle.
          </p>
        )}
      </div>
    </div>
  );
}
```

### 7. Admin Cycle Management Component

```tsx
// components/admin/CycleManagement.tsx
import { useState } from 'react';
import { useCycle } from '@/contexts/CycleContext';
import { useQuery } from '@apollo/client';
import { GET_ALL_CYCLES } from '@/graphql/queries';

export function CycleManagement() {
  const { currentCycle, startNewCycle } = useCycle();
  const { data: cyclesData } = useQuery(GET_ALL_CYCLES);
  const [showNewCycleForm, setShowNewCycleForm] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    startDate: '',
    endDate: ''
  });
  
  const handleStartNewCycle = async () => {
    if (!window.confirm('This will close the current cycle and start a new one. Continue?')) {
      return;
    }
    
    try {
      await startNewCycle({
        name: formData.name,
        code: formData.code,
        start_date: formData.startDate,
        end_date: formData.endDate
      });
      
      setShowNewCycleForm(false);
      setFormData({ name: '', code: '', startDate: '', endDate: '' });
    } catch (error) {
      alert('Failed to start new cycle. Please try again.');
    }
  };
  
  return (
    <div className="p-6 bg-white rounded-lg shadow">
      <h2 className="text-xl font-bold mb-4">Assessment Cycle Management</h2>
      
      {currentCycle && (
        <div className="mb-6 p-4 bg-green-50 rounded">
          <h3 className="font-semibold text-green-800">Current Active Cycle</h3>
          <p className="text-green-700">
            {currentCycle.name} ({currentCycle.code})
          </p>
          <p className="text-sm text-green-600">
            {new Date(currentCycle.start_date).toLocaleDateString()} - 
            {new Date(currentCycle.end_date).toLocaleDateString()}
          </p>
        </div>
      )}
      
      {!showNewCycleForm ? (
        <button
          onClick={() => setShowNewCycleForm(true)}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Start New Cycle
        </button>
      ) : (
        <form className="space-y-4" onSubmit={(e) => {
          e.preventDefault();
          handleStartNewCycle();
        }}>
          <div>
            <label className="block text-sm font-medium mb-1">Cycle Name</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="e.g., Fall 2024"
              className="w-full p-2 border rounded"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Cycle Code</label>
            <input
              type="text"
              value={formData.code}
              onChange={(e) => setFormData({ ...formData, code: e.target.value })}
              placeholder="e.g., 2024-FALL"
              className="w-full p-2 border rounded"
              required
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Start Date</label>
              <input
                type="date"
                value={formData.startDate}
                onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                className="w-full p-2 border rounded"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">End Date</label>
              <input
                type="date"
                value={formData.endDate}
                onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                className="w-full p-2 border rounded"
                required
              />
            </div>
          </div>
          
          <div className="flex gap-2">
            <button
              type="submit"
              className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
            >
              Start New Cycle
            </button>
            <button
              type="button"
              onClick={() => setShowNewCycleForm(false)}
              className="bg-gray-300 px-4 py-2 rounded hover:bg-gray-400"
            >
              Cancel
            </button>
          </div>
        </form>
      )}
      
      <div className="mt-8">
        <h3 className="font-semibold mb-3">Historical Cycles</h3>
        <div className="space-y-2">
          {cyclesData?.assessment_cycles?.map((cycle) => (
            <div 
              key={cycle.id} 
              className={`p-3 border rounded ${
                cycle.is_active ? 'border-green-500 bg-green-50' : 'border-gray-300'
              }`}
            >
              <div className="flex justify-between items-center">
                <div>
                  <span className="font-medium">{cycle.name}</span>
                  <span className="text-sm text-gray-500 ml-2">({cycle.code})</span>
                  {cycle.is_active && (
                    <span className="ml-2 text-xs bg-green-500 text-white px-2 py-1 rounded">
                      Active
                    </span>
                  )}
                </div>
                <div className="text-sm text-gray-600">
                  {new Date(cycle.start_date).toLocaleDateString()} - 
                  {new Date(cycle.end_date).toLocaleDateString()}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
```

## Best Practices and Considerations

### 8. Automated Cycle Transitions

```typescript
// utils/cycleAutomation.ts
export async function checkAndTransitionCycles() {
  const { data } = await nhost.graphql.request(`
    query GetEndedCycles {
      assessment_cycles(
        where: {
          _and: [
            { is_active: { _eq: true } },
            { end_date: { _lte: "now()" } }
          ]
        }
      ) {
        id
        name
      }
    }
  `);
  
  if (data.assessment_cycles.length > 0) {
    // Send notifications to admins
    await notifyAdmins({
      message: `Cycle "${data.assessment_cycles[0].name}" has ended. Please start a new cycle.`,
      type: 'CYCLE_ENDED'
    });
    
    // Optionally auto-deactivate
    if (process.env.AUTO_CLOSE_CYCLES === 'true') {
      await nhost.graphql.request(`
        mutation DeactivateEndedCycle($id: uuid!) {
          update_assessment_cycles_by_pk(
            pk_columns: { id: $id },
            _set: { is_active: false }
          ) {
            id
          }
        }
      `, { id: data.assessment_cycles[0].id });
    }
  }
}
```

### 9. Data Migration for Existing Systems

```sql
-- If migrating from a system without cycles
-- 1. Create a default cycle for existing data
INSERT INTO assessment_cycles (name, code, start_date, end_date, is_active)
VALUES ('Legacy Data', 'LEGACY', '2020-01-01', '2024-12-31', false);

-- 2. Update existing submissions with the legacy cycle ID
UPDATE submissions 
SET cycle_id = (SELECT id FROM assessment_cycles WHERE code = 'LEGACY')
WHERE cycle_id IS NULL;

-- 3. Add NOT NULL constraint after migration
ALTER TABLE submissions ALTER COLUMN cycle_id SET NOT NULL;
```

### 10. Performance Optimizations

```typescript
// hooks/useSubmissions.ts
import { useQuery } from '@apollo/client';
import { useCycle } from '@/contexts/CycleContext';

export function useSubmissions(options = {}) {
  const { currentCycle } = useCycle();
  
  return useQuery(GET_CYCLE_SUBMISSIONS, {
    variables: { cycleId: currentCycle?.id },
    skip: !currentCycle?.id,
    // Cache submissions per cycle
    fetchPolicy: 'cache-and-network',
    nextFetchPolicy: 'cache-first',
    ...options
  });
}

// Implement pagination for large datasets
export function usePaginatedSubmissions(page = 1, limit = 20) {
  const { currentCycle } = useCycle();
  const offset = (page - 1) * limit;
  
  return useQuery(GET_PAGINATED_SUBMISSIONS, {
    variables: { 
      cycleId: currentCycle?.id,
      limit,
      offset
    }
  });
}
```

### 11. Export and Reporting Features

```typescript
// utils/cycleExports.ts
export async function exportCycleData(cycleId: string) {
  const { data } = await nhost.graphql.request(`
    query ExportCycleData($cycleId: uuid!) {
      assessment_cycles_by_pk(id: $cycleId) {
        name
        code
        submissions {
          student {
            display_name
            email
          }
          assignment {
            title
          }
          grade
          submitted_at
          graded_at
        }
      }
    }
  `, { cycleId });
  
  // Convert to CSV or Excel format
  const csv = convertToCSV(data.assessment_cycles_by_pk);
  downloadFile(csv, `${data.assessment_cycles_by_pk.code}_export.csv`);
}
```

## Edge Cases and Error Handling

### 12. Handling Edge Cases

```typescript
// Handle students joining mid-cycle
export function canStudentSubmit(studentId: string, cycleId: string) {
  // Check if student enrollment date is within cycle period
  return nhost.graphql.request(`
    query CheckStudentEligibility($studentId: uuid!, $cycleId: uuid!) {
      users_by_pk(id: $studentId) {
        created_at
      }
      assessment_cycles_by_pk(id: $cycleId) {
        start_date
        end_date
      }
    }
  `, { studentId, cycleId });
}

// Handle cycle overlaps (backup validation)
export async function validateCycleDates(startDate: Date, endDate: Date) {
  const { data } = await nhost.graphql.request(`
    query CheckOverlap($start: timestamp!, $end: timestamp!) {
      assessment_cycles(
        where: {
          _or: [
            { _and: [
              { start_date: { _lte: $start } },
              { end_date: { _gte: $start } }
            ]},
            { _and: [
              { start_date: { _lte: $end } },
              { end_date: { _gte: $end } }
            ]}
          ]
        }
      ) {
        id
        name
      }
    }
  `, { start: startDate, end: endDate });
  
  return data.assessment_cycles.length === 0;
}
```

This implementation provides a robust, scalable solution that maintains data integrity while providing a clean slate for each assessment cycle.