# Submission Filtering Infrastructure

## Overview

This document describes the centralized submission filtering system implemented to prevent confusion from old test data and maintain a clean user experience across the application.

## Problem Statement

Previously, users would see old test submissions (e.g., from 6/2/2025) mixed with current assignment submissions, causing confusion during bulk assessment workflows. This infrastructure provides a consistent, application-wide solution.

## Architecture

### Core Components

1. **`src/lib/submission-filters.ts`** - Core filtering logic and utilities
2. **`src/lib/gql/submission-queries.ts`** - Centralized GraphQL queries
3. **`src/hooks/useFilteredSubmissions.ts`** - React hook for components
4. **Filter Presets** - Predefined configurations for different contexts

### Key Features

- **Automatic Old Data Filtering**: Submissions before today are filtered out by default
- **Assignment-Specific Filtering**: Show only submissions for the current assignment
- **Status Filtering**: Filter by processing status (pending, completed, etc.)
- **Consistent UI Feedback**: Shows users what was filtered and why
- **Flexible Configuration**: Multiple presets for different use cases

## Usage

### Basic Hook Usage

```typescript
import { useFilteredSubmissions } from '@/hooks/useFilteredSubmissions';

function MyComponent() {
  const {
    submissions,        // Filtered submissions
    filterSummary,      // User-friendly filter description
    hasFiltered,        // Boolean: were any submissions filtered?
    loading,
    error,
    refetch
  } = useFilteredSubmissions({
    assignmentId: 'some-uuid',
    preset: 'ASSIGNMENT_FOCUSED'
  });

  return (
    <div>
      <h2>Submissions ({submissions.length})</h2>
      {hasFiltered && (
        <p className="text-sm text-gray-500">{filterSummary}</p>
      )}
      {/* Render submissions */}
    </div>
  );
}
```

### Convenience Hooks

```typescript
// For assignment-specific pages
const result = useAssignmentSubmissions(assignmentId);

// For bulk assessment workflows
const result = useBulkAssessmentSubmissions(assignmentId);

// For recent activity views
const result = useRecentSubmissions();

// For admin/debugging (shows all data)
const result = useAllSubmissionsAdmin();
```

### Filter Presets

```typescript
// Available presets
FILTER_PRESETS = {
  ASSIGNMENT_FOCUSED: {
    excludeOldSubmissions: true,
    sortBy: 'upload_date',
    sortOrder: 'desc'
  },
  
  BULK_ASSESSMENT: {
    excludeOldSubmissions: true,
    statusFilter: ['pending', 'uploaded'],
    sortBy: 'student_name',
    sortOrder: 'asc'
  },
  
  RECENT_ACTIVITY: {
    excludeOldSubmissions: false,
    dateThreshold: getRecentThreshold(7), // Last 7 days
    limit: 10,
    sortBy: 'upload_date',
    sortOrder: 'desc'
  },
  
  ADMIN_VIEW: {
    excludeOldSubmissions: false, // Show everything
    sortBy: 'upload_date',
    sortOrder: 'desc'
  }
}
```

## Implementation Guidelines

### For New Components

1. **Always use the filtering infrastructure** for any component that displays submissions
2. **Choose the appropriate preset** based on your use case
3. **Show filter feedback** to users when submissions are filtered
4. **Use centralized GraphQL queries** from `submission-queries.ts`

### Example Implementation

```typescript
import { useFilteredSubmissions } from '@/hooks/useFilteredSubmissions';

export default function SubmissionList({ assignmentId }: { assignmentId?: string }) {
  const {
    submissions,
    filterSummary,
    hasFiltered,
    loading
  } = useFilteredSubmissions({
    assignmentId,
    preset: 'ASSIGNMENT_FOCUSED'
  });

  if (loading) return <div>Loading...</div>;

  return (
    <div>
      <div className="flex items-center justify-between">
        <h2>Submissions ({submissions.length})</h2>
        {hasFiltered && filterSummary && (
          <p className="text-xs text-gray-500">{filterSummary}</p>
        )}
      </div>
      
      {submissions.map(submission => (
        <SubmissionCard key={submission.id} submission={submission} />
      ))}
    </div>
  );
}
```

## Filter Behavior

### Default Filtering (excludeOldSubmissions: true)

- **Threshold**: 10 minutes ago from current time
- **Logic**: `submissionDate >= (now - 10 minutes)`
- **Purpose**: Hide old test data while preserving recent submissions from today

### Assignment Filtering

- **Logic**: `submission.assignment_id === assignmentId`
- **Purpose**: Show only submissions for the current assignment

### Status Filtering

- **Options**: `['pending', 'uploaded', 'processing', 'completed', 'failed']`
- **Purpose**: Filter by processing status

## User Experience

### Filter Feedback

When submissions are filtered, users see helpful messages like:

- "🗂️ Filtered out 2 old submissions (older than 10 minutes)"
- "🗂️ Filtered out 5 submissions from other assignments"
- "🗂️ Filtered out 3 submissions with different status"

### Visual Indicators

- Filter summaries appear as small, unobtrusive text
- Consistent styling across all components
- Clear indication of what was filtered and why

## Migration Guide

### Updating Existing Components

1. **Replace direct GraphQL queries** with `useFilteredSubmissions`
2. **Remove manual filtering logic** 
3. **Add filter feedback UI**
4. **Choose appropriate preset**

### Before (Old Pattern)

```typescript
const { data, loading } = useQuery(GET_SUBMISSIONS, {
  variables: { assignment_id: assignmentId }
});

const submissions = data?.student_submissions || [];
const filteredSubmissions = submissions.filter(sub => {
  // Manual filtering logic
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return new Date(sub.upload_date) >= today;
});
```

### After (New Pattern)

```typescript
const { submissions, filterSummary, hasFiltered } = useFilteredSubmissions({
  assignmentId,
  preset: 'ASSIGNMENT_FOCUSED'
});
```

## Testing

### Unit Tests

- Test filtering logic with various date scenarios
- Test preset configurations
- Test filter summary generation

### Integration Tests

- Test hook behavior with real GraphQL data
- Test component rendering with filtered data
- Test user feedback display

## Configuration

### Environment-Specific Behavior

- **Development**: May show more lenient filtering for testing
- **Production**: Strict filtering to maintain clean UX
- **Admin Mode**: Option to disable filtering for debugging

### Customization

```typescript
// Custom filter options
const { submissions } = useFilteredSubmissions({
  assignmentId,
  excludeOldSubmissions: false,  // Show all dates
  dateThreshold: customDate,     // Custom threshold
  statusFilter: ['completed'],   // Only completed submissions
  limit: 50,                     // Limit results
  sortBy: 'student_name',        // Custom sorting
  sortOrder: 'asc'
});
```

## Best Practices

1. **Always provide user feedback** when filtering occurs
2. **Use appropriate presets** rather than custom configurations when possible
3. **Test with realistic data** including old submissions
4. **Consider performance** with large datasets
5. **Document any custom filtering logic** in component comments

## Troubleshooting

### Common Issues

1. **No submissions showing**: Check if all submissions are being filtered out
2. **Wrong submissions showing**: Verify assignment ID and filter configuration
3. **Performance issues**: Consider adding pagination or limiting results

### Debug Mode

```typescript
// Enable debug logging
const { submissions, filterBreakdown } = useFilteredSubmissions({
  assignmentId,
  preset: 'ASSIGNMENT_FOCUSED'
});

console.log('Filter breakdown:', filterBreakdown);
// Output: { oldSubmissions: 2, wrongAssignment: 0, statusFiltered: 1 }
```

## Future Enhancements

- **Date range filtering**: Allow custom date ranges
- **Advanced search**: Text-based filtering
- **Saved filters**: User-defined filter presets
- **Bulk operations**: Apply actions to filtered results
- **Export functionality**: Export filtered data
