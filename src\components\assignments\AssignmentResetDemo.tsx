/**
 * Demo component to test Assignment Reset functionality
 * This can be used for testing the reset feature
 */

import { useState } from 'react';
import AssignmentResetButton from './AssignmentResetButton';

export default function AssignmentResetDemo() {
  const [submissionCount, setSubmissionCount] = useState(5);
  const [lastResetCount, setLastResetCount] = useState<number | null>(null);

  const mockAssignmentId = "demo-assignment-123";
  const mockAssignmentTitle = "History Essay Assignment";

  const handleResetComplete = (clearedCount: number) => {
    setLastResetCount(clearedCount);
    setSubmissionCount(0); // Simulate cleared submissions
    
    // Reset count after 3 seconds for demo purposes
    setTimeout(() => {
      setSubmissionCount(5);
      setLastResetCount(null);
    }, 3000);
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Assignment Reset Demo</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        
        {/* Button Variant */}
        <div className="bg-white p-6 rounded-lg shadow border">
          <h3 className="text-lg font-semibold mb-4">Button Variant</h3>
          <div className="mb-4">
            <p className="text-sm text-gray-600 mb-2">
              Assignment: {mockAssignmentTitle}
            </p>
            <p className="text-sm text-gray-600">
              Submissions: {submissionCount}
            </p>
          </div>
          
          <AssignmentResetButton
            assignmentId={mockAssignmentId}
            assignmentTitle={mockAssignmentTitle}
            submissionCount={submissionCount}
            variant="button"
            size="md"
            onResetComplete={handleResetComplete}
          />
          
          {lastResetCount !== null && (
            <div className="mt-3 p-2 bg-green-50 border border-green-200 rounded text-sm text-green-800">
              ✅ Reset complete: {lastResetCount} submissions cleared
            </div>
          )}
        </div>

        {/* Icon Variant */}
        <div className="bg-white p-6 rounded-lg shadow border">
          <h3 className="text-lg font-semibold mb-4">Icon Variant</h3>
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="text-sm text-gray-600 mb-1">
                {mockAssignmentTitle}
              </p>
              <p className="text-xs text-gray-500">
                {submissionCount} submissions
              </p>
            </div>
            
            <AssignmentResetButton
              assignmentId={mockAssignmentId}
              assignmentTitle={mockAssignmentTitle}
              submissionCount={submissionCount}
              variant="icon"
              onResetComplete={handleResetComplete}
            />
          </div>
          
          {lastResetCount !== null && (
            <div className="mt-3 p-2 bg-green-50 border border-green-200 rounded text-sm text-green-800">
              ✅ Cleared {lastResetCount} submissions
            </div>
          )}
        </div>

        {/* Dropdown Variant */}
        <div className="bg-white p-6 rounded-lg shadow border">
          <h3 className="text-lg font-semibold mb-4">Dropdown Variant</h3>
          <div className="mb-4">
            <p className="text-sm text-gray-600 mb-2">
              Assignment: {mockAssignmentTitle}
            </p>
            <p className="text-sm text-gray-600">
              Submissions: {submissionCount}
            </p>
          </div>
          
          <div className="border border-gray-200 rounded-lg p-2">
            <AssignmentResetButton
              assignmentId={mockAssignmentId}
              assignmentTitle={mockAssignmentTitle}
              submissionCount={submissionCount}
              variant="dropdown"
              onResetComplete={handleResetComplete}
            />
          </div>
          
          {lastResetCount !== null && (
            <div className="mt-3 p-2 bg-green-50 border border-green-200 rounded text-sm text-green-800">
              ✅ Reset: {lastResetCount} submissions
            </div>
          )}
        </div>
      </div>

      {/* Usage Instructions */}
      <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-3">How to Use Assignment Reset</h3>
        <div className="space-y-2 text-sm text-blue-800">
          <p><strong>Archive:</strong> Marks submissions as archived (can be restored later)</p>
          <p><strong>Delete:</strong> Permanently removes submissions (cannot be undone)</p>
          <p><strong>Use Case:</strong> When reusing the same assignment for a new assessment cycle</p>
          <p><strong>Result:</strong> Clean slate for fresh student submissions</p>
        </div>
      </div>

      {/* Integration Example */}
      <div className="mt-6 bg-gray-50 border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">Integration Example</h3>
        <pre className="text-sm text-gray-700 bg-white p-4 rounded border overflow-x-auto">
{`<AssignmentResetButton
  assignmentId={assignmentId}
  assignmentTitle={assignmentTitle}
  submissionCount={submissions.length}
  variant="button"
  size="sm"
  onResetComplete={(clearedCount) => {
    console.log(\`Reset complete: \${clearedCount} cleared\`);
    refreshSubmissions(); // Refresh your submissions list
  }}
/>`}
        </pre>
      </div>
    </div>
  );
}
