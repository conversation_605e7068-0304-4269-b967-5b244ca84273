<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="400" height="300" fill="#F5F2EA"/>
  
  <!-- Document representation -->
  <rect x="50" y="60" width="120" height="160" rx="8" fill="#FFFFFF" stroke="#B38B5D" stroke-width="2"/>
  
  <!-- Text lines on document -->
  <line x1="70" y1="90" x2="130" y2="90" stroke="#2A2A2A" stroke-width="2" opacity="0.3"/>
  <line x1="70" y1="110" x2="140" y2="110" stroke="#2A2A2A" stroke-width="2" opacity="0.3"/>
  <line x1="70" y1="130" x2="125" y2="130" stroke="#2A2A2A" stroke-width="2" opacity="0.3"/>
  <line x1="70" y1="150" x2="135" y2="150" stroke="#2A2A2A" stroke-width="2" opacity="0.3"/>
  <line x1="70" y1="170" x2="120" y2="170" stroke="#2A2A2A" stroke-width="2" opacity="0.3"/>
  
  <!-- AI Brain/Analysis representation -->
  <circle cx="280" cy="120" r="45" fill="#A3BDB8" opacity="0.8"/>
  <circle cx="280" cy="120" r="35" fill="none" stroke="#B38B5D" stroke-width="2"/>
  <circle cx="280" cy="120" r="25" fill="none" stroke="#B38B5D" stroke-width="1" opacity="0.6"/>
  
  <!-- Neural network nodes -->
  <circle cx="265" cy="105" r="3" fill="#B38B5D"/>
  <circle cx="295" cy="105" r="3" fill="#B38B5D"/>
  <circle cx="280" cy="135" r="3" fill="#B38B5D"/>
  <circle cx="270" cy="125" r="2" fill="#B38B5D" opacity="0.7"/>
  <circle cx="290" cy="125" r="2" fill="#B38B5D" opacity="0.7"/>
  
  <!-- Connection lines -->
  <line x1="265" y1="105" x2="280" y2="135" stroke="#B38B5D" stroke-width="1" opacity="0.5"/>
  <line x1="295" y1="105" x2="280" y2="135" stroke="#B38B5D" stroke-width="1" opacity="0.5"/>
  <line x1="270" y1="125" x2="290" y2="125" stroke="#B38B5D" stroke-width="1" opacity="0.5"/>
  
  <!-- Analysis flow arrows -->
  <path d="M 180 140 Q 220 120 240 120" stroke="#B38B5D" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Arrow marker definition -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#B38B5D"/>
    </marker>
  </defs>
  
  <!-- Results/Feedback representation -->
  <rect x="320" y="180" width="60" height="80" rx="6" fill="#FFFFFF" stroke="#A3BDB8" stroke-width="2"/>
  
  <!-- Grade/Score -->
  <text x="350" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#B38B5D">A-</text>
  
  <!-- Feedback lines -->
  <line x1="330" y1="220" x2="370" y2="220" stroke="#A3BDB8" stroke-width="1"/>
  <line x1="330" y1="230" x2="365" y2="230" stroke="#A3BDB8" stroke-width="1"/>
  <line x1="330" y1="240" x2="360" y2="240" stroke="#A3BDB8" stroke-width="1"/>
  <line x1="330" y1="250" x2="370" y2="250" stroke="#A3BDB8" stroke-width="1"/>
  
  <!-- Analysis flow to results -->
  <path d="M 310 140 Q 340 160 340 170" stroke="#A3BDB8" stroke-width="2" fill="none" marker-end="url(#arrowhead2)"/>
  
  <!-- Second arrow marker -->
  <defs>
    <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#A3BDB8"/>
    </marker>
  </defs>
  
  <!-- Floating analysis indicators -->
  <circle cx="200" cy="80" r="4" fill="#B38B5D" opacity="0.6">
    <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="220" cy="100" r="3" fill="#A3BDB8" opacity="0.6">
    <animate attributeName="opacity" values="0.6;1;0.6" dur="2.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="240" cy="90" r="3" fill="#B38B5D" opacity="0.6">
    <animate attributeName="opacity" values="0.6;1;0.6" dur="1.8s" repeatCount="indefinite"/>
  </circle>
</svg>
