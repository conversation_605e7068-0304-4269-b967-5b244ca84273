// AI Assessment Types
export interface AssessmentConfig {
  submissionId: string;
  aiModel: 'standard' | 'comprehensive';
  priority: 'standard' | 'high' | 'urgent';
  feedbackLevel: 'brief' | 'standard' | 'detailed';
  assessmentType: 'letter' | 'numeric' | 'passfail' | 'holistic' | 'rubric';
  criteria: AssessmentCriteria[];
  features: AssessmentFeatures;
  // Intelligent routing metadata
  routingDecision?: {
    selectedProvider: 'openai' | 'anthropic';
    selectedModel: string;
    confidence: number;
    reasoning: string[];
    contentAnalysis: {
      complexity: 'low' | 'medium' | 'high';
      contentType: 'text' | 'code' | 'math' | 'creative' | 'analytical' | 'mixed';
      wordCount: number;
      technicalTerms: number;
      subjectArea: string;
      requiresNuancedAnalysis: boolean;
      estimatedProcessingTime: number;
    };
  };
}

export interface AssessmentCriteria {
  id: string;
  name: string;
  description: string;
  weight: number;
  maxScore?: number;
}

export interface AssessmentFeatures {
  plagiarismCheck: boolean;
  grammarCheck: boolean;
  contentAnalysis: boolean;
  citationCheck: boolean;
  codeAnalysis: boolean;
}

export interface Submission {
  id: string;
  studentName: string;
  fileName: string;
  fileType: string;
  content: string;
  uploadDate: Date;
  assignment: {
    id: string;
    title: string;
    description: string;
  };
  criteria: AssessmentCriteria[];
  assessment_results?: AssessmentResult; // Optional - only present after assessment
}

export interface AssessmentResult {
  id: string;
  submissionId: string;
  assignmentId: string;
  overallGrade: string;
  overallScore: number;
  feedbackSummary: string;
  detailedFeedback: CriteriaFeedback[];
  strengths: string[];
  areasForImprovement: string[];
  suggestions: string[];
  confidenceScore: number;
  processingTimeMs: number;
  tokenUsage: TokenUsage;
  aiProvider: 'openai' | 'anthropic';
  modelUsed: string;
  createdAt: Date;
  // Intelligent routing information
  routingInfo?: {
    selectedProvider: 'openai' | 'anthropic';
    selectedModel: string;
    confidence: number;
    contentAnalysis: {
      complexity: 'low' | 'medium' | 'high';
      contentType: 'text' | 'code' | 'math' | 'creative' | 'analytical' | 'mixed';
      wordCount: number;
      technicalTerms: number;
      subjectArea: string;
      requiresNuancedAnalysis: boolean;
      estimatedProcessingTime: number;
    };
  };
}

export interface CriteriaFeedback {
  criteriaId: string;
  criteriaName: string;
  score: number;
  maxScore: number;
  feedback: string;
  strengths: string[];
  improvements: string[];
}

export interface TokenUsage {
  inputTokens: number;
  outputTokens: number;
  totalTokens: number;
  estimatedCost: number;
}

export interface ProcessingStatus {
  id: string;
  submissionId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'retrying';
  aiProvider: 'openai' | 'anthropic';
  modelUsed: string;
  configuration: AssessmentConfig;
  startedAt: Date;
  completedAt?: Date;
  errorMessage?: string;
  retryCount: number;
  estimatedCompletion: Date;
  progress?: number; // 0-100
}

export interface AIProviderResponse {
  success: boolean;
  result?: AssessmentResult;
  error?: string;
  tokenUsage?: TokenUsage;
}

// Prompt Templates
export interface PromptTemplate {
  id: string;
  name: string;
  subject: string;
  systemPrompt: string;
  userPromptTemplate: string;
  gradingScale: string;
  maxTokens: number;
  temperature: number;
}

// Cost Tracking
export interface UsageRecord {
  id: string;
  provider: 'openai' | 'anthropic';
  model: string;
  inputTokens: number;
  outputTokens: number;
  estimatedCost: number;
  submissionId: string;
  timestamp: Date;
}

// Error Types
export class AIAssessmentError extends Error {
  constructor(
    message: string,
    public provider: 'openai' | 'anthropic',
    public statusCode?: number,
    public retryable: boolean = false
  ) {
    super(message);
    this.name = 'AIAssessmentError';
  }
}

export class RateLimitError extends AIAssessmentError {
  constructor(provider: 'openai' | 'anthropic', retryAfter?: number) {
    super(`Rate limit exceeded for ${provider}`, provider, 429, true);
    this.retryAfter = retryAfter;
  }

  retryAfter?: number;
}

export class QuotaExceededError extends AIAssessmentError {
  constructor(provider: 'openai' | 'anthropic') {
    super(`Quota exceeded for ${provider}`, provider, 429, false);
  }
}

export class InvalidResponseError extends AIAssessmentError {
  constructor(provider: 'openai' | 'anthropic', response: any) {
    super(`Invalid response format from ${provider}`, provider, 422, true);
    this.response = response;
  }

  response: any;
}
