'use client';

import { useState, useEffect } from 'react';
import { useAuthenticationStatus, useUserData } from '@nhost/nextjs';
import { useRouter, useSearchParams } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import ProgressIndicator from '@/components/assessment/ProgressIndicator';
import BasicInformation from '@/components/assessment/BasicInformation';
import AssessmentCriteria from '@/components/assessment/AssessmentCriteria';
import SubmissionsSection from '@/components/assessment/SubmissionsSection';
import ConfigurationSection from '@/components/assessment/ConfigurationSection';
import ProcessingTimeEstimate from '@/components/assessment/ProcessingTimeEstimate';
import SubmitActions from '@/components/assessment/SubmitActions';
import { AssessmentAPI } from '@/lib/ai/AssessmentAPI';
import { AssessmentConfig } from '@/types/ai-assessment';

// Remove GraphQL queries - we'll use API routes instead to bypass allowlist restrictions

export default function ReviewSubmitPage() {
  const { isAuthenticated, isLoading } = useAuthenticationStatus();
  const user = useUserData();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get assignment ID from URL parameters
  const assignmentId = searchParams.get('assignment');



  // State for submissions data (using API route instead of GraphQL to bypass allowlist)
  const [submissionsData, setSubmissionsData] = useState<any>(null);
  const [submissionsLoading, setSubmissionsLoading] = useState(false);

  // Function to fetch submissions using API route (bypasses allowlist)
  const fetchSubmissions = async () => {
    if (!assignmentId) {
      setSubmissionsData({ student_submissions: [] });
      return;
    }

    setSubmissionsLoading(true);
    try {
      const response = await fetch(`/api/submissions?assignment_id=${assignmentId}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Transform API response to match expected structure
      const transformedData = {
        student_submissions: data.submissions || []
      };

      setSubmissionsData(transformedData);
    } catch (error) {
      console.error('❌ Failed to load submissions via API:', error);
      setSubmissionsData({ student_submissions: [] });
    } finally {
      setSubmissionsLoading(false);
    }
  };

  // Fetch submissions when assignment ID changes or component mounts
  useEffect(() => {
    if (isAuthenticated && assignmentId) {
      fetchSubmissions();
    }
  }, [assignmentId, isAuthenticated]);

  // Define types for assessment data
  type CriteriaData = {
    templateId: string;
    templateName: string;
    criteria: any[];
    weight: number;
  } | null;

  type SubmissionData = {
    id: string;
    fileName: string;
    fileSize: number;
    fileType: string;
    uploadedAt: Date;
  };

  // Assessment data state - will be loaded from localStorage
  const [assessmentData, setAssessmentData] = useState<{
    title: string;
    description: string;
    criteria: CriteriaData;
    submissions: SubmissionData[];
    configuration: {
      aiModel: 'standard' | 'comprehensive';
      priority: 'standard' | 'high' | 'urgent';
      feedbackLevel: 'standard' | 'detailed' | 'brief';
      assessmentType: 'letter' | 'numeric' | 'passfail' | 'holistic' | 'rubric';
      feedbackTemplate: string;
      features: {
        plagiarismCheck: boolean;
        grammarCheck: boolean;
        contentAnalysis: boolean;
        citationCheck: boolean;
        codeAnalysis: boolean;
      };
    };
  }>({
    title: '',
    description: '',
    criteria: null,
    submissions: [],
    configuration: {
      aiModel: 'standard' as const,
      priority: 'standard' as const,
      feedbackLevel: 'standard' as const,
      assessmentType: 'rubric' as const,
      feedbackTemplate: '',
      features: {
        plagiarismCheck: true,
        grammarCheck: true,
        contentAnalysis: true,
        citationCheck: false,
        codeAnalysis: false
      }
    }
  });

  // Load real data from previous steps
  useEffect(() => {
    // Load configuration from configuration page
    const configData = localStorage.getItem('assessmentConfig');

    // Load submission data (you might need to implement this)
    const submissionData = localStorage.getItem('currentSubmission');

    if (configData) {
      const config = JSON.parse(configData);

      setAssessmentData(prev => ({
        ...prev,
        title: 'American Civil War Assessment', // You can make this dynamic
        description: 'Historical analysis assessment',
        criteria: {
          templateId: config.selectedTemplate,
          templateName: getTemplateName(config.selectedTemplate),
          criteria: config.criteria,
          weight: 100
        },
        configuration: {
          ...prev.configuration,
          feedbackTemplate: getTemplateName(config.selectedTemplate)
        }
      }));
    }

    // Use real submission data from database - handle multiple submissions
    if (submissionsData?.student_submissions?.length > 0) {
      const realSubmissions = submissionsData.student_submissions;

      setAssessmentData(prev => ({
        ...prev,
        submissions: realSubmissions.map((submission: any) => ({
          id: submission.id,
          fileName: `${submission.student_name || 'Unknown Student'} - ${submission.file_path || 'Unknown File'}`,
          fileSize: 156, // KB - could be calculated from file metadata
          fileType: submission.file_type || 'pdf',
          uploadedAt: new Date(submission.upload_date || new Date())
        }))
      }));
    } else {
      // Clear submissions if none found
      setAssessmentData(prev => ({
        ...prev,
        submissions: []
      }));
    }
  }, [submissionsData]);

  // Helper function to get template display name
  const getTemplateName = (templateId: string) => {
    const templateNames: { [key: string]: string } = {
      'historical-analysis': 'Historical Analysis',
      'historical-research': 'Historical Research',
      'essay-analysis': 'Essay Analysis',
      'lab-report': 'Lab Report',
      'problem-solving': 'Problem Solving',
      // Add more as needed
    };
    return templateNames[templateId] || templateId;
  };

  const [estimatedTime, setEstimatedTime] = useState(4.5); // minutes
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Temporarily disable auth check for testing
  // useEffect(() => {
  //   if (!isLoading && !isAuthenticated) {
  //     router.push('/auth/sign-in');
  //   }
  // }, [isAuthenticated, isLoading, router]);

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Loading...</div>
        </div>
      </DashboardLayout>
    );
  }

  // Temporarily allow access without authentication for testing
  // if (!isAuthenticated) {
  //   return null;
  // }

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {

      // Check if this is a bulk assessment (multiple submissions)
      if (assessmentData.submissions.length > 1) {

        // Use bulk assessment API
        const bulkConfig = {
          assignmentId: assignmentId,
          submissionIds: assessmentData.submissions.map(sub => sub.id),
          aiModel: assessmentData.configuration.aiModel,
          priority: assessmentData.configuration.priority,
          feedbackLevel: assessmentData.configuration.feedbackLevel,
          assessmentType: assessmentData.configuration.assessmentType,
          criteria: assessmentData.criteria?.criteria || [],
          features: assessmentData.configuration.features
        };



        // Submit to bulk assessment API
        const response = await fetch('/api/bulk-assess', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(bulkConfig),
        });

        const result = await response.json();

        if (result.success) {
          // Store results in localStorage for bulk results page
          if (result.results && result.results.successfulAssessments) {
            localStorage.setItem('bulkAssessmentResults', JSON.stringify(result.results.successfulAssessments));
          }

          alert(`✅ Bulk assessment completed for ${assessmentData.submissions.length} submissions!`);

          // Redirect to bulk results page
          router.push(`/assessment/bulk-results`);
        } else {
          throw new Error(result.error || 'Bulk assessment submission failed');
        }

      } else {
        // Single submission assessment

        // Check if we have a valid submission
        if (!assessmentData.submissions[0]?.id) {
          throw new Error('No valid submission found. Please upload a file first.');
        }

        const config: AssessmentConfig = {
          submissionId: assessmentData.submissions[0].id,
          aiModel: assessmentData.configuration.aiModel,
          priority: assessmentData.configuration.priority,
          feedbackLevel: assessmentData.configuration.feedbackLevel,
          assessmentType: assessmentData.configuration.assessmentType,
          criteria: assessmentData.criteria?.criteria || [],
          features: assessmentData.configuration.features
        };



        // Validate configuration
        const validation = AssessmentAPI.validateConfig(config);
        if (!validation.valid) {
          throw new Error(`Configuration validation failed: ${validation.errors.join(', ')}`);
        }

        // Submit to AI assessment API
        const assessmentAPI = new AssessmentAPI();
        const result = await assessmentAPI.submitAssessment(
          config.submissionId,
          config
        );

        if (result.success) {

          // Redirect to results page with submission ID
          const submissionId = config.submissionId;
          router.push(`/assessment/results?submissionId=${submissionId}`);
        } else {
          throw new Error(result.error || 'Assessment submission failed');
        }
      }

    } catch (error: any) {
      console.error('❌ Error submitting assessment:', error);
      alert(`Failed to submit assessment: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSaveDraft = async () => {
    try {
      // TODO: Implement save draft logic
      alert('Draft saved successfully!');
    } catch (error) {
      console.error('Error saving draft:', error);
      alert('Failed to save draft');
    }
  };

  return (
    <DashboardLayout>
      <div className="max-w-6xl mx-auto p-6 space-y-8">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-semibold text-text mb-2">
            Review & Submit
          </h1>
          <p className="text-gray-600">
            Review assessment details and submit for processing
          </p>
        </div>

        {/* Progress Indicator */}
        <ProgressIndicator currentStep={3} />

        {/* Basic Information */}
        <BasicInformation
          title={assessmentData.title}
          description={assessmentData.description}
          onUpdate={(updates) => setAssessmentData(prev => ({ ...prev, ...updates }))}
        />

        {/* Assessment Criteria */}
        <AssessmentCriteria
          criteria={assessmentData.criteria}
          onUpdate={(criteria) => setAssessmentData(prev => ({ ...prev, criteria }))}
        />

        {/* Assignment Filter - Show only if no assignment ID in URL */}
        {!assignmentId && submissionsData?.student_submissions?.length > 0 && (
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-3">
              <span className="text-amber-800 font-medium">⚠️ Multiple Assignments Detected</span>
            </div>
            <p className="text-amber-700 text-sm mb-3">
              You have submissions from multiple assignments. For bulk assessment, please select a specific assignment:
            </p>
            <div className="space-y-2">
              {Array.from(new Set(submissionsData.student_submissions.map((s: any) => s.assignment?.id)))
                .filter(Boolean)
                .map((assignmentId: any) => {
                  const assignment = submissionsData.student_submissions.find((s: any) => s.assignment?.id === assignmentId)?.assignment;
                  const submissionCount = submissionsData.student_submissions.filter((s: any) => s.assignment?.id === assignmentId).length;
                  return (
                    <button
                      key={assignmentId}
                      onClick={() => router.push(`/assessment/review?assignment=${assignmentId}`)}
                      className="block w-full text-left p-3 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="font-medium text-gray-900">{assignment?.title || 'Untitled Assignment'}</div>
                      <div className="text-sm text-gray-500">{submissionCount} submissions</div>
                    </button>
                  );
                })}
            </div>
          </div>
        )}

        {/* Submissions */}
        <SubmissionsSection
          submissions={assessmentData.submissions}
          onUpdate={(submissions) => setAssessmentData(prev => ({ ...prev, submissions }))}
        />

        {/* Configuration */}
        <ConfigurationSection
          configuration={assessmentData.configuration}
          onUpdate={(configuration) => setAssessmentData(prev => ({ ...prev, configuration }))}
          onEstimatedTimeChange={setEstimatedTime}
        />

        {/* Processing Time Estimate */}
        <ProcessingTimeEstimate estimatedTime={estimatedTime} />

        {/* Submit Actions */}
        <SubmitActions
          onSubmit={handleSubmit}
          onSaveDraft={handleSaveDraft}
          onBack={() => router.push('/assessment/configuration')}
          isSubmitting={isSubmitting}
        />
      </div>
    </DashboardLayout>
  );
}
