import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { ProcessingEfficiency } from '@/lib/analytics/types';

interface ProcessingEfficiencyChartProps {
  data: ProcessingEfficiency[];
  loading?: boolean;
}

export default function ProcessingEfficiencyChart({ data, loading = false }: ProcessingEfficiencyChartProps) {
  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="flex items-center justify-between mb-4">
            <div className="h-6 bg-gray-200 rounded w-48"></div>
            <div className="h-4 bg-gray-200 rounded w-24"></div>
          </div>
          {/* Fixed height chart skeleton to prevent layout shift */}
          <div className="h-64 bg-gray-200 rounded mb-4"></div>
          {/* Summary stats skeleton */}
          <div className="border-t pt-4">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="h-8 bg-gray-200 rounded w-12 mx-auto mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-20 mx-auto"></div>
              </div>
              <div>
                <div className="h-8 bg-gray-200 rounded w-8 mx-auto mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-16 mx-auto"></div>
              </div>
              <div>
                <div className="h-8 bg-gray-200 rounded w-10 mx-auto mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-20 mx-auto"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium mb-4">Processing Efficiency</h3>
        <div className="flex items-center justify-center h-64 text-gray-500">
          <div className="text-center">
            <span className="text-4xl mb-2 block">⚡</span>
            <p>No processing data available</p>
          </div>
        </div>
      </div>
    );
  }

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium mb-2">{label}</p>
          <p className="text-sm text-blue-600">
            Avg Processing: {data.averageProcessingTime}m
          </p>
          <p className="text-sm text-green-600">
            Submissions: {data.submissionCount}
          </p>
          <p className="text-sm text-amber-600">
            Completion: {data.completionRate}%
          </p>
        </div>
      );
    }
    return null;
  };

  // Truncate long assignment titles for display
  const chartData = data.map(item => ({
    ...item,
    displayTitle: item.assignmentTitle.length > 20 
      ? item.assignmentTitle.substring(0, 20) + '...'
      : item.assignmentTitle
  }));

  const averageProcessingTime = data.reduce((sum, item) => sum + item.averageProcessingTime, 0) / data.length;
  const totalSubmissions = data.reduce((sum, item) => sum + item.submissionCount, 0);
  const averageCompletionRate = data.reduce((sum, item) => sum + item.completionRate, 0) / data.length;

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium">Processing Efficiency</h3>
        <span className="text-sm text-gray-500">
          {data.length} assignments
        </span>
      </div>
      
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="displayTitle" 
              stroke="#6b7280"
              fontSize={12}
              tickLine={false}
              angle={-45}
              textAnchor="end"
              height={80}
            />
            <YAxis 
              stroke="#6b7280"
              fontSize={12}
              tickLine={false}
              label={{ value: 'Minutes', angle: -90, position: 'insideLeft' }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Bar 
              dataKey="averageProcessingTime" 
              fill="#3b82f6"
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* Summary stats */}
      <div className="mt-4 border-t pt-4">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-2xl font-light text-blue-600">
              {Math.round(averageProcessingTime)}m
            </div>
            <div className="text-sm text-gray-600">Avg Processing Time</div>
          </div>
          <div>
            <div className="text-2xl font-light text-green-600">{totalSubmissions}</div>
            <div className="text-sm text-gray-600">Total Submissions</div>
          </div>
          <div>
            <div className="text-2xl font-light text-amber-600">
              {Math.round(averageCompletionRate)}%
            </div>
            <div className="text-sm text-gray-600">Avg Completion Rate</div>
          </div>
        </div>
      </div>
    </div>
  );
}
