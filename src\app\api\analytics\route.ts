import { NextRequest, NextResponse } from 'next/server';
import { getServerApolloClient } from '@/lib/apollo-client';
import { gql } from '@apollo/client';
import { withRateLimit } from '@/lib/middleware/rateLimiter';

// Simple UUID validation function
function validateUUID(uuid: string): string {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuid || typeof uuid !== 'string' || !uuidRegex.test(uuid)) {
    throw new Error('Invalid UUID format');
  }
  return uuid;
}

// GraphQL queries using admin client (bypasses allowlist)
const GET_USER_ANALYTICS_OVERVIEW_ADMIN = gql`
  query GetUserAnalyticsOverviewAdmin($userId: uuid!) {
    assignments(where: { user_id: { _eq: $userId } }) {
      id
      title
      description
      created_at
      student_submissions {
        id
        student_name
        upload_date
        processing_status
        assessment_results {
          id
          overall_grade
          created_at
        }
      }
    }
  }
`;

const GET_USER_ASSESSMENT_RESULTS_ADMIN = gql`
  query GetUserAssessmentResultsAdmin($userId: uuid!, $timeRange: timestamptz) {
    assessment_results(
      where: {
        student_submission: {
          assignment: { user_id: { _eq: $userId } }
        }
        created_at: { _gte: $timeRange }
      }
      order_by: { created_at: desc }
    ) {
      id
      overall_grade
      general_comments
      created_at
      student_submission {
        id
        student_name
        upload_date
        assignment {
          id
          title
          description
          created_at
        }
      }
    }
  }
`;

const GET_SUBMISSION_TRENDS_ADMIN = gql`
  query GetSubmissionTrendsAdmin($userId: uuid!, $timeRange: timestamptz) {
    student_submissions(
      where: {
        assignment: { user_id: { _eq: $userId } }
        upload_date: { _gte: $timeRange }
      }
      order_by: { upload_date: asc }
    ) {
      id
      upload_date
      processing_status
      assignment {
        id
        title
      }
      assessment_results {
        id
        overall_grade
        created_at
      }
    }
  }
`;

const GET_RECENT_ACTIVITY_ADMIN = gql`
  query GetRecentActivityAdmin($userId: uuid!, $limit: Int = 10) {
    assessment_results(
      where: {
        student_submission: {
          assignment: { user_id: { _eq: $userId } }
        }
      }
      order_by: { created_at: desc }
      limit: $limit
    ) {
      id
      overall_grade
      created_at
      student_submission {
        id
        student_name
        upload_date
        assignment {
          id
          title
          description
        }
      }
    }
    
    student_submissions(
      where: {
        assignment: { user_id: { _eq: $userId } }
        assessment_results: { id: { _is_null: true } }
      }
      order_by: { upload_date: desc }
      limit: $limit
    ) {
      id
      student_name
      upload_date
      processing_status
      assignment {
        id
        title
      }
    }
  }
`;

// GET - Fetch analytics data
const getAnalyticsHandler = async (request: NextRequest) => {
  try {
    const { searchParams } = new URL(request.url);
    const user_id = searchParams.get('user_id');
    const timeRange = searchParams.get('timeRange');
    const dataType = searchParams.get('type') || 'overview';

    if (!user_id) {
      return NextResponse.json(
        { error: 'Missing user_id parameter' },
        { status: 400 }
      );
    }

    const validatedUserId = validateUUID(user_id);

    console.log('📊 Fetching analytics data:', {
      user_id: validatedUserId,
      timeRange,
      dataType
    });

    // Use server-side Apollo client with admin secret
    const apolloClient = getServerApolloClient();
    
    let result;
    
    switch (dataType) {
      case 'overview':
        result = await apolloClient.query({
          query: GET_USER_ANALYTICS_OVERVIEW_ADMIN,
          variables: { userId: validatedUserId },
        });
        break;
        
      case 'assessments':
        result = await apolloClient.query({
          query: GET_USER_ASSESSMENT_RESULTS_ADMIN,
          variables: { 
            userId: validatedUserId,
            timeRange: timeRange || new Date('2020-01-01').toISOString()
          },
        });
        break;
        
      case 'trends':
        result = await apolloClient.query({
          query: GET_SUBMISSION_TRENDS_ADMIN,
          variables: { 
            userId: validatedUserId,
            timeRange: timeRange || new Date('2020-01-01').toISOString()
          },
        });
        break;
        
      case 'activity':
        result = await apolloClient.query({
          query: GET_RECENT_ACTIVITY_ADMIN,
          variables: { 
            userId: validatedUserId,
            limit: 10
          },
        });
        break;
        
      default:
        return NextResponse.json(
          { error: 'Invalid data type' },
          { status: 400 }
        );
    }

    console.log('✅ Analytics data fetched successfully:', {
      dataType,
      dataKeys: Object.keys(result.data)
    });

    return NextResponse.json({
      success: true,
      data: result.data
    });

  } catch (error: any) {
    console.error('❌ Failed to fetch analytics data:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch analytics data',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
};

// Apply rate limiting
export const GET = withRateLimit(getAnalyticsHandler, 'general');

// CORS handling
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
