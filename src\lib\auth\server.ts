/**
 * Server-side Authentication Utilities
 * Provides authentication helpers for API routes and server components
 */

import { NextRequest } from 'next/server';
import { cookies } from 'next/headers';

export interface ServerUser {
  id: string;
  email: string;
  role: string;
  allowedRoles: string[];
  displayName?: string;
}

/**
 * Extract JWT token from various sources
 */
export function getServerAuthToken(request?: NextRequest): string | null {
  // Try request headers first (for API routes)
  if (request) {
    const authHeader = request.headers.get('authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }
  }

  // Try cookies (for server components)
  try {
    const cookieStore = cookies();
    const tokenCookie = cookieStore.get('nhostAccessToken');
    if (tokenCookie) {
      return tokenCookie.value;
    }
  } catch (error) {
    // Cookies might not be available in all contexts
  }

  return null;
}

/**
 * Validate and decode JWT token
 */
export function validateServerToken(token: string): ServerUser | null {
  try {
    // Basic JWT structure validation
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    // Decode payload
    const payload = JSON.parse(atob(parts[1]));

    // Check expiration
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp && payload.exp < now) {
      return null; // Token expired
    }

    // Extract Hasura claims
    const hasuraClaims = payload['https://hasura.io/jwt/claims'];
    if (!hasuraClaims) {
      return null;
    }

    const userId = hasuraClaims['x-hasura-user-id'];
    const defaultRole = hasuraClaims['x-hasura-default-role'];
    const allowedRoles = hasuraClaims['x-hasura-allowed-roles'];

    if (!userId || !defaultRole) {
      return null;
    }

    return {
      id: userId,
      email: payload.email || payload.sub || '<EMAIL>',
      role: defaultRole,
      allowedRoles: Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles],
      displayName: payload.displayName || payload.name
    };

  } catch (error) {
    console.error('Token validation error:', error);
    return null;
  }
}

/**
 * Get authenticated user from request
 */
export function getServerUser(request?: NextRequest): ServerUser | null {
  const token = getServerAuthToken(request);
  if (!token) {
    return null;
  }

  return validateServerToken(token);
}

/**
 * Check if user has required role
 */
export function hasServerRole(user: ServerUser, requiredRole: string): boolean {
  return user.allowedRoles.includes(requiredRole) || user.role === requiredRole;
}

/**
 * Check if user owns a resource
 */
export function isServerResourceOwner(user: ServerUser, resourceUserId: string): boolean {
  return user.id === resourceUserId;
}

/**
 * Require authentication for API route
 */
export function requireServerAuth(request: NextRequest): ServerUser {
  const user = getServerUser(request);
  
  if (!user) {
    throw new Error('Authentication required');
  }

  return user;
}

/**
 * Require specific role for API route
 */
export function requireServerRole(request: NextRequest, requiredRole: string): ServerUser {
  const user = requireServerAuth(request);
  
  if (!hasServerRole(user, requiredRole)) {
    throw new Error(`Role '${requiredRole}' required`);
  }

  return user;
}

/**
 * Require resource ownership for API route
 */
export function requireServerResourceOwnership(
  request: NextRequest, 
  resourceUserId: string
): ServerUser {
  const user = requireServerAuth(request);
  
  if (!isServerResourceOwner(user, resourceUserId) && !hasServerRole(user, 'admin')) {
    throw new Error('Resource access denied');
  }

  return user;
}

/**
 * Enhanced error handling for authentication
 */
export class AuthenticationError extends Error {
  constructor(
    message: string,
    public code: string = 'AUTH_ERROR',
    public statusCode: number = 401
  ) {
    super(message);
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends Error {
  constructor(
    message: string,
    public code: string = 'AUTHORIZATION_ERROR',
    public statusCode: number = 403
  ) {
    super(message);
    this.name = 'AuthorizationError';
  }
}

/**
 * Safe authentication check that doesn't throw
 */
export function checkServerAuth(request: NextRequest): {
  isAuthenticated: boolean;
  user: ServerUser | null;
  error: string | null;
} {
  try {
    const user = getServerUser(request);
    return {
      isAuthenticated: !!user,
      user,
      error: null
    };
  } catch (error) {
    return {
      isAuthenticated: false,
      user: null,
      error: error instanceof Error ? error.message : 'Authentication error'
    };
  }
}

/**
 * Create authentication context for API routes
 */
export function createAuthContext(request: NextRequest) {
  const user = getServerUser(request);
  
  return {
    user,
    isAuthenticated: !!user,
    hasRole: (role: string) => user ? hasServerRole(user, role) : false,
    isOwner: (resourceUserId: string) => user ? isServerResourceOwner(user, resourceUserId) : false,
    requireAuth: () => requireServerAuth(request),
    requireRole: (role: string) => requireServerRole(request, role),
    requireOwnership: (resourceUserId: string) => requireServerResourceOwnership(request, resourceUserId)
  };
}

/**
 * Middleware wrapper for API routes with authentication
 */
export function withServerAuth<T extends any[]>(
  handler: (request: NextRequest, ...args: T) => Promise<Response>,
  options: {
    required?: boolean;
    roles?: string[];
    allowOwnership?: boolean;
  } = {}
) {
  return async (request: NextRequest, ...args: T): Promise<Response> => {
    const { required = true, roles = [], allowOwnership = false } = options;

    try {
      const authContext = createAuthContext(request);

      if (required && !authContext.isAuthenticated) {
        throw new AuthenticationError('Authentication required');
      }

      if (roles.length > 0 && authContext.user) {
        const hasRequiredRole = roles.some(role => authContext.hasRole(role));
        
        if (!hasRequiredRole && !allowOwnership) {
          throw new AuthorizationError(`One of these roles required: ${roles.join(', ')}`);
        }
      }

      // Add auth context to request for handler use
      (request as any).auth = authContext;

      return await handler(request, ...args);

    } catch (error) {
      if (error instanceof AuthenticationError) {
        return new Response(
          JSON.stringify({ error: error.message, code: error.code }),
          { status: error.statusCode, headers: { 'Content-Type': 'application/json' } }
        );
      }

      if (error instanceof AuthorizationError) {
        return new Response(
          JSON.stringify({ error: error.message, code: error.code }),
          { status: error.statusCode, headers: { 'Content-Type': 'application/json' } }
        );
      }

      // Re-throw other errors
      throw error;
    }
  };
}
