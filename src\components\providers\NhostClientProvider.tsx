'use client';

import { NhostProvider } from '@nhost/nextjs';
import { nhost } from '@/lib/nhost';
import { ReactNode, useEffect, useState } from 'react';

export default function NhostClientProvider({ children }: { children: ReactNode }) {
  // Use a state to control rendering to avoid hydration mismatch
  const [mounted, setMounted] = useState(false);

  // Only render the provider's children after the component has mounted on the client
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    // Return a loading state that matches what will be rendered on the client
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-gray-600">Loading...</div>
      </div>
    );
  }

  return (
    <NhostProvider nhost={nhost} initial={undefined}>
      {children}
    </NhostProvider>
  );
}
