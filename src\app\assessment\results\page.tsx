'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuthenticationStatus } from '@nhost/nextjs';
import DashboardLayout from '@/components/layout/DashboardLayout';
import {
  ArrowLeftIcon,
  EyeIcon,
  DocumentArrowDownIcon,
  PencilIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import { type FilterMode, type StudentSubmission, filterSubmissionsBySession } from '@/lib/submission-filters';

// Remove GraphQL queries - we'll use API routes instead to bypass allowlist restrictions

export default function AssessmentResultsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, isLoading } = useAuthenticationStatus();

  // Get parameters from URL - can be either submissionId or assignmentId
  const submissionId = searchParams.get('submissionId');
  const assignmentId = searchParams.get('assignmentId');
  const isBulkView = !!assignmentId && !submissionId;

  // Filter mode state for bulk view - moved to top to avoid reference error
  const [filterMode, setFilterMode] = useState<FilterMode>('current-session');

  // State for API data (replacing GraphQL queries)
  const [assessmentData, setAssessmentData] = useState<any>(null);
  const [bulkAssessmentData, setBulkAssessmentData] = useState<any>(null);
  const [submissionData, setSubmissionData] = useState<any>(null);
  const [assessmentLoading, setAssessmentLoading] = useState(false);
  const [bulkAssessmentLoading, setBulkAssessmentLoading] = useState(false);
  const [submissionLoading, setSubmissionLoading] = useState(false);
  const [assessmentError, setAssessmentError] = useState<any>(null);
  const [bulkAssessmentError, setBulkAssessmentError] = useState<any>(null);

  // Function to fetch assessment results via API
  const fetchAssessmentResults = async () => {
    if (!submissionId || isBulkView) return;

    setAssessmentLoading(true);
    setAssessmentError(null);
    try {

      const response = await fetch(`/api/assessment-results?submissionId=${submissionId}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      setAssessmentData(data);
    } catch (error) {
      console.error('❌ Failed to load assessment results via API:', error);
      setAssessmentError(error);
    } finally {
      setAssessmentLoading(false);
    }
  };

  // Function to fetch bulk assessment results via API
  const fetchBulkAssessmentResults = async () => {
    if (!assignmentId || !isBulkView) return;

    setBulkAssessmentLoading(true);
    setBulkAssessmentError(null);
    try {

      const response = await fetch(`/api/assessment-results?assignmentId=${assignmentId}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      setBulkAssessmentData(data);
    } catch (error) {
      console.error('❌ Failed to load bulk assessment results via API:', error);
      setBulkAssessmentError(error);
    } finally {
      setBulkAssessmentLoading(false);
    }
  };

  // Function to fetch submission details via API
  const fetchSubmissionDetails = async () => {
    if (!submissionId || isBulkView) return;

    setSubmissionLoading(true);
    try {

      const response = await fetch(`/api/submissions?submissionId=${submissionId}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      setSubmissionData(data);
    } catch (error) {
      console.error('❌ Failed to load submission details via API:', error);
    } finally {
      setSubmissionLoading(false);
    }
  };

  // Fetch data when component mounts or parameters change
  useEffect(() => {
    if (isAuthenticated) {
      if (isBulkView) {
        fetchBulkAssessmentResults();
      } else if (submissionId) {
        fetchAssessmentResults();
        // Only fetch submission details if no assessment data exists
        if (!assessmentData?.assessment_results?.length) {
          fetchSubmissionDetails();
        }
      }
    }
  }, [isAuthenticated, submissionId, assignmentId, isBulkView]);

  // Process assessment data based on view type
  const assessment = assessmentData?.assessment_results?.[0];
  const allBulkAssessments = bulkAssessmentData?.assessment_results || [];
  const submission = submissionData?.submission || assessmentData?.submission || assessment?.student_submission;

  // Apply session-based filtering to bulk assessments
  const bulkAssessments = isBulkView ? allBulkAssessments.filter((assessment: any) => {
    // Convert assessment results to submission format for filtering
    const submissionForFilter: StudentSubmission = {
      id: assessment.student_submission.id,
      assignment_id: assignmentId || '',
      student_name: assessment.student_submission.student_name,
      file_path: '', // Not available in assessment results
      file_type: 'unknown', // Not available in assessment results
      processing_status: 'assessed',
      upload_date: assessment.created_at,
      // Optional fields for backward compatibility
      student_id: assessment.student_submission.student_name,
      status: 'assessed',
      created_at: assessment.created_at,
      submitted_at: assessment.created_at
    };

    const filtered = filterSubmissionsBySession([submissionForFilter], {
      mode: filterMode,
      assignmentId: assignmentId || undefined
    });

    return filtered.length > 0;
  }) : [];

  // State for editable content
  const [teacherNotes, setTeacherNotes] = useState('');
  const [actionPlan, setActionPlan] = useState<string[]>([]);
  const [selectedAssessment, setSelectedAssessment] = useState<any>(null);

  const [isEditingNotes, setIsEditingNotes] = useState(false);
  const [strengthsExpanded, setStrengthsExpanded] = useState(false);
  const [improvementsExpanded, setImprovementsExpanded] = useState(false);
  const [tempNotes, setTempNotes] = useState('');
  const [tempActionPlan, setTempActionPlan] = useState('');

  // Initialize data when assessment loads
  useEffect(() => {
    if (assessment) {
      setTeacherNotes('No teacher notes added yet.'); // teacher_notes field doesn't exist in assessment_results
      setTempNotes('');
      setActionPlan([]); // suggestions field doesn't exist in assessment_results
    }
  }, [assessment]);

  // Loading states
  if (isLoading || assessmentLoading || submissionLoading || bulkAssessmentLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">
            Loading {isBulkView ? 'bulk assessment' : 'assessment'} results...
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Error states
  if (!submissionId && !assignmentId) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-red-600">No submission ID or assignment ID provided</div>
        </div>
      </DashboardLayout>
    );
  }

  if ((assessmentError && !assessment) || (bulkAssessmentError && isBulkView)) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-red-600">
            Error loading assessment: {(assessmentError || bulkAssessmentError)?.message}
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // No assessment found
  if (!assessment && submission) {
    return (
      <DashboardLayout>
        <div className="max-w-4xl mx-auto p-6">
          <div className="text-center py-12">
            <h1 className="text-2xl font-semibold mb-4">Assessment Not Ready</h1>
            <p className="text-gray-600 mb-6">
              The AI assessment for this submission is not yet complete.
            </p>
            <p className="text-sm text-gray-500 mb-6">
              Submission: {submission.student_name} - {submission.assignment?.title}
              <br />
              Status: {submission.processing_status || 'Pending'}
            </p>
            <button
              onClick={() => router.push('/dashboard')}
              className="bg-accent text-white px-6 py-2 rounded-md hover:bg-[#9A7649]"
            >
              Back to Dashboard
            </button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // For bulk view, don't show "Submission not found" - let it fall through to bulk view
  if (!assessment && !submission && !isBulkView) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-red-600">Submission not found</div>
        </div>
      </DashboardLayout>
    );
  }

  const handleExport = () => {
    alert('Export functionality - PDF generation will be implemented');
  };

  const handleViewAsStudent = () => {
    alert('Student view will show a simplified version of this assessment');
  };

  const handleSaveNotes = () => {
    setTeacherNotes(tempNotes);
    setIsEditingNotes(false);
    // TODO: Save to database
  };

  const handleAddActionItem = () => {
    if (tempActionPlan.trim()) {
      setActionPlan(prev => [...prev, tempActionPlan.trim()]);
      setTempActionPlan('');
      // TODO: Save to database
    }
  };

  // Calculate bulk assessment statistics
  const calculateBulkStats = () => {
    if (bulkAssessments.length === 0) return null;

    const grades = bulkAssessments.map((a: any) => parseFloat(a.overall_grade)).filter((g: number) => !isNaN(g));
    const average = grades.length > 0 ? (grades.reduce((sum: number, g: number) => sum + g, 0) / grades.length).toFixed(1) : 'N/A';
    const highest = grades.length > 0 ? Math.max(...grades).toFixed(1) : 'N/A';
    const lowest = grades.length > 0 ? Math.min(...grades).toFixed(1) : 'N/A';

    return { average, highest, lowest, total: bulkAssessments.length };
  };

  // Render bulk assessment view
  if (isBulkView) {
    const stats = calculateBulkStats();

    return (
      <DashboardLayout>
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <button
              onClick={() => router.push('/assignments')}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              <ArrowLeftIcon className="w-4 h-4" />
              <span>Back to Assignments</span>
            </button>

            <button
              onClick={() => alert('Bulk export functionality coming soon')}
              className="flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
            >
              <DocumentArrowDownIcon className="w-4 h-4" />
              <span>Export All</span>
            </button>
          </div>

          {/* Assignment Header */}
          <div>
            <h1 className="text-3xl font-semibold text-text mb-2">
              Bulk Assessment Results
            </h1>
            <p className="text-gray-600">
              {allBulkAssessments.length > 0
                ? `${allBulkAssessments[0].student_submission.assignment.title} - ${allBulkAssessments.length} total assessments`
                : 'No assessments found for this assignment'
              }
            </p>
          </div>

          {/* Filter Controls */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium text-gray-700 flex items-center">
                <PlusIcon className="w-4 h-4 mr-2" />
                Assessment View
              </h3>
              <div className="text-xs text-gray-500">
                Showing {bulkAssessments.length} of {allBulkAssessments.length} assessments
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setFilterMode('current-session')}
                className={`px-3 py-1.5 rounded-md text-xs font-medium transition-colors ${
                  filterMode === 'current-session'
                    ? 'bg-green-500 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Current Session (30 min)
              </button>
              <button
                onClick={() => setFilterMode('recent')}
                className={`px-3 py-1.5 rounded-md text-xs font-medium transition-colors ${
                  filterMode === 'recent'
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Recent (24 hours)
              </button>
              <button
                onClick={() => setFilterMode('all')}
                className={`px-3 py-1.5 rounded-md text-xs font-medium transition-colors ${
                  filterMode === 'all'
                    ? 'bg-purple-500 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                <EyeIcon className="w-3 h-3 mr-1 inline" />
                All Assessments
              </button>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              {filterMode === 'current-session' && 'Shows only assessments from the last 30 minutes - perfect for current work'}
              {filterMode === 'recent' && 'Shows assessments from the last 24 hours'}
              {filterMode === 'all' && 'Shows all assessments including old ones - may include mixed assessment runs'}
            </p>
          </div>

          {/* Statistics Overview */}
          {stats && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="text-sm text-blue-600 mb-1">Total Assessed</div>
                <div className="text-2xl font-bold text-blue-900">{stats.total}</div>
              </div>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="text-sm text-green-600 mb-1">Average Grade</div>
                <div className="text-2xl font-bold text-green-900">{stats.average}</div>
              </div>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="text-sm text-yellow-600 mb-1">Highest Grade</div>
                <div className="text-2xl font-bold text-yellow-900">{stats.highest}</div>
              </div>
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="text-sm text-red-600 mb-1">Lowest Grade</div>
                <div className="text-2xl font-bold text-red-900">{stats.lowest}</div>
              </div>
            </div>
          )}

          {/* Assessment Results List */}
          <div className="bg-white border border-gray-200 rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Individual Results</h3>
            </div>

            {bulkAssessments.length === 0 ? (
              <div className="p-6 text-center text-gray-500">
                No assessment results found for this assignment.
              </div>
            ) : (
              <div className="divide-y divide-gray-200">
                {bulkAssessments.map((assessment: any) => (
                  <div key={assessment.id} className="p-6 hover:bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h4 className="text-lg font-medium text-gray-900">
                          {assessment.student_submission.student_name}
                        </h4>
                        <p className="text-sm text-gray-500 mt-1">
                          Assessed on {new Date(assessment.created_at).toLocaleDateString()}
                        </p>
                        <p className="text-sm text-gray-700 mt-2 line-clamp-2">
                          {assessment.general_comments || 'No comments available'}
                        </p>
                      </div>

                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <div className="text-sm text-gray-500">Grade</div>
                          <div className="text-2xl font-bold text-green-600">
                            {assessment.overall_grade || 'N/A'}
                          </div>
                        </div>

                        <button
                          onClick={() => router.push(`/assessment/results?submissionId=${assessment.student_submission.id}`)}
                          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                        >
                          View Details
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <button
            onClick={() => router.push('/dashboard')}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            <ArrowLeftIcon className="w-4 h-4" />
            <span>Back to Dashboard</span>
          </button>

          <div className="flex items-center space-x-3">
            <button
              onClick={() => {
                // Clear localStorage and start fresh assessment
                localStorage.removeItem('assessmentConfig');
                localStorage.removeItem('currentSubmission');

                router.push('/student-upload');
              }}
              className="flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
            >
              <PlusIcon className="w-4 h-4" />
              <span>New Assessment</span>
            </button>

            <button
              onClick={handleViewAsStudent}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              <EyeIcon className="w-4 h-4" />
              <span>View as Student</span>
            </button>
          </div>
        </div>

        {/* Assignment Header */}
        <div className="flex items-start justify-between">
          <div>
            <h1 className="text-3xl font-semibold text-text mb-2">
              {assessment?.student_submission?.assignment?.title || submission?.assignment?.title || 'Assessment Results'}
            </h1>
            <p className="text-gray-600">{assessment?.student_submission?.assignment?.description || submission?.assignment?.description || 'AI Assessment'}</p>
          </div>

          <div className="text-right">
            <div className="text-sm text-gray-500 mb-2">Overall Score</div>
            <div className="text-4xl font-bold text-green-600 mb-3">
              {assessment?.overall_grade || 'N/A'}
            </div>
            <button
              onClick={handleExport}
              className="flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
            >
              <DocumentArrowDownIcon className="w-4 h-4" />
              <span>Export</span>
            </button>
          </div>
        </div>

        {/* Assessment Details */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-medium text-blue-900">Assessment Details</h3>
            <span className="text-sm text-blue-700">
              {assessment.created_at ? new Date(assessment.created_at).toLocaleDateString() : 'Recently generated'}
            </span>
          </div>
          <p className="text-blue-800 text-sm">
            Student: {assessment?.student_submission?.student_name || submission?.student_name || 'Unknown'} •
            Grade: {assessment?.overall_grade || 'N/A'} •
            Reviewed: {assessment?.reviewed ? 'Yes' : 'No'}
          </p>
        </div>



        {/* Criteria Performance Overview */}
        {assessment?.per_criterion_feedbacks && assessment.per_criterion_feedbacks.length > 0 && (
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Criteria Performance Overview</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {assessment.per_criterion_feedbacks.map((feedback: any, index: number) => {
                const score = feedback.score ? parseFloat(feedback.score) : 0;
                const getScoreColor = (score: number) => {
                  if (score >= 80) return 'text-green-600 bg-green-50 border-green-200';
                  if (score >= 70) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
                  return 'text-red-600 bg-red-50 border-red-200';
                };

                return (
                  <div key={feedback.id} className={`p-4 rounded-lg border ${getScoreColor(score)}`}>
                    <div className="text-sm font-medium mb-1">
                      {feedback.assessment_criterium?.criterion_name || `Criterion ${index + 1}`}
                    </div>
                    <div className="text-2xl font-bold mb-2">
                      {feedback.score || 'N/A'}
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          score >= 80 ? 'bg-green-500' :
                          score >= 70 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${Math.min(score, 100)}%` }}
                      ></div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Summary Statistics */}
            <div className="mt-6 pt-4 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-sm text-gray-500">Average Score</div>
                  <div className="text-xl font-bold text-gray-900">
                    {assessment.per_criterion_feedbacks.length > 0 ? (
                      (assessment.per_criterion_feedbacks
                        .filter((f: any) => f.score)
                        .reduce((sum: number, f: any) => sum + parseFloat(f.score), 0) /
                       assessment.per_criterion_feedbacks.filter((f: any) => f.score).length
                      ).toFixed(1)
                    ) : 'N/A'}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Criteria Assessed</div>
                  <div className="text-xl font-bold text-gray-900">
                    {assessment.per_criterion_feedbacks.length}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Above 80%</div>
                  <div className="text-xl font-bold text-green-600">
                    {assessment.per_criterion_feedbacks.filter((f: any) =>
                      f.score && parseFloat(f.score) >= 80
                    ).length}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Teacher Notes */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
              Teacher Notes
            </h3>
            <button
              onClick={() => setIsEditingNotes(!isEditingNotes)}
              className="text-gray-500 hover:text-gray-700 flex items-center space-x-1"
            >
              <PlusIcon className="w-4 h-4" />
              <span className="text-sm">Add Notes</span>
            </button>
          </div>

          {isEditingNotes ? (
            <div className="space-y-3">
              <textarea
                value={tempNotes}
                onChange={(e) => setTempNotes(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent"
                rows={3}
                placeholder="Add your notes here..."
              />
              <div className="flex justify-end space-x-2">
                <button
                  onClick={() => setIsEditingNotes(false)}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveNotes}
                  className="px-4 py-2 bg-accent text-white rounded-md hover:bg-[#9A7649]"
                >
                  Save Changes
                </button>
              </div>
            </div>
          ) : (
            <p className="text-gray-700">{teacherNotes}</p>
          )}
        </div>

        {/* Summary */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Summary</h3>
          <p className="text-gray-700">{assessment?.general_comments || 'No summary available'}</p>
        </div>

        {/* Action Plan */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
            <h3 className="text-lg font-medium text-green-900">Action Plan</h3>
          </div>

          <div className="space-y-2 mb-4">
            {actionPlan.map((item, index) => (
              <div key={index} className="text-green-800">• {item}</div>
            ))}
          </div>

          <div className="flex space-x-2">
            <input
              type="text"
              value={tempActionPlan}
              onChange={(e) => setTempActionPlan(e.target.value)}
              placeholder="Add a new action item..."
              className="flex-1 px-3 py-2 border border-green-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              onKeyPress={(e) => e.key === 'Enter' && handleAddActionItem()}
            />
            <button
              onClick={handleAddActionItem}
              className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
            >
              Add
            </button>
          </div>
        </div>

        {/* Per-Criteria Assessment Results */}
        {assessment?.per_criterion_feedbacks && assessment.per_criterion_feedbacks.length > 0 && (
          <div className="bg-white border border-gray-200 rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Detailed Criteria Assessment</h3>
              <p className="text-sm text-gray-600 mt-1">Individual breakdown by assessment criteria</p>
            </div>

            <div className="divide-y divide-gray-200">
              {assessment.per_criterion_feedbacks.map((feedback: any, index: number) => (
                <div key={feedback.id} className="p-6">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="text-md font-medium text-gray-900">
                        {feedback.assessment_criterium?.criterion_name || `Criterion ${index + 1}`}
                      </h4>
                      <div className="flex items-center mt-1">
                        <span className="text-sm text-gray-500">Score: </span>
                        <span className="text-lg font-semibold text-blue-600 ml-1">
                          {feedback.score || 'N/A'}
                        </span>
                        {feedback.score && (
                          <div className="ml-3 flex-1 max-w-xs">
                            <div className="bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${Math.min((parseFloat(feedback.score) / 100) * 100, 100)}%` }}
                              ></div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {feedback.feedback_text && (
                    <div className="mt-3">
                      <p className="text-sm text-gray-700 leading-relaxed">
                        {feedback.feedback_text}
                      </p>
                    </div>
                  )}

                  {!feedback.feedback_text && (
                    <div className="mt-3">
                      <p className="text-sm text-gray-500 italic">
                        No detailed feedback provided for this criterion.
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Strengths */}
        <div className="bg-white border border-gray-200 rounded-lg">
          <div
            className="flex items-center justify-between p-6 cursor-pointer hover:bg-gray-50"
            onClick={() => setStrengthsExpanded(!strengthsExpanded)}
          >
            <h3 className="text-lg font-medium text-gray-900">
              <span className="mr-2">▼</span>Key Strengths
            </h3>
            {strengthsExpanded ? (
              <ChevronUpIcon className="w-5 h-5 text-gray-400" />
            ) : (
              <ChevronDownIcon className="w-5 h-5 text-gray-400" />
            )}
          </div>

          {strengthsExpanded && (
            <div className="px-6 pb-6 border-t border-gray-200">
              <div className="space-y-2 pt-4">
                {assessment?.per_criterion_feedbacks?.length > 0 ? (
                  <div className="text-gray-700">
                    <p className="mb-2 font-medium">Extracted from criteria assessments:</p>
                    {assessment.per_criterion_feedbacks
                      .filter((feedback: any) => feedback.score && parseFloat(feedback.score) >= 80)
                      .map((feedback: any, index: number) => (
                        <div key={index} className="mb-2">
                          • <strong>{feedback.assessment_criterium?.criterion_name}:</strong> Strong performance ({feedback.score})
                        </div>
                      ))
                    }
                  </div>
                ) : (
                  <div className="text-gray-700">• Strengths will be extracted from general comments</div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Areas for Improvement */}
        <div className="bg-white border border-gray-200 rounded-lg">
          <div
            className="flex items-center justify-between p-6 cursor-pointer hover:bg-gray-50"
            onClick={() => setImprovementsExpanded(!improvementsExpanded)}
          >
            <h3 className="text-lg font-medium text-gray-900">Areas for Improvement</h3>
            {improvementsExpanded ? (
              <ChevronUpIcon className="w-5 h-5 text-gray-400" />
            ) : (
              <ChevronDownIcon className="w-5 h-5 text-gray-400" />
            )}
          </div>

          {improvementsExpanded && (
            <div className="px-6 pb-6 border-t border-gray-200">
              <div className="space-y-2 pt-4">
                {assessment?.per_criterion_feedbacks?.length > 0 ? (
                  <div className="text-gray-700">
                    <p className="mb-2 font-medium">Areas needing attention based on criteria scores:</p>
                    {assessment.per_criterion_feedbacks
                      .filter((feedback: any) => feedback.score && parseFloat(feedback.score) < 70)
                      .map((feedback: any, index: number) => (
                        <div key={index} className="mb-3 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                          <div className="font-medium text-yellow-800">
                            {feedback.assessment_criterium?.criterion_name} (Score: {feedback.score})
                          </div>
                          {feedback.feedback_text && (
                            <div className="text-sm text-yellow-700 mt-1">
                              {feedback.feedback_text}
                            </div>
                          )}
                        </div>
                      ))
                    }
                    {assessment.per_criterion_feedbacks.filter((feedback: any) =>
                      feedback.score && parseFloat(feedback.score) < 70
                    ).length === 0 && (
                      <div className="text-green-700">• All criteria scored above 70% - excellent work!</div>
                    )}
                  </div>
                ) : (
                  <div className="text-gray-700">• Areas for improvement will be extracted from general comments</div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="text-center py-6 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            Assessment completed • {assessment?.created_at ? new Date(assessment.created_at).toLocaleDateString() : 'Recently generated'}
          </p>
        </div>
      </div>
    </DashboardLayout>
  );
}
