import { ApolloClient, InMemoryCache, from, createHttpLink } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';

// Client-side Apollo Client (uses nhost context)
let clientApolloClient: ApolloClient<any> | null = null;

export function getClientApolloClient() {
  if (typeof window === 'undefined') {
    throw new Error('getClientApolloClient should only be called on the client side');
  }

  if (!clientApolloClient) {
    const { nhost } = require('./nhost');

    const httpLink = createHttpLink({
      uri: `https://${process.env.NEXT_PUBLIC_NHOST_SUBDOMAIN}.graphql.${process.env.NEXT_PUBLIC_NHOST_REGION}.nhost.run/v1`,
    });

    const authLink = setContext((_, { headers }) => {
      const token = nhost.auth.getAccessToken();
      const user = nhost.auth.getUser();

      // Build Hasura headers
      const hasuraHeaders: Record<string, string> = {};

      if (token) {
        hasuraHeaders.authorization = `Bearer ${token}`;

        // Try to decode JWT token to see what's inside
        try {
          const tokenParts = token.split('.');
          if (tokenParts.length === 3) {
            const payload = JSON.parse(atob(tokenParts[1]));
            // JWT payload contains sensitive data - do not log in production

            // Extract Hasura claims from JWT
            const hasuraClaims = payload['https://hasura.io/jwt/claims'];
            if (hasuraClaims) {
              // Hasura claims extracted successfully
              // Use claims from JWT
              if (hasuraClaims['x-hasura-user-id']) {
                hasuraHeaders['x-hasura-user-id'] = hasuraClaims['x-hasura-user-id'];
              }
              if (hasuraClaims['x-hasura-default-role']) {
                hasuraHeaders['x-hasura-default-role'] = hasuraClaims['x-hasura-default-role'];
              }
              if (hasuraClaims['x-hasura-allowed-roles']) {
                hasuraHeaders['x-hasura-allowed-roles'] = Array.isArray(hasuraClaims['x-hasura-allowed-roles'])
                  ? hasuraClaims['x-hasura-allowed-roles'].join(',')
                  : hasuraClaims['x-hasura-allowed-roles'];
              }
            } else {
              console.log('⚠️ No Hasura claims found in JWT, using fallback');
              // Fallback to user object or defaults
              if (user?.id) {
                hasuraHeaders['x-hasura-user-id'] = user.id;
              }
              hasuraHeaders['x-hasura-default-role'] = 'user';
              hasuraHeaders['x-hasura-allowed-roles'] = 'user';
            }
          }
        } catch (error) {
          console.error('❌ Error decoding JWT:', error);
          // Fallback to user object or defaults
          if (user?.id) {
            hasuraHeaders['x-hasura-user-id'] = user.id;
          }
          hasuraHeaders['x-hasura-default-role'] = 'user';
          hasuraHeaders['x-hasura-allowed-roles'] = 'user';
        }
      }

      // Authentication debug info (production-safe)
      if (process.env.NODE_ENV === 'development') {
        console.log('🔑 Apollo Auth Status:', {
          hasToken: !!token,
          isAuthenticated: nhost.auth.isAuthenticated(),
          hasUserId: !!hasuraHeaders['x-hasura-user-id'],
          role: hasuraHeaders['x-hasura-default-role']
        });
      }

      return {
        headers: {
          ...headers,
          ...hasuraHeaders,
        },
      };
    });

    clientApolloClient = new ApolloClient({
      link: from([authLink, httpLink]),
      cache: new InMemoryCache(),
    });
  }

  return clientApolloClient;
}

// Server-side Apollo Client (uses admin secret)
export function getServerApolloClient() {
  const httpLink = createHttpLink({
    uri: `https://${process.env.NEXT_PUBLIC_NHOST_SUBDOMAIN}.graphql.${process.env.NEXT_PUBLIC_NHOST_REGION}.nhost.run/v1`,
  });

  const authLink = setContext((_, { headers }) => {
    return {
      headers: {
        ...headers,
        'x-hasura-admin-secret': process.env.NHOST_ADMIN_SECRET,
      },
    };
  });

  return new ApolloClient({
    link: from([authLink, httpLink]),
    cache: new InMemoryCache(),
  });
}

// Legacy export removed to prevent SSR issues
