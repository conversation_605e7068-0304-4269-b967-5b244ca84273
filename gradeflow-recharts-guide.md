# GradeFlow: Recharts Score Distribution Chart Implementation Guide

## Overview

This guide provides step-by-step instructions for implementing a score distribution chart using Recharts in the GradeFlow application. The implementation fetches assessment data based on the authenticated user's ID and displays it on an analytics page.

## Architecture Overview

- **Frontend**: Next.js with TypeScript
- **Backend**: Nhost (PostgreSQL + Hasura GraphQL)
- **Authentication**: Nhost Auth
- **Charting Library**: Recharts
- **Data Flow**: User → Next.js → Nhost GraphQL → PostgreSQL → Chart

## Prerequisites

- Nhost project set up with authentication enabled
- Next.js application with Nhost SDK installed
- Recharts package installed
- Basic understanding of GraphQL queries

## Step 1: Database Schema Setup

First, ensure your Nhost database has the necessary tables and relationships:

```sql
-- Assessments table
CREATE TABLE assessments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  user_id UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMP DEFAULT NOW()
);

-- Assessment scores table
CREATE TABLE assessment_scores (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  assessment_id UUID NOT NULL REFERENCES assessments(id),
  student_id UUID NOT NULL,
  score NUMERIC NOT NULL,
  max_score NUMERIC NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_assessments_user_id ON assessments(user_id);
CREATE INDEX idx_assessment_scores_assessment_id ON assessment_scores(assessment_id);
```

## Step 2: GraphQL Query Setup

In your Nhost console, create a GraphQL query to fetch score distribution data:

```graphql
# queries/getScoreDistribution.graphql
query GetScoreDistribution($userId: uuid!, $assessmentId: uuid!) {
  assessments(
    where: {
      id: { _eq: $assessmentId }
      user_id: { _eq: $userId }
    }
  ) {
    id
    title
    assessment_scores {
      id
      score
      max_score
    }
  }
}
```

## Step 3: Install Required Dependencies

```bash
npm install recharts @nhost/nextjs graphql
# or
yarn add recharts @nhost/nextjs graphql
```

## Step 4: Create the Score Distribution Hook

Create a custom hook to fetch and transform the data:

```typescript
// hooks/useScoreDistribution.ts
import { useQuery } from '@apollo/client';
import { useUserId } from '@nhost/nextjs';
import { gql } from 'graphql-tag';

const GET_SCORE_DISTRIBUTION = gql`
  query GetScoreDistribution($userId: uuid!, $assessmentId: uuid!) {
    assessments(
      where: {
        id: { _eq: $assessmentId }
        user_id: { _eq: $userId }
      }
    ) {
      id
      title
      assessment_scores {
        id
        score
        max_score
      }
    }
  }
`;

interface ScoreData {
  id: string;
  score: number;
  max_score: number;
}

interface AssessmentData {
  id: string;
  title: string;
  assessment_scores: ScoreData[];
}

export const useScoreDistribution = (assessmentId: string) => {
  const userId = useUserId();

  const { data, loading, error } = useQuery(GET_SCORE_DISTRIBUTION, {
    variables: { userId, assessmentId },
    skip: !userId || !assessmentId,
  });

  // Transform data for Recharts
  const chartData = data?.assessments[0]?.assessment_scores.map((score: ScoreData) => {
    const percentage = (score.score / score.max_score) * 100;
    return {
      percentage: Math.round(percentage),
      score: score.score,
      maxScore: score.max_score,
    };
  }) || [];

  // Create distribution buckets (0-10%, 11-20%, etc.)
  const distribution = Array.from({ length: 10 }, (_, i) => {
    const min = i * 10;
    const max = (i + 1) * 10;
    const count = chartData.filter(
      (item: any) => item.percentage > min && item.percentage <= max
    ).length;
    
    return {
      range: `${min + 1}-${max}%`,
      count,
      percentage: (count / chartData.length) * 100 || 0,
    };
  });

  return {
    data: distribution,
    assessmentTitle: data?.assessments[0]?.title || '',
    loading,
    error,
    totalScores: chartData.length,
  };
};
```

## Step 5: Create the Score Distribution Chart Component

```typescript
// components/ScoreDistributionChart.tsx
import React from 'react';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell,
} from 'recharts';

interface ScoreDistributionChartProps {
  data: Array<{
    range: string;
    count: number;
    percentage: number;
  }>;
  title?: string;
}

const ScoreDistributionChart: React.FC<ScoreDistributionChartProps> = ({ data, title }) => {
  // Color scale for bars (red to green)
  const getBarColor = (index: number) => {
    const colors = [
      '#ef4444', // red-500
      '#f97316', // orange-500
      '#f59e0b', // amber-500
      '#eab308', // yellow-500
      '#84cc16', // lime-500
      '#22c55e', // green-500
      '#10b981', // emerald-500
      '#14b8a6', // teal-500
      '#06b6d4', // cyan-500
      '#0ea5e9', // sky-500
    ];
    return colors[index] || '#3b82f6';
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded shadow-lg">
          <p className="font-semibold">{label}</p>
          <p className="text-sm">Students: {payload[0].value}</p>
          <p className="text-sm">Percentage: {payload[0].payload.percentage.toFixed(1)}%</p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="w-full">
      {title && <h3 className="text-lg font-semibold mb-4">{title}</h3>}
      <ResponsiveContainer width="100%" height={400}>
        <BarChart
          data={data}
          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            dataKey="range" 
            angle={-45}
            textAnchor="end"
            height={60}
          />
          <YAxis 
            label={{ value: 'Number of Students', angle: -90, position: 'insideLeft' }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          <Bar dataKey="count" name="Students" radius={[8, 8, 0, 0]}>
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={getBarColor(index)} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default ScoreDistributionChart;
```

## Step 6: Create the Analytics Page

```typescript
// pages/analytics/[assessmentId].tsx or app/analytics/[assessmentId]/page.tsx
import React from 'react';
import { useRouter } from 'next/router';
import { useAuthenticated } from '@nhost/nextjs';
import ScoreDistributionChart from '@/components/ScoreDistributionChart';
import { useScoreDistribution } from '@/hooks/useScoreDistribution';

const AssessmentAnalyticsPage: React.FC = () => {
  const router = useRouter();
  const { assessmentId } = router.query;
  const isAuthenticated = useAuthenticated();
  
  const { data, assessmentTitle, loading, error, totalScores } = useScoreDistribution(
    assessmentId as string
  );

  // Redirect if not authenticated
  React.useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, router]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded">
          Error loading assessment data: {error.message}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Assessment Analytics</h1>
      
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-2">{assessmentTitle}</h2>
        <p className="text-gray-600">Total Students: {totalScores}</p>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <ScoreDistributionChart 
          data={data} 
          title="Score Distribution"
        />
      </div>

      <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {data.map((bucket) => (
          <div key={bucket.range} className="bg-white rounded-lg shadow p-4">
            <h4 className="font-semibold text-gray-700">{bucket.range}</h4>
            <p className="text-2xl font-bold">{bucket.count}</p>
            <p className="text-sm text-gray-500">
              {bucket.percentage.toFixed(1)}% of students
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AssessmentAnalyticsPage;
```

## Step 7: Add Navigation to Analytics

Create a link component to navigate to the analytics page:

```typescript
// components/AssessmentCard.tsx
import Link from 'next/link';

interface AssessmentCardProps {
  assessmentId: string;
  title: string;
  studentCount: number;
}

const AssessmentCard: React.FC<AssessmentCardProps> = ({ 
  assessmentId, 
  title, 
  studentCount 
}) => {
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      <p className="text-gray-600 mb-4">{studentCount} students</p>
      <Link 
        href={`/analytics/${assessmentId}`}
        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
      >
        View Analytics
      </Link>
    </div>
  );
};
```

## Step 8: Handle Edge Cases

Add error boundaries and loading states:

```typescript
// components/ErrorBoundary.tsx
import React, { Component, ReactNode } from 'react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
}

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false
  };

  public static getDerivedStateFromError(_: Error): State {
    return { hasError: true };
  }

  public componentDidCatch(error: Error, errorInfo: any) {
    console.error('Uncaught error:', error, errorInfo);
  }

  public render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Oops, something went wrong</h2>
            <button
              onClick={() => this.setState({ hasError: false })}
              className="bg-blue-500 text-white px-4 py-2 rounded"
            >
              Try again
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Wrap your analytics page with ErrorBoundary
<ErrorBoundary>
  <AssessmentAnalyticsPage />
</ErrorBoundary>
```

## Step 9: Optimize Performance

Add data caching and memoization:

```typescript
// hooks/useScoreDistribution.ts (updated)
import { useMemo } from 'react';

export const useScoreDistribution = (assessmentId: string) => {
  // ... existing code ...

  const distribution = useMemo(() => {
    if (!chartData.length) return [];
    
    return Array.from({ length: 10 }, (_, i) => {
      const min = i * 10;
      const max = (i + 1) * 10;
      const count = chartData.filter(
        (item: any) => item.percentage > min && item.percentage <= max
      ).length;
      
      return {
        range: `${min + 1}-${max}%`,
        count,
        percentage: (count / chartData.length) * 100 || 0,
      };
    });
  }, [chartData]);

  return {
    data: distribution,
    assessmentTitle: data?.assessments[0]?.title || '',
    loading,
    error,
    totalScores: chartData.length,
  };
};
```

## Step 10: Add Export Functionality

Allow users to export the chart data:

```typescript
// utils/exportData.ts
export const exportToCSV = (data: any[], filename: string) => {
  const csv = [
    ['Score Range', 'Student Count', 'Percentage'],
    ...data.map(row => [row.range, row.count, row.percentage.toFixed(2)])
  ]
    .map(row => row.join(','))
    .join('\n');

  const blob = new Blob([csv], { type: 'text/csv' });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `${filename}.csv`;
  a.click();
  window.URL.revokeObjectURL(url);
};

// Add to your analytics page
<button
  onClick={() => exportToCSV(data, `score-distribution-${assessmentId}`)}
  className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
>
  Export CSV
</button>
```

## Testing Checklist

- [ ] User authentication works correctly
- [ ] Chart loads with correct data
- [ ] Chart is responsive on different screen sizes
- [ ] Error states are handled gracefully
- [ ] Loading states are shown appropriately
- [ ] Data updates when assessment scores change
- [ ] Export functionality works correctly
- [ ] Performance is acceptable with large datasets

## Troubleshooting

### Common Issues and Solutions

1. **Chart not rendering**: Ensure ResponsiveContainer has explicit height
2. **No data showing**: Check GraphQL permissions in Nhost console
3. **Authentication errors**: Verify Nhost is properly configured in _app.tsx
4. **Performance issues**: Implement pagination for large datasets

## Next Steps

1. Add real-time updates using GraphQL subscriptions
2. Implement additional chart types (pie chart, line chart)
3. Add filtering options (date range, student groups)
4. Create a dashboard with multiple analytics widgets
5. Add comparative analysis between assessments

## Security Considerations

- Always validate user ownership of assessments
- Use row-level security in Hasura
- Sanitize any user inputs
- Implement rate limiting for API calls
- Use HTTPS in production

This completes the implementation guide for the Recharts score distribution chart in GradeFlow.