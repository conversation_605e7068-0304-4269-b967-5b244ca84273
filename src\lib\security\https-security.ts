/**
 * HTTPS/TLS Security System
 * Provides HSTS, certificate pinning, TLS enforcement, and transport security
 */

import { NextRequest, NextResponse } from 'next/server';

export interface HTTPSSecurityConfig {
  enableHSTS: boolean;
  hstsMaxAge: number;
  hstsIncludeSubdomains: boolean;
  hstsPreload: boolean;
  enableCertificatePinning: boolean;
  certificatePins: string[];
  minTLSVersion: string;
  enforceHTTPS: boolean;
  enableOCSPStapling: boolean;
}

// Default HTTPS security configuration
export const DEFAULT_HTTPS_CONFIG: HTTPSSecurityConfig = {
  enableHSTS: true,
  hstsMaxAge: 31536000, // 1 year
  hstsIncludeSubdomains: true,
  hstsPreload: true,
  enableCertificatePinning: false, // Disabled by default due to complexity
  certificatePins: [],
  minTLSVersion: '1.2',
  enforceHTTPS: true,
  enableOCSPStapling: true
};

/**
 * HTTPS Security Headers
 */
export function getHTTPSSecurityHeaders(config: HTTPSSecurityConfig = DEFAULT_HTTPS_CONFIG): Record<string, string> {
  const headers: Record<string, string> = {};

  // HTTP Strict Transport Security (HSTS)
  if (config.enableHSTS) {
    let hstsValue = `max-age=${config.hstsMaxAge}`;
    
    if (config.hstsIncludeSubdomains) {
      hstsValue += '; includeSubDomains';
    }
    
    if (config.hstsPreload) {
      hstsValue += '; preload';
    }
    
    headers['Strict-Transport-Security'] = hstsValue;
  }

  // Certificate Pinning (HTTP Public Key Pinning - deprecated but shown for reference)
  if (config.enableCertificatePinning && config.certificatePins.length > 0) {
    const pins = config.certificatePins.map(pin => `pin-sha256="${pin}"`).join('; ');
    headers['Public-Key-Pins'] = `${pins}; max-age=5184000; includeSubDomains`;
    
    // Note: HPKP is deprecated, use Certificate Transparency and Expect-CT instead
    headers['Expect-CT'] = 'max-age=86400, enforce';
  }

  // Additional transport security headers
  headers['X-Content-Type-Options'] = 'nosniff';
  headers['X-Frame-Options'] = 'DENY';
  headers['X-XSS-Protection'] = '1; mode=block';
  headers['Referrer-Policy'] = 'strict-origin-when-cross-origin';

  // Enhanced Content Security Policy for HTTPS
  headers['Content-Security-Policy'] = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self' data:",
    "connect-src 'self' https:",
    "frame-ancestors 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "upgrade-insecure-requests"
  ].join('; ');

  return headers;
}

/**
 * HTTPS Enforcement Middleware
 */
export function withHTTPSEnforcement(
  handler: (request: NextRequest) => Promise<NextResponse>,
  config: HTTPSSecurityConfig = DEFAULT_HTTPS_CONFIG
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const url = request.nextUrl.clone();
    const isHTTPS = url.protocol === 'https:';
    const isLocalhost = url.hostname === 'localhost' || url.hostname === '127.0.0.1';
    const isDevelopment = process.env.NODE_ENV === 'development';

    // Enforce HTTPS in production
    if (config.enforceHTTPS && !isHTTPS && !isLocalhost && !isDevelopment) {
      url.protocol = 'https:';
      
      console.warn('🔒 Redirecting HTTP to HTTPS:', {
        originalUrl: request.url,
        redirectUrl: url.toString()
      });
      
      return NextResponse.redirect(url, 301);
    }

    // Check TLS version (simplified check)
    if (isHTTPS && config.minTLSVersion) {
      // In a real implementation, you'd check the actual TLS version from the connection
      // This is a simplified example
      const tlsVersion = request.headers.get('x-forwarded-proto-version') || '1.2';
      
      if (parseFloat(tlsVersion) < parseFloat(config.minTLSVersion)) {
        return new NextResponse('TLS version not supported', { status: 426 });
      }
    }

    // Execute the handler
    const response = await handler(request);

    // Add HTTPS security headers
    const securityHeaders = getHTTPSSecurityHeaders(config);
    Object.entries(securityHeaders).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    // Add security headers specific to HTTPS
    if (isHTTPS || isDevelopment) {
      // Secure cookie settings
      response.headers.set('Set-Cookie', 
        response.headers.get('Set-Cookie')?.replace(/; Secure/g, '') + '; Secure; SameSite=Strict' || ''
      );
    }

    return response;
  };
}

/**
 * Certificate Validation System
 */
export class CertificateValidator {
  private trustedCertificates: Map<string, { fingerprint: string; expiry: Date; issuer: string }>;

  constructor() {
    this.trustedCertificates = new Map();
  }

  /**
   * Add a trusted certificate
   */
  addTrustedCertificate(domain: string, fingerprint: string, expiry: Date, issuer: string): void {
    this.trustedCertificates.set(domain, { fingerprint, expiry, issuer });
  }

  /**
   * Validate certificate for a domain
   */
  validateCertificate(domain: string, fingerprint: string): {
    isValid: boolean;
    reason?: string;
    expiry?: Date;
  } {
    const trusted = this.trustedCertificates.get(domain);
    
    if (!trusted) {
      return { isValid: false, reason: 'Certificate not in trusted list' };
    }

    if (trusted.fingerprint !== fingerprint) {
      return { isValid: false, reason: 'Certificate fingerprint mismatch' };
    }

    if (trusted.expiry < new Date()) {
      return { isValid: false, reason: 'Certificate expired', expiry: trusted.expiry };
    }

    return { isValid: true, expiry: trusted.expiry };
  }

  /**
   * Get certificates expiring soon
   */
  getExpiringCertificates(days: number = 30): Array<{ domain: string; expiry: Date; daysLeft: number }> {
    const now = new Date();
    const threshold = new Date(now.getTime() + (days * 24 * 60 * 60 * 1000));
    
    return Array.from(this.trustedCertificates.entries())
      .filter(([_, cert]) => cert.expiry <= threshold && cert.expiry > now)
      .map(([domain, cert]) => ({
        domain,
        expiry: cert.expiry,
        daysLeft: Math.ceil((cert.expiry.getTime() - now.getTime()) / (24 * 60 * 60 * 1000))
      }));
  }
}

/**
 * TLS Configuration Validator
 */
export class TLSConfigValidator {
  private secureProtocols = ['TLSv1.2', 'TLSv1.3'];
  private secureCiphers = [
    'ECDHE-RSA-AES256-GCM-SHA384',
    'ECDHE-RSA-AES128-GCM-SHA256',
    'ECDHE-RSA-AES256-SHA384',
    'ECDHE-RSA-AES128-SHA256'
  ];

  /**
   * Validate TLS configuration
   */
  validateTLSConfig(config: {
    protocol?: string;
    cipher?: string;
    keyLength?: number;
  }): {
    isSecure: boolean;
    warnings: string[];
    recommendations: string[];
  } {
    const warnings: string[] = [];
    const recommendations: string[] = [];
    let isSecure = true;

    // Check protocol version
    if (config.protocol && !this.secureProtocols.includes(config.protocol)) {
      warnings.push(`Insecure TLS protocol: ${config.protocol}`);
      recommendations.push('Use TLS 1.2 or higher');
      isSecure = false;
    }

    // Check cipher suite
    if (config.cipher && !this.secureCiphers.includes(config.cipher)) {
      warnings.push(`Potentially weak cipher: ${config.cipher}`);
      recommendations.push('Use ECDHE with AES-GCM for forward secrecy');
    }

    // Check key length
    if (config.keyLength && config.keyLength < 2048) {
      warnings.push(`Weak key length: ${config.keyLength} bits`);
      recommendations.push('Use at least 2048-bit RSA or 256-bit ECC keys');
      isSecure = false;
    }

    return { isSecure, warnings, recommendations };
  }
}

/**
 * HTTPS Security Monitor
 */
export class HTTPSSecurityMonitor {
  private certificateValidator: CertificateValidator;
  private tlsValidator: TLSConfigValidator;

  constructor() {
    this.certificateValidator = new CertificateValidator();
    this.tlsValidator = new TLSConfigValidator();
  }

  /**
   * Monitor HTTPS security status
   */
  async monitorHTTPSSecurity(): Promise<{
    httpsEnabled: boolean;
    hstsEnabled: boolean;
    certificateValid: boolean;
    tlsSecure: boolean;
    expiringCertificates: Array<{ domain: string; expiry: Date; daysLeft: number }>;
    securityScore: number;
  }> {
    // In a real implementation, you'd check actual server configuration
    const httpsEnabled = process.env.NODE_ENV === 'production';
    const hstsEnabled = true; // Based on our configuration
    const certificateValid = true; // Would check actual certificates
    const tlsSecure = true; // Would validate TLS configuration

    const expiringCertificates = this.certificateValidator.getExpiringCertificates();

    // Calculate security score (0-100)
    let score = 0;
    if (httpsEnabled) score += 30;
    if (hstsEnabled) score += 20;
    if (certificateValid) score += 25;
    if (tlsSecure) score += 25;
    if (expiringCertificates.length === 0) score += 0;
    else score -= expiringCertificates.length * 5;

    return {
      httpsEnabled,
      hstsEnabled,
      certificateValid,
      tlsSecure,
      expiringCertificates,
      securityScore: Math.max(0, Math.min(100, score))
    };
  }

  /**
   * Get HTTPS security recommendations
   */
  getSecurityRecommendations(): string[] {
    const recommendations: string[] = [];

    if (process.env.NODE_ENV !== 'production') {
      recommendations.push('Enable HTTPS in production environment');
    }

    recommendations.push('Implement Certificate Transparency monitoring');
    recommendations.push('Set up automated certificate renewal');
    recommendations.push('Enable OCSP stapling for better performance');
    recommendations.push('Consider implementing Certificate Authority Authorization (CAA) records');

    return recommendations;
  }
}

// Global instances
export const certificateValidator = new CertificateValidator();
export const tlsValidator = new TLSConfigValidator();
export const httpsMonitor = new HTTPSSecurityMonitor();

/**
 * Helper functions
 */

export function isHTTPSRequest(request: NextRequest): boolean {
  return request.nextUrl.protocol === 'https:' || 
         request.headers.get('x-forwarded-proto') === 'https';
}

export function getSecurityHeaders(): Record<string, string> {
  return getHTTPSSecurityHeaders();
}

export function validateHTTPSConfiguration(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check if HTTPS is properly configured
  if (process.env.NODE_ENV === 'production') {
    if (!process.env.NEXTAUTH_URL?.startsWith('https://')) {
      errors.push('NEXTAUTH_URL must use HTTPS in production');
    }

    if (!process.env.NEXT_PUBLIC_SITE_URL?.startsWith('https://')) {
      warnings.push('NEXT_PUBLIC_SITE_URL should use HTTPS in production');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}
