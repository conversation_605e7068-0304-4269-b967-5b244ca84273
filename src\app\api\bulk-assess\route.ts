import { NextRequest, NextResponse } from 'next/server';
import { AIService } from '@/lib/ai/AIService';
import { DatabaseIntegration } from '@/lib/ai/DatabaseIntegration';
import { AssessmentConfig } from '@/types/ai-assessment';

/**
 * Bulk Assessment API Endpoint
 * Processes multiple submissions for AI assessment
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Bulk assessment API called');

    const body = await request.json();
    console.log('📥 Request body:', body);

    // Handle both formats: direct config object or flattened parameters
    let assignmentId, config, maxConcurrent = 5;

    if (body.config) {
      // Format 1: { assignmentId, config: {...} } - from student upload page
      assignmentId = body.assignmentId;
      config = body.config;
      maxConcurrent = body.maxConcurrent || 5;
    } else {
      // Format 2: { assignmentId, aiModel, priority, ... } - from review page
      assignmentId = body.assignmentId;
      config = {
        aiModel: body.aiModel || 'standard',
        priority: body.priority || 'standard',
        feedbackLevel: body.feedbackLevel || 'detailed',
        assessmentType: body.assessmentType || 'comprehensive',
        criteria: body.criteria || [],
        features: body.features || {
          enableDetailedFeedback: true,
          enableSuggestions: true,
          enableConfidenceScoring: true
        }
      };
      maxConcurrent = body.maxConcurrent || 5;
    }

    if (!assignmentId) {
      return NextResponse.json(
        { success: false, message: 'Assignment ID is required' },
        { status: 400 }
      );
    }

    if (!config) {
      return NextResponse.json(
        { success: false, message: 'Assessment configuration is required' },
        { status: 400 }
      );
    }

    console.log(`📋 Starting bulk assessment for assignment: ${assignmentId}`);
    console.log(`⚙️ Config:`, config);

    // Initialize services
    const aiService = new AIService();

    // Initialize database integration
    const { gql } = await import('@apollo/client');
    const { getServerApolloClient } = await import('@/lib/apollo-client');

    const client = getServerApolloClient();
    const dbIntegration = new DatabaseIntegration(client);

    const GET_ASSIGNMENT_SUBMISSIONS = gql`
      query GetAssignmentSubmissions($assignmentId: uuid!) {
        student_submissions(where: { assignment_id: { _eq: $assignmentId } }) {
          id
          student_name
          file_path
          file_type
          upload_date
          assignment {
            id
            title
            description
            assessment_criteria {
              id
              criterion_name
              description
              weight
            }
          }
          assessment_results {
            id
            overall_grade
          }
        }
      }
    `;

    const { data } = await client.query({
      query: GET_ASSIGNMENT_SUBMISSIONS,
      variables: { assignmentId }
    });

    const submissions = data?.student_submissions || [];

    if (!submissions || submissions.length === 0) {
      return NextResponse.json(
        { success: false, message: 'No submissions found for this assignment' },
        { status: 404 }
      );
    }

    // Limit to maximum 20 submissions for safety
    const submissionsToProcess = submissions.slice(0, 20);
    console.log(`📊 Processing ${submissionsToProcess.length} submissions`);

    // Process submissions in batches
    const results: any[] = [];
    const errors: any[] = [];

    for (let i = 0; i < submissionsToProcess.length; i += maxConcurrent) {
      const batch = submissionsToProcess.slice(i, i + maxConcurrent);
      console.log(`🔄 Processing batch ${Math.floor(i / maxConcurrent) + 1}/${Math.ceil(submissionsToProcess.length / maxConcurrent)}`);

      // Process batch in parallel
      const batchPromises = batch.map(async (submission: any) => {
        try {
          console.log(`🤖 Assessing submission: ${submission.id} (${submission.student_name})`);

          // Format submission for AI service
          const formattedSubmission = {
            id: submission.id,
            studentName: submission.student_name,
            fileName: submission.file_path?.split('/').pop() || 'unknown',
            fileType: submission.file_type,
            content: `[Real File Content for Assessment]

Student: ${submission.student_name}
File: ${submission.file_path}
Type: ${submission.file_type}

Note: This represents the actual uploaded file content that would be processed by the AI assessment system.
The file was uploaded on ${new Date(submission.upload_date).toLocaleDateString()} and is ready for evaluation.`,
            uploadDate: new Date(submission.upload_date),
            assignment: {
              id: submission.assignment?.id || '',
              title: submission.assignment?.title || '',
              description: submission.assignment?.description || ''
            },
            criteria: submission.assignment?.assessment_criteria?.map((criterion: any) => ({
              id: criterion.id,
              name: criterion.criterion_name,
              description: criterion.description,
              weight: criterion.weight,
              maxScore: 100
            })) || []
          };

          // Add assignment ID to config for proper linking
          const configWithAssignment = {
            ...config,
            submissionId: submission.id,
            assignmentId: assignmentId,
            criteria: formattedSubmission.criteria
          };

          const assessmentResult = await aiService.assessSubmission(formattedSubmission, configWithAssignment);

          // Create criteria mapping using the proper template-based method
          let criteriaMapping = {};
          try {
            criteriaMapping = await dbIntegration.createCriteriaMappingFromTemplate(
              assignmentId,
              formattedSubmission.criteria || []
            );
            console.log(`✅ Criteria mapping created for ${submission.student_name}:`, criteriaMapping);
          } catch (mappingError) {
            console.error(`❌ Failed to create criteria mapping for ${submission.student_name}:`, mappingError);
            console.log('⚠️ Proceeding with empty mapping - per-criteria feedback will be skipped');
          }

          // Save result to database using DatabaseIntegration (includes per-criteria feedback)
          await dbIntegration.saveAssessmentResults(
            submission.id,
            assessmentResult,
            criteriaMapping
          );

          console.log(`✅ Completed assessment for: ${submission.student_name}`);
          return {
            submissionId: submission.id,
            studentName: submission.student_name,
            success: true,
            overallScore: assessmentResult.overallScore,
            overallGrade: assessmentResult.overallGrade
          };

        } catch (error) {
          console.error(`❌ Failed to assess submission ${submission.id}:`, error);
          return {
            submissionId: submission.id,
            studentName: submission.student_name,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          };
        }
      });

      // Wait for batch to complete
      const batchResults = await Promise.all(batchPromises);

      // Separate successful and failed results
      batchResults.forEach(result => {
        if (result.success) {
          results.push(result);
        } else {
          errors.push(result);
        }
      });

      // Small delay between batches to avoid overwhelming the AI APIs
      if (i + maxConcurrent < submissionsToProcess.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    console.log(`🎯 Bulk assessment complete: ${results.length} successful, ${errors.length} failed`);

    return NextResponse.json({
      success: true,
      message: `Bulk assessment completed`,
      results: {
        total: submissionsToProcess.length,
        successful: results.length,
        failed: errors.length,
        successfulAssessments: results,
        failedAssessments: errors
      },
      metadata: {
        assignmentId,
        processedAt: new Date().toISOString(),
        processingTimeMs: Date.now() // This would need to be calculated properly
      }
    });

  } catch (error) {
    console.error('❌ Bulk assessment API error:', error);

    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error during bulk assessment',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Get bulk assessment status
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const assignmentId = searchParams.get('assignmentId');

    if (!assignmentId) {
      return NextResponse.json(
        { success: false, message: 'Assignment ID is required' },
        { status: 400 }
      );
    }

    // Get GraphQL client for database operations
    const { getServerApolloClient } = await import('@/lib/apollo-client');
    const client = getServerApolloClient();

    const dbIntegration = new DatabaseIntegration(client);
    const submissions = await dbIntegration.getSubmissionsByAssignment(assignmentId);

    const assessmentStatus = {
      total: submissions.length,
      assessed: submissions.filter((s: any) => s.assessment_results != null).length,
      pending: submissions.filter((s: any) => s.assessment_results == null).length
    };

    return NextResponse.json({
      success: true,
      assignmentId,
      status: assessmentStatus
    });

  } catch (error) {
    console.error('❌ Bulk assessment status error:', error);

    return NextResponse.json(
      {
        success: false,
        message: 'Failed to get bulk assessment status',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
