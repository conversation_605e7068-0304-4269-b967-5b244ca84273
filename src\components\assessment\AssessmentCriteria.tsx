import { useState } from 'react';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';

interface Criterion {
  id: string;
  name: string;
  description: string;
  weight: number;
}

interface Criteria {
  templateId: string;
  templateName: string;
  criteria: Criterion[];
  weight: number;
}

interface AssessmentCriteriaProps {
  criteria: Criteria | null;
  onUpdate: (criteria: Criteria) => void;
}

export default function AssessmentCriteria({ criteria, onUpdate }: AssessmentCriteriaProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!criteria) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-text mb-4">Assessment Criteria</h2>
        <div className="text-center py-8">
          <p className="text-gray-500">No criteria configured. Please go back to configuration.</p>
        </div>
      </div>
    );
  }

  const totalWeight = criteria.criteria?.reduce((sum, criterion) => sum + criterion.weight, 0) || 0;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 className="text-xl font-semibold text-text mb-4">Assessment Criteria</h2>

      <div className="space-y-4">
        {/* Using Template */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Using Template
          </label>
          <div className="text-gray-900 font-medium">
            {criteria.templateName}
          </div>
        </div>

        {/* Criteria List */}
        <div className="space-y-3">
          {criteria.criteria?.map((criterion, index) => (
            <div key={criterion.id} className="border border-gray-200 rounded-lg">
              <div
                className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={() => setIsExpanded(!isExpanded)}
              >
                <div className="flex items-center space-x-3">
                  <div className="text-sm font-medium text-gray-900">
                    {criterion.name}
                  </div>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                    {criterion.weight}%
                  </span>
                </div>
                {isExpanded ? (
                  <ChevronUpIcon className="w-5 h-5 text-gray-400" />
                ) : (
                  <ChevronDownIcon className="w-5 h-5 text-gray-400" />
                )}
              </div>

              {isExpanded && (
                <div className="px-4 pb-4 border-t border-gray-200">
                  <div className="pt-3">
                    <p className="text-sm text-gray-600">
                      {criterion.description}
                    </p>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Weight Summary */}
        <div className={`p-3 rounded-lg border ${
          totalWeight === 100
            ? 'bg-green-50 border-green-200'
            : 'bg-orange-50 border-orange-200'
        }`}>
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">Total Weight:</span>
            <span className={`font-bold ${
              totalWeight === 100 ? 'text-green-600' : 'text-orange-600'
            }`}>
              {totalWeight}%
            </span>
          </div>
        </div>

        {/* Change Template Button */}
        <div className="flex justify-end">
          <button
            onClick={() => {
              window.history.back(); // Go back to configuration
            }}
            className="text-sm text-accent hover:text-[#9A7649] font-medium transition-colors"
          >
            Change Template
          </button>
        </div>
      </div>
    </div>
  );
}
