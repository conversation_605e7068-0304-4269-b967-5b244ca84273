import { NextRequest, NextResponse } from 'next/server';
import { getServerApolloClient } from '@/lib/apollo-client';
import { gql } from '@apollo/client';

// GraphQL query using admin client (bypasses allowlist)
const GET_ASSIGNMENT_SUBMISSIONS_ADMIN = gql`
  query GetAssignmentSubmissionsAdmin($assignment_id: uuid!) {
    student_submissions(
      where: { assignment_id: { _eq: $assignment_id } }
      order_by: { upload_date: desc }
    ) {
      id
      student_name
      file_path
      file_type
      processing_status
      upload_date
      assignment_id
    }
  }
`;

// GraphQL query for single submission using admin client
const GET_SUBMISSION_BY_ID_ADMIN = gql`
  query GetSubmissionByIdAdmin($submission_id: uuid!) {
    student_submissions_by_pk(id: $submission_id) {
      id
      student_name
      file_path
      file_type
      processing_status
      upload_date
      assignment_id
      assignment {
        id
        title
        description
      }
    }
  }
`;

// GraphQL mutation for creating submissions using admin client
const CREATE_SUBMISSION_ADMIN = gql`
  mutation CreateSubmissionAdmin(
    $assignment_id: uuid!
    $student_name: String!
    $processing_status: String!
    $file_path: String!
    $file_type: String!
    $upload_date: timestamptz!
  ) {
    insert_student_submissions_one(object: {
      assignment_id: $assignment_id
      student_name: $student_name
      processing_status: $processing_status
      file_path: $file_path
      file_type: $file_type
      upload_date: $upload_date
    }) {
      id
      student_name
      file_path
      file_type
      processing_status
      upload_date
      assignment_id
    }
  }
`;

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const assignmentId = searchParams.get('assignment_id');
    const submissionId = searchParams.get('submissionId');

    if (!assignmentId && !submissionId) {
      return NextResponse.json(
        { error: 'Either assignment_id or submissionId parameter is required' },
        { status: 400 }
      );
    }

    // Use server-side Apollo client with admin secret
    const apolloClient = getServerApolloClient();

    if (submissionId) {
      // Get single submission by ID
      console.log('🔍 Fetching submission by ID:', submissionId);

      const result = await apolloClient.query({
        query: GET_SUBMISSION_BY_ID_ADMIN,
        variables: {
          submission_id: submissionId,
        },
      });

      console.log('✅ Successfully fetched submission:', result.data.student_submissions_by_pk?.id);

      return NextResponse.json({
        submission: result.data.student_submissions_by_pk,
        count: result.data.student_submissions_by_pk ? 1 : 0
      });

    } else if (assignmentId) {
      // Get submissions for assignment
      console.log('🔍 Fetching submissions for assignment:', assignmentId);

      const result = await apolloClient.query({
        query: GET_ASSIGNMENT_SUBMISSIONS_ADMIN,
        variables: {
          assignment_id: assignmentId,
        },
      });

      console.log('✅ Successfully fetched submissions:', result.data.student_submissions.length);

      return NextResponse.json({
        submissions: result.data.student_submissions,
        count: result.data.student_submissions.length
      });
    }

  } catch (error: any) {
    console.error('❌ API error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { assignment_id, student_name, status, file_path, file_type, upload_date } = body;

    // Validate required fields
    if (!assignment_id || !student_name) {
      return NextResponse.json(
        { error: 'assignment_id and student_name are required' },
        { status: 400 }
      );
    }

    // Ensure required fields have valid values
    const finalFileType = file_type || 'document';
    const finalFilePath = file_path || `unknown_${Date.now()}`;
    const finalUploadDate = upload_date || new Date().toISOString();
    const finalProcessingStatus = status || 'submitted';

    console.log('📝 Creating submission via API:', {
      assignment_id,
      student_name,
      processing_status: finalProcessingStatus,
      file_path: finalFilePath,
      file_type: finalFileType,
      upload_date: finalUploadDate
    });

    // Use server-side Apollo client with admin secret
    const apolloClient = getServerApolloClient();

    const result = await apolloClient.mutate({
      mutation: CREATE_SUBMISSION_ADMIN,
      variables: {
        assignment_id,
        student_name,
        processing_status: finalProcessingStatus,
        file_path: finalFilePath,
        file_type: finalFileType,
        upload_date: finalUploadDate
      },
    });

    console.log('✅ Submission created successfully:', result.data.insert_student_submissions_one);

    return NextResponse.json({
      submission: result.data.insert_student_submissions_one,
      success: true
    });

  } catch (error: any) {
    console.error('❌ Submission creation error:', error);
    return NextResponse.json(
      {
        error: 'Failed to create submission',
        details: process.env.NODE_ENV === 'development' ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
