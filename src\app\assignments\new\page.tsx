'use client';

import { useEffect, useState } from 'react';
import { useAuthenticationStatus, useUserData } from '@nhost/nextjs';
import { useRouter } from 'next/navigation';
import { useQuery, gql } from '@apollo/client';
import DashboardLayout from '@/components/layout/DashboardLayout';
import AssignmentForm from '@/components/assignments/AssignmentForm';

// GraphQL query to fetch user's courses
const GET_USER_COURSES = gql`
  query GetUserCourses {
    courses(where: {
      user_id: { _eq: "X-Hasura-User-Id" }
    }) {
      id
      name
    }
  }
`;

export default function NewAssignmentPage() {
  const { isAuthenticated, isLoading } = useAuthenticationStatus();
  const user = useUserData();
  const router = useRouter();
  const [courses, setCourses] = useState([]);

  // Fetch user's courses
  const { data, loading: coursesLoading } = useQuery(GET_USER_COURSES, {
    skip: !isAuthenticated,
  });

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/sign-in');
    }

    if (data?.courses) {
      const formattedCourses = data.courses.map((course: any) => ({
        value: course.id,
        label: course.name,
      }));
      setCourses(formattedCourses);
    }
  }, [isLoading, isAuthenticated, router, data]);

  if (isLoading || coursesLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[50vh]">
          <p>Loading...</p>
        </div>
      </DashboardLayout>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect in the useEffect
  }

  return (
    <DashboardLayout>
      <div className="mb-8">
        <h1 className="text-3xl font-light mb-2">Create New Assignment</h1>
        <p className="text-gray-600">
          Create a new assignment for your students to complete.
        </p>
      </div>

      <div className="max-w-3xl">
        <AssignmentForm />
      </div>
    </DashboardLayout>
  );
}
