// Simple test script to verify the assessment API is working
const testAssessmentAPI = async () => {
  const testConfig = {
    submissionId: "931e7f18-6af3-4c8f-9e96-94abd771fa0f", // Real submission ID from the logs
    aiModel: "standard",
    priority: "standard",
    feedbackLevel: "standard",
    assessmentType: "letter",
    criteria: [
      {
        id: "content",
        name: "Content Quality",
        description: "Quality of content and ideas",
        weight: 50
      },
      {
        id: "grammar",
        name: "Grammar & Style",
        description: "Grammar, spelling, and writing style",
        weight: 50
      }
    ],
    features: {
      plagiarismCheck: false,
      grammarCheck: true,
      contentAnalysis: true,
      citationCheck: false,
      codeAnalysis: false
    }
  };

  try {
    console.log('🚀 Testing assessment API...');
    
    const response = await fetch('http://localhost:3002/api/assess-submission', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        submissionId: testConfig.submissionId,
        config: testConfig
      })
    });

    console.log('📊 Response status:', response.status);
    console.log('📊 Response headers:', Object.fromEntries(response.headers.entries()));

    const data = await response.json();
    console.log('📊 Response data:', data);

    if (response.ok) {
      console.log('✅ Assessment API is working!');
    } else {
      console.log('❌ Assessment API failed:', data);
    }

  } catch (error) {
    console.error('❌ Error testing assessment API:', error);
  }
};

// Run the test
testAssessmentAPI();
