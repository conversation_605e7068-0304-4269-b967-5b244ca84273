import { NextRequest, NextResponse } from 'next/server';
import { AIService } from '@/lib/ai/AIService';
import { DatabaseIntegration } from '@/lib/ai/DatabaseIntegration';
import { AssessmentConfig, AssessmentResult, Submission } from '@/types/ai-assessment';
import { getServerApolloClient } from '@/lib/apollo-client';
import { validateSubmissionId, validateAssessmentConfig, ValidationError } from '@/lib/middleware/validation';

// Main POST handler (no security middleware to avoid CORS issues)
export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Assessment API called');
    console.log('🔧 Environment check:');
    console.log('- OPENAI_API_KEY:', process.env.OPENAI_API_KEY ? `SET (${process.env.OPENAI_API_KEY.substring(0, 10)}...)` : 'MISSING');
    console.log('- ANTHROPIC_API_KEY:', process.env.ANTHROPIC_API_KEY ? `SET (${process.env.ANTHROPIC_API_KEY.substring(0, 10)}...)` : 'MISSING');
    console.log('- NODE_ENV:', process.env.NODE_ENV);
    console.log('- All env vars starting with OPENAI:', Object.keys(process.env).filter(key => key.includes('OPENAI')));
    console.log('- All env vars starting with ANTHROPIC:', Object.keys(process.env).filter(key => key.includes('ANTHROPIC')));
    console.log('- Total env vars count:', Object.keys(process.env).length);

    // Parse request body
    let body;
    try {
      body = await request.json();
    } catch (parseError) {
      console.error('❌ Failed to parse request body:', parseError);
      return NextResponse.json(
        { success: false, error: 'Invalid JSON in request body' },
        { status: 400 }
      );
    }

    const { submissionId, config } = body;
    console.log('📋 Received data:', { submissionId, configKeys: Object.keys(config || {}) });

    // Validate and sanitize inputs
    let validatedSubmissionId: string;
    let validatedConfig: any;

    try {
      validatedSubmissionId = validateSubmissionId(submissionId);
      validatedConfig = validateAssessmentConfig(config);
    } catch (error) {
      if (error instanceof ValidationError) {
        console.error('❌ Validation failed:', error.message);
        return NextResponse.json(
          {
            success: false,
            error: 'Validation failed',
            message: error.message,
            field: error.field
          },
          { status: 400 }
        );
      }
      throw error;
    }

    console.log(`📄 Processing assessment for submission: ${validatedSubmissionId}`);

    // Check for API keys - if missing, provide clear instructions
    if (!process.env.OPENAI_API_KEY && !process.env.ANTHROPIC_API_KEY) {
      console.error('❌ No AI API keys found');
      console.error('Available env vars:', Object.keys(process.env).sort());
      return NextResponse.json(
        {
          success: false,
          error: 'AI API keys not configured. Please add OPENAI_API_KEY or ANTHROPIC_API_KEY to your environment variables.',
          debug: {
            availableEnvVars: Object.keys(process.env).filter(key => key.includes('API') || key.includes('KEY')),
            nodeEnv: process.env.NODE_ENV
          }
        },
        { status: 500 }
      );
    }

    // Create database integration with server-side Apollo Client
    const serverApolloClient = getServerApolloClient();
    const dbIntegration = new DatabaseIntegration(serverApolloClient);

    // Get submission data from database
    console.log(`📄 Fetching submission data: ${validatedSubmissionId}`);
    const submissionData = await dbIntegration.getSubmissionForAssessment(validatedSubmissionId);

    // Validate configuration (additional AI-specific validation)
    console.log('🔍 Validating AI configuration...');
    try {
      AIService.validateConfig(validatedConfig);
      console.log('✅ AI Configuration validation passed');
    } catch (validationError: any) {
      console.error('❌ AI Configuration validation failed:', validationError.message);
      return NextResponse.json(
        { success: false, error: `AI Configuration invalid: ${validationError.message}` },
        { status: 400 }
      );
    }

    // Create AI service instance
    const aiService = new AIService();

    // Process the assessment with REAL AI
    console.log(`🤖 Processing REAL AI assessment for submission: ${validatedSubmissionId}`);
    const startTime = Date.now();

    const result: AssessmentResult = await aiService.assessSubmission(
      submissionData as Submission,
      validatedConfig as AssessmentConfig
    );

    // Ensure assessment criteria exist in database before saving results
    console.log('🔍 Ensuring assessment criteria exist in database...');
    console.log('📋 Assignment ID:', result.assignmentId);
    console.log('📝 Template criteria count:', validatedConfig.criteria?.length || 0);

    try {
      await dbIntegration.ensureAssessmentCriteria(
        result.assignmentId,
        validatedConfig.criteria || []
      );
      console.log('✅ Assessment criteria ensured successfully');
    } catch (criteriaError) {
      console.error('❌ Failed to ensure assessment criteria:', criteriaError);
      // Continue anyway - we'll try to map what we can
    }

    // Create proper criteria mapping using database IDs
    console.log('🗺️ Creating criteria mapping...');
    let criteriaMapping = {};
    try {
      criteriaMapping = await dbIntegration.createCriteriaMappingFromTemplate(
        result.assignmentId,
        validatedConfig.criteria || []
      );
      console.log('✅ Criteria mapping created successfully:', criteriaMapping);
    } catch (mappingError) {
      console.error('❌ Failed to create criteria mapping:', mappingError);
      console.log('⚠️ Proceeding with empty mapping - per-criteria feedback will be skipped');
    }

    // Save results to database using existing schema
    await dbIntegration.saveAssessmentResults(validatedSubmissionId, result, criteriaMapping);

    const totalTime = Date.now() - startTime;
    console.log(`✅ REAL AI Assessment completed and saved in ${totalTime}ms`);

    // Return successful result
    return NextResponse.json({
      success: true,
      result: {
        id: result.id,
        submissionId: validatedSubmissionId,
        assignmentId: result.assignmentId,
        overallGrade: result.overallGrade,
        overallScore: result.overallScore,
        feedbackSummary: result.feedbackSummary,
        confidenceScore: result.confidenceScore,
        aiProvider: result.aiProvider,
        modelUsed: result.modelUsed
      },
      metadata: {
        processingTimeMs: totalTime,
        timestamp: new Date().toISOString(),
        provider: result.aiProvider,
        model: result.modelUsed,
        tokenUsage: result.tokenUsage
      }
    });



  } catch (error: any) {
    console.error('❌ Assessment API error:', error);

    // Sanitize error messages for production
    const errorMessage = process.env.NODE_ENV === 'development'
      ? error.message || 'Assessment processing failed'
      : 'Assessment processing failed';

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
