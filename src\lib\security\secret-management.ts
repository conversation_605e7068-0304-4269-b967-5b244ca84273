/**
 * Advanced Secret Management System
 * Provides secure secret handling, encryption, rotation, and validation
 */

export interface SecretConfig {
  name: string;
  required: boolean;
  encrypted: boolean;
  rotationInterval?: number; // in days
  validator?: (value: string) => boolean;
  description?: string;
}

export interface SecretValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  missingSecrets: string[];
  expiredSecrets: string[];
}

// Critical secrets configuration
export const SECRET_CONFIGS: Record<string, SecretConfig> = {
  NHOST_ADMIN_SECRET: {
    name: 'NHOST_ADMIN_SECRET',
    required: true,
    encrypted: true,
    rotationInterval: 90,
    validator: (value) => value.length >= 32,
    description: 'Nhost admin secret for database access'
  },
  OPENAI_API_KEY: {
    name: 'OPENAI_API_KEY',
    required: true,
    encrypted: true,
    rotationInterval: 180,
    validator: (value) => value.startsWith('sk-') && value.length > 20,
    description: 'OpenAI API key for AI assessments'
  },
  ANTHROPIC_API_KEY: {
    name: 'ANTHROPIC_API_KEY',
    required: true,
    encrypted: true,
    rotationInterval: 180,
    validator: (value) => value.startsWith('sk-ant-') && value.length > 20,
    description: 'Anthropic API key for advanced AI assessments'
  },
  NEXTAUTH_SECRET: {
    name: 'NEXTAUTH_SECRET',
    required: true,
    encrypted: true,
    rotationInterval: 30,
    validator: (value) => value.length >= 32,
    description: 'NextAuth secret for session encryption'
  },
  JWT_SECRET: {
    name: 'JWT_SECRET',
    required: false,
    encrypted: true,
    rotationInterval: 30,
    validator: (value) => value.length >= 32,
    description: 'JWT signing secret'
  }
};

// Public configuration (non-sensitive)
export const PUBLIC_CONFIGS: Record<string, SecretConfig> = {
  NEXT_PUBLIC_NHOST_SUBDOMAIN: {
    name: 'NEXT_PUBLIC_NHOST_SUBDOMAIN',
    required: true,
    encrypted: false,
    validator: (value) => /^[a-z0-9]+$/.test(value),
    description: 'Nhost project subdomain'
  },
  NEXT_PUBLIC_NHOST_REGION: {
    name: 'NEXT_PUBLIC_NHOST_REGION',
    required: true,
    encrypted: false,
    validator: (value) => /^[a-z0-9-]+$/.test(value),
    description: 'Nhost project region'
  },
  NODE_ENV: {
    name: 'NODE_ENV',
    required: true,
    encrypted: false,
    validator: (value) => ['development', 'production', 'test'].includes(value),
    description: 'Node.js environment'
  }
};

/**
 * Simple encryption/decryption for secrets (in production, use proper key management)
 */
class SimpleEncryption {
  private key: string;

  constructor() {
    // In production, this should come from a secure key management service
    this.key = process.env.ENCRYPTION_KEY || 'default-key-change-in-production';
  }

  encrypt(text: string): string {
    // Simple XOR encryption (replace with proper encryption in production)
    let result = '';
    for (let i = 0; i < text.length; i++) {
      result += String.fromCharCode(
        text.charCodeAt(i) ^ this.key.charCodeAt(i % this.key.length)
      );
    }
    return Buffer.from(result).toString('base64');
  }

  decrypt(encryptedText: string): string {
    try {
      const text = Buffer.from(encryptedText, 'base64').toString();
      let result = '';
      for (let i = 0; i < text.length; i++) {
        result += String.fromCharCode(
          text.charCodeAt(i) ^ this.key.charCodeAt(i % this.key.length)
        );
      }
      return result;
    } catch (error) {
      throw new Error('Failed to decrypt secret');
    }
  }
}

/**
 * Secret Management System
 */
export class SecretManager {
  private encryption: SimpleEncryption;
  private secretCache: Map<string, { value: string; encrypted: boolean; lastAccessed: Date }>;

  constructor() {
    this.encryption = new SimpleEncryption();
    this.secretCache = new Map();
  }

  /**
   * Get a secret value with automatic decryption
   */
  getSecret(name: string): string | undefined {
    const config = SECRET_CONFIGS[name] || PUBLIC_CONFIGS[name];
    const envValue = process.env[name];

    if (!envValue) {
      return undefined;
    }

    // Check cache first
    const cached = this.secretCache.get(name);
    if (cached) {
      cached.lastAccessed = new Date();
      return cached.value;
    }

    let value = envValue;

    // Decrypt if needed
    if (config?.encrypted && this.isEncrypted(envValue)) {
      try {
        value = this.encryption.decrypt(envValue);
      } catch (error) {
        console.error(`Failed to decrypt secret ${name}:`, error);
        return undefined;
      }
    }

    // Cache the decrypted value
    this.secretCache.set(name, {
      value,
      encrypted: config?.encrypted || false,
      lastAccessed: new Date()
    });

    return value;
  }

  /**
   * Set a secret value with automatic encryption
   */
  setSecret(name: string, value: string): void {
    const config = SECRET_CONFIGS[name] || PUBLIC_CONFIGS[name];

    if (config?.encrypted) {
      // In production, this would update the environment variable in your deployment platform
      console.warn(`Setting encrypted secret ${name} - implement proper secret storage in production`);
      
      // For now, just cache the value
      this.secretCache.set(name, {
        value,
        encrypted: true,
        lastAccessed: new Date()
      });
    } else {
      this.secretCache.set(name, {
        value,
        encrypted: false,
        lastAccessed: new Date()
      });
    }
  }

  /**
   * Validate all secrets
   */
  validateSecrets(): SecretValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const missingSecrets: string[] = [];
    const expiredSecrets: string[] = [];

    // Check required secrets
    for (const [name, config] of Object.entries({ ...SECRET_CONFIGS, ...PUBLIC_CONFIGS })) {
      const value = this.getSecret(name);

      if (config.required && !value) {
        missingSecrets.push(name);
        errors.push(`Required secret ${name} is missing`);
        continue;
      }

      if (value && config.validator && !config.validator(value)) {
        errors.push(`Secret ${name} failed validation`);
      }

      // Check for rotation needs (simplified - in production, track actual rotation dates)
      if (config.rotationInterval && config.encrypted) {
        // This is a simplified check - in production, you'd track actual rotation dates
        warnings.push(`Secret ${name} should be rotated every ${config.rotationInterval} days`);
      }
    }

    // Check for common secret patterns in environment
    this.checkForExposedSecrets(errors, warnings);

    return {
      isValid: errors.length === 0 && missingSecrets.length === 0,
      errors,
      warnings,
      missingSecrets,
      expiredSecrets
    };
  }

  /**
   * Generate a secure random secret
   */
  generateSecret(length: number = 32): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let result = '';
    
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return result;
  }

  /**
   * Check if a value appears to be encrypted
   */
  private isEncrypted(value: string): boolean {
    // Simple check - in production, use proper encryption markers
    try {
      Buffer.from(value, 'base64');
      return !value.includes(' ') && value.length > 20;
    } catch {
      return false;
    }
  }

  /**
   * Check for exposed secrets in environment
   */
  private checkForExposedSecrets(errors: string[], warnings: string[]): void {
    // Check for common secret patterns
    const secretPatterns = [
      { pattern: /sk-[a-zA-Z0-9]{20,}/, name: 'OpenAI API Key' },
      { pattern: /sk-ant-[a-zA-Z0-9]{20,}/, name: 'Anthropic API Key' },
      { pattern: /ghp_[a-zA-Z0-9]{36}/, name: 'GitHub Personal Access Token' },
      { pattern: /glpat-[a-zA-Z0-9]{20}/, name: 'GitLab Personal Access Token' },
      { pattern: /xoxb-[a-zA-Z0-9-]{50,}/, name: 'Slack Bot Token' }
    ];

    // In a real implementation, you'd scan your codebase and logs
    // For now, just check environment variables
    for (const [key, value] of Object.entries(process.env)) {
      if (typeof value === 'string') {
        for (const { pattern, name } of secretPatterns) {
          if (pattern.test(value) && !SECRET_CONFIGS[key]) {
            warnings.push(`Potential ${name} found in environment variable ${key}`);
          }
        }
      }
    }
  }

  /**
   * Get secret usage statistics
   */
  getSecretStats(): {
    totalSecrets: number;
    encryptedSecrets: number;
    missingRequired: number;
    lastValidation: Date;
  } {
    const validation = this.validateSecrets();
    
    return {
      totalSecrets: Object.keys({ ...SECRET_CONFIGS, ...PUBLIC_CONFIGS }).length,
      encryptedSecrets: Object.values(SECRET_CONFIGS).filter(c => c.encrypted).length,
      missingRequired: validation.missingSecrets.length,
      lastValidation: new Date()
    };
  }

  /**
   * Clear secret cache (for security)
   */
  clearCache(): void {
    this.secretCache.clear();
  }
}

// Global secret manager instance
export const secretManager = new SecretManager();

/**
 * Helper functions for common secret operations
 */

export function getRequiredSecret(name: string): string {
  const value = secretManager.getSecret(name);
  if (!value) {
    throw new Error(`Required secret ${name} is not available`);
  }
  return value;
}

export function getOptionalSecret(name: string, defaultValue?: string): string | undefined {
  return secretManager.getSecret(name) || defaultValue;
}

export function validateEnvironment(): SecretValidationResult {
  return secretManager.validateSecrets();
}

export function isProductionEnvironment(): boolean {
  return secretManager.getSecret('NODE_ENV') === 'production';
}

export function isDevelopmentEnvironment(): boolean {
  return secretManager.getSecret('NODE_ENV') === 'development';
}

/**
 * Environment validation middleware
 */
export function validateEnvironmentMiddleware() {
  const validation = validateEnvironment();
  
  if (!validation.isValid) {
    console.error('🚨 Environment validation failed:', validation.errors);
    
    if (isProductionEnvironment()) {
      throw new Error('Production environment validation failed - deployment aborted');
    }
  }
  
  if (validation.warnings.length > 0) {
    console.warn('⚠️ Environment warnings:', validation.warnings);
  }
  
  return validation;
}
