'use client';

import React, { createContext, useContext, useState } from 'react';

// Simple test context to isolate the issue
const TestContext = createContext<{ value: string; setValue: (v: string) => void } | null>(null);

function TestProvider({ children }: { children: React.ReactNode }) {
  const [value, setValue] = useState('test');
  
  return (
    <TestContext.Provider value={{ value, setValue }}>
      {children}
    </TestContext.Provider>
  );
}

function TestComponent() {
  const context = useContext(TestContext);
  
  if (!context) {
    return <div>Context not available</div>;
  }
  
  return (
    <div className="p-4">
      <h1>Context Test</h1>
      <p>Value: {context.value}</p>
      <button 
        onClick={() => context.setValue('updated')}
        className="bg-blue-500 text-white px-4 py-2 rounded"
      >
        Update Value
      </button>
    </div>
  );
}

export default function TestContextPage() {
  return (
    <TestProvider>
      <TestComponent />
    </TestProvider>
  );
}
