'use client';

import { useState, useCallback, useEffect, useRef } from 'react';
import { useDropzone } from 'react-dropzone';
import { useMutation, gql } from '@apollo/client';
import { useAuthenticationStatus, useFileUpload, useUserData, useAccessToken } from '@nhost/nextjs';
import { useRouter, useSearchParams } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Upload, FileText, Trash2, CheckCircle, Clock, AlertCircle } from 'lucide-react';
import { nhost } from '@/lib/nhost';
// Removed complex filtering - using simple assignment ID filtering only
import { CREATE_SUBMISSION, DELETE_SUBMISSION } from '@/lib/gql/submission-queries';
import AssignmentResetButton from '@/components/assignments/AssignmentResetButton';


// Using StudentSubmission interface from submission-filters.ts

// Helper function to extract student name from filename
const extractStudentName = (filename: string): string => {
  // Remove file extension
  const nameWithoutExt = filename.replace(/\.[^/.]+$/, '');
  // Split by underscore or dash and take first part
  const parts = nameWithoutExt.split(/[_-]/);
  // Clean up the name (remove numbers, special chars)
  const cleanName = parts[0].replace(/[^a-zA-Z\s]/g, '').trim();
  return cleanName || 'Unknown Student';
};

// Remove direct GraphQL query - will use API route instead

export default function StudentUploadPage() {
  const { isAuthenticated, isLoading } = useAuthenticationStatus();
  const user = useUserData();
  const accessToken = useAccessToken();
  const router = useRouter();
  const searchParams = useSearchParams();
  const assignmentFromUrl = searchParams.get('assignment');

  const [assignmentId, setAssignmentId] = useState<string>(assignmentFromUrl || '');
  const [studentName, setStudentName] = useState<string>('');
  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: number }>({});
  const [csvFile, setCsvFile] = useState<File | null>(null);

  // State for assignments data (using API route instead of GraphQL)
  const [assignmentsData, setAssignmentsData] = useState<any>(null);
  const [assignmentsLoading, setAssignmentsLoading] = useState(true);
  const [assignmentsError, setAssignmentsError] = useState<string | null>(null);

  // State for submissions data (using API route instead of GraphQL)
  // Simplified state management with ref backup
  const [submissionsData, setSubmissionsData] = useState({
    items: [] as any[],
    loading: false,
    error: null as string | null,
    lastFetch: 0
  });

  // Ref to track submissions for debugging
  const submissionsRef = useRef<any[]>([]);
  const [forceUpdate, setForceUpdate] = useState(0);

  // Bulk processing state
  const [isBulkMode, setIsBulkMode] = useState<boolean>(false);
  const [bulkFiles, setBulkFiles] = useState<File[]>([]);
  const [bulkProgress, setBulkProgress] = useState<{
    total: number;
    completed: number;
    failed: number;
    processing: boolean;
  }>({ total: 0, completed: 0, failed: 0, processing: false });

  // Fetch assignments using API route (bypasses allowlist)
  useEffect(() => {
    if (!isAuthenticated || !user?.id || !accessToken) {
      setAssignmentsLoading(false);
      return;
    }

    const fetchAssignments = async () => {
      try {
        setAssignmentsLoading(true);
        setAssignmentsError(null);

        console.log('🔍 Fetching assignments via API route...');

        const response = await fetch('/api/assignments', {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        });
        const result = await response.json();

        if (response.ok && result.success) {
          setAssignmentsData({ assignments: result.assignments });
          console.log('✅ Assignments fetched successfully:', result.assignments.length);
        } else {
          throw new Error(result.error || 'Failed to fetch assignments');
        }
      } catch (err) {
        console.error('❌ Failed to fetch assignments:', err);
        setAssignmentsError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setAssignmentsLoading(false);
      }
    };

    fetchAssignments();
  }, [isAuthenticated, user?.id, accessToken]);

  // Update assignment ID when URL parameter changes
  useEffect(() => {
    if (assignmentFromUrl && assignmentFromUrl !== assignmentId) {
      setAssignmentId(assignmentFromUrl);
    }
  }, [assignmentFromUrl, assignmentId]);

  // Component mounted and authenticated
  useEffect(() => {
    if (isAuthenticated) {
      console.log('Student upload page ready');
    }
  }, [isAuthenticated]);

  // Simplified fetch function
  const loadSubmissions = async (forceRefresh = false) => {
    if (!assignmentId) {
      console.log('⚠️ No assignment ID provided');
      setSubmissionsData({ items: [], loading: false, error: null, lastFetch: 0 });
      return;
    }

    const now = Date.now();
    console.log('🔍 Loading submissions for assignment:', assignmentId);

    setSubmissionsData(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await fetch(`/api/submissions?assignment_id=${assignmentId}&t=${now}`);
      const result = await response.json();

      if (response.ok) {
        const items = result.submissions || [];
        console.log('✅ Loaded submissions:', items.length);

        // Log each submission for debugging
        items.forEach((sub: any, i: number) => {
          console.log(`  ${i + 1}. ${sub.student_name} - ${sub.file_path}`);
        });

        // Update both state and ref
        submissionsRef.current = items;
        console.log('🔄 Updated ref with:', submissionsRef.current.length, 'items');

        setSubmissionsData({
          items: items,
          loading: false,
          error: null,
          lastFetch: now
        });

        // Force re-render
        setForceUpdate(prev => prev + 1);
        console.log('🔄 Forced update triggered');
      } else {
        throw new Error(result.error || 'Failed to load submissions');
      }
    } catch (error) {
      console.error('❌ Error loading submissions:', error);
      setSubmissionsData(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }));
    }
  };

  // Load submissions when assignment changes
  useEffect(() => {
    if (assignmentId) {
      loadSubmissions();
    }
  }, [assignmentId]);



  console.log(`📊 Submissions for assignment ${assignmentId}:`, submissionsData.items.length);

  const [createSubmission] = useMutation(CREATE_SUBMISSION, {
    onCompleted: (data) => {
      console.log('Submission created successfully:', data);
      console.log('Refetching submissions for immediate button update...');

      // Immediate refetch to update button count
      loadSubmissions(true);

      // Additional refetch after short delay to ensure data consistency
      setTimeout(() => {
        console.log('Secondary refetch for data consistency...');
        loadSubmissions(true);
      }, 500);
    },
    onError: (error) => {
      console.error('Submission creation failed:', error);
    }
  });



  const [deleteSubmission] = useMutation(DELETE_SUBMISSION, {
    onCompleted: () => {
      console.log('Submission deleted successfully, refetching...');
      loadSubmissions(true);
    }
  });

  // Try using Nhost hook for upload as alternative
  const { upload: nhostUpload, isUploaded, isUploading, progress: nhostProgress, error: nhostError } = useFileUpload();

  // Display only the most recent submission
  const mostRecentSubmission = submissionsData.items.length > 0 ? submissionsData.items[0] : null;



  // Bulk upload handler for multiple student submissions
  const handleBulkUpload = useCallback(async (files: File[]) => {
    if (!assignmentId) {
      alert('Please select an assignment first');
      return;
    }

    if (files.length === 0) {
      alert('Please select files to upload');
      return;
    }

    if (files.length > 20) {
      alert('Maximum 20 files allowed for bulk upload');
      return;
    }

    console.log(`🚀 Starting bulk upload for ${files.length} files`);

    setBulkProgress({
      total: files.length,
      completed: 0,
      failed: 0,
      processing: true
    });

    let completed = 0;
    let failed = 0;

    // Process files in batches of 5 to avoid overwhelming the system
    const batchSize = 5;
    for (let i = 0; i < files.length; i += batchSize) {
      const batch = files.slice(i, i + batchSize);

      // Process batch in parallel
      const batchPromises = batch.map(async (file, batchIndex) => {
        const globalIndex = i + batchIndex;
        try {
          console.log(`📁 Processing file ${globalIndex + 1}/${files.length}: ${file.name}`);

          // Upload file using API route
          const formData = new FormData();
          formData.append('file', file);
          formData.append('bucketId', 'default');

          const uploadResponse = await fetch('/api/upload', {
            method: 'POST',
            body: formData,
          });

          if (!uploadResponse.ok) {
            const errorData = await uploadResponse.text();
            throw new Error(`Upload failed: ${errorData}`);
          }

          const uploadResult = await uploadResponse.json();

          // Create submission record with extracted student name using API route
          const extractedName = extractStudentName(file.name);
          console.log('📝 Creating bulk submission record for:', extractedName);

          try {
            const submissionData = {
              assignment_id: assignmentId,
              student_name: extractedName,
              status: 'submitted',
              file_path: uploadResult.name,
              file_type: getFileType(file),
              upload_date: new Date().toISOString()
            };

            const submissionResponse = await fetch('/api/submissions', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(submissionData),
            });

            if (!submissionResponse.ok) {
              const errorData = await submissionResponse.text();
              throw new Error(`Submission creation failed: ${errorData}`);
            }

            const submissionResult = await submissionResponse.json();
            console.log('✅ Bulk submission record created for:', extractedName, submissionResult);
          } catch (submissionError) {
            console.error('❌ Bulk submission creation failed for:', extractedName, submissionError);
            throw submissionError;
          }

          console.log(`✅ Successfully processed: ${file.name}`);
          return { success: true, fileName: file.name };

        } catch (error) {
          console.error(`❌ Failed to process ${file.name}:`, error);
          return { success: false, fileName: file.name, error };
        }
      });

      // Wait for batch to complete
      const batchResults = await Promise.all(batchPromises);

      // Update progress
      batchResults.forEach(result => {
        if (result.success) {
          completed++;
        } else {
          failed++;
        }
      });

      setBulkProgress(prev => ({
        ...prev,
        completed,
        failed
      }));
    }

    setBulkProgress(prev => ({
      ...prev,
      processing: false
    }));

    console.log(`🎯 Bulk upload complete: ${completed} successful, ${failed} failed`);

    // Refresh the submissions list
    loadSubmissions(true);

    // Show completion message
    if (failed === 0) {
      alert(`✅ Successfully uploaded all ${completed} files!`);
    } else {
      alert(`⚠️ Upload complete: ${completed} successful, ${failed} failed. Check console for details.`);
    }
  }, [assignmentId, nhostUpload, createSubmission, loadSubmissions]);

  // File upload handler (single mode)
  const handleFileUpload = useCallback(async (files: File[]) => {
    if (!assignmentId) {
      alert('Please select an assignment first');
      return;
    }

    if (!studentName.trim()) {
      alert('Please enter a student name');
      return;
    }

    // Check authentication
    if (isLoading) {
      alert('Authentication is still loading. Please wait a moment and try again.');
      return;
    }

    if (!isAuthenticated) {
      alert('You are not authenticated. Please sign in and try again.');
      return;
    }

    try {
      console.log('Starting file upload...', { assignmentId, fileCount: files.length });

      setUploadProgress(prev => ({ ...prev, 'batch': 0 }));

      // Upload files one by one and create individual submission records
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        console.log(`Uploading file ${i + 1}/${files.length}:`, file.name);
        setUploadProgress(prev => ({ ...prev, 'batch': (i / files.length) * 80 }));

        // Use API route for file upload (bypasses auth issues)
        const formData = new FormData();
        formData.append('file', file);
        formData.append('bucketId', 'default');

        console.log('🚀 Sending upload request...');
        const uploadResponse = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });

        console.log('📡 Upload response status:', uploadResponse.status);

        if (!uploadResponse.ok) {
          const errorData = await uploadResponse.text();
          console.error('❌ Upload failed with error:', errorData);
          throw new Error(`Upload failed: ${errorData}`);
        }

        const uploadResult = await uploadResponse.json();
        console.log('✅ Upload succeeded!', uploadResult);
        console.log('🔍 Upload result name:', uploadResult.name);
        console.log('🔍 Upload result keys:', Object.keys(uploadResult));

        // Create submission record for this file using API route
        console.log('📝 Creating submission record via API...');
        const submissionData = {
          assignment_id: assignmentId,
          student_name: studentName.trim(),
          status: 'submitted',
          file_path: uploadResult.name,
          file_type: getFileType(file),
          upload_date: new Date().toISOString()
        };


        try {
          const submissionResponse = await fetch('/api/submissions', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(submissionData),
          });

          if (!submissionResponse.ok) {
            const errorData = await submissionResponse.text();
            throw new Error(`Submission creation failed: ${errorData}`);
          }

          const submissionResult = await submissionResponse.json();
        } catch (submissionError) {
          console.error('❌ Submission creation failed:', submissionError);
          throw submissionError; // Re-throw to be caught by outer try-catch
        }
      }

      setUploadProgress(prev => ({ ...prev, 'batch': 100 }));

      // Refresh submissions with multiple attempts
      console.log('🔄 Refreshing submissions data...');
      await loadSubmissions(true);

      // Additional refetch after delay to ensure data consistency
      setTimeout(async () => {
        console.log('🔄 Secondary refetch...');
        await loadSubmissions(true);
      }, 1000);

      // Remove progress after 3 seconds
      setTimeout(() => {
        setUploadProgress(prev => {
          const newProgress = { ...prev };
          delete newProgress['batch'];
          return newProgress;
        });
      }, 3000);

      console.log('🎉 Upload process completed successfully!');
      console.log('Triggering refetch to update submissions list...');

      // Force a complete refetch
      await loadSubmissions(true);
      console.log('Refetch completed');

      alert(`✅ Successfully uploaded ${files.length} file(s)!`);

    } catch (error) {
      console.error('Upload failed:', error);
      setUploadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress['batch'];
        return newProgress;
      });
      const errorMessage = error instanceof Error ? error.message : String(error);
      alert(`Upload failed: ${errorMessage}`);
    }
  }, [assignmentId, studentName, createSubmission]);

  // Helper function to determine file type
  const getFileType = (file: File): string => {
    const mimeType = file.type;
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.includes('pdf') || mimeType.includes('document') || mimeType.includes('text')) return 'document';
    if (mimeType.includes('zip') || mimeType.includes('archive')) return 'other';
    return 'other';
  };

  // Dropzone configuration for single mode
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt'],
      'image/png': ['.png'],
      'image/jpeg': ['.jpg', '.jpeg'],
      'application/zip': ['.zip']
    },
    maxSize: 50 * 1024 * 1024, // 50MB
    onDrop: handleFileUpload,
    disabled: !assignmentId || !studentName.trim() || isBulkMode,
  });

  // Bulk dropzone configuration
  const bulkDropzone = useDropzone({
    accept: {
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt'],
      'image/png': ['.png'],
      'image/jpeg': ['.jpg', '.jpeg'],
      'application/zip': ['.zip']
    },
    maxSize: 50 * 1024 * 1024, // 50MB per file
    maxFiles: 20, // Maximum 20 files for bulk upload
    onDrop: handleBulkUpload,
    disabled: !assignmentId || bulkProgress.processing,
  });

  // CSV upload handler
  const handleCSVUpload = useCallback((files: File[]) => {
    const csvFile = files.find(file => file.name.endsWith('.csv'));
    if (csvFile) {
      setCsvFile(csvFile);
      // TODO: Process CSV file for student mappings
    }
  }, []);

  const csvDropzone = useDropzone({
    accept: {
      'text/csv': ['.csv'],
    },
    maxFiles: 1,
    onDrop: handleCSVUpload,
  });

  // Delete submission handler
  const handleDelete = async (id: string) => {
    try {
      await deleteSubmission({ variables: { id } });
      loadSubmissions(true);
    } catch (error) {
      console.error('Delete failed:', error);
    }
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };



  if (isLoading || assignmentsLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[50vh]">
          <p>Loading...</p>
        </div>
      </DashboardLayout>
    );
  }

  if (!isAuthenticated) {
    router.push('/auth/sign-in');
    return null;
  }

  return (
    <DashboardLayout>
      <div className="max-w-6xl mx-auto">
        {/* Header Section */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div>
              {assignmentFromUrl && (
                <button
                  onClick={() => router.back()}
                  className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
                >
                  ← Back to Assignment
                </button>
              )}
            </div>
            <button
              onClick={() => {
                // Clear all assessment session data
                localStorage.removeItem('assessmentConfig');
                localStorage.removeItem('currentSubmission');
                console.log('🧹 Starting fresh assessment session');
                // Clear current state
                setAssignmentId('');
                setStudentName('');
                setSubmissionsData({ items: [], loading: false, error: null, lastFetch: 0 });
                // Redirect to clean upload page
                router.push('/student-upload');
              }}
              className="flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
            >
              <span>🧹</span>
              <span>Start Fresh Assessment</span>
            </button>
          </div>
          <h1 className="text-3xl font-light mb-2">Student Submissions</h1>
          <p className="text-gray-600 mb-4">
            {assignmentFromUrl
              ? "Upload student work for this assignment"
              : "Upload student work for assessment"
            }
          </p>

          {/* Assignment Selection */}
          <div className="mb-6">
            <label htmlFor="assignment-select" className="block text-sm font-medium text-gray-700 mb-2">
              Assignment
              {assignmentFromUrl && (
                <span className="ml-2 text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
                  Selected from assignment page
                </span>
              )}
            </label>
            {assignmentFromUrl ? (
              <div className="flex items-center space-x-3">
                <select
                  id="assignment-select"
                  value={assignmentId}
                  onChange={(e) => setAssignmentId(e.target.value)}
                  className="flex-1 max-w-md px-3 py-2 border border-blue-300 bg-blue-50 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Choose an assignment...</option>
                  {assignmentsData?.assignments?.map((assignment: any) => (
                    <option key={assignment.id} value={assignment.id}>
                      {assignment.title}
                    </option>
                  ))}
                </select>
                <span className="text-sm text-gray-500">
                  (You can change this if needed)
                </span>
              </div>
            ) : (
              <select
                id="assignment-select"
                value={assignmentId}
                onChange={(e) => setAssignmentId(e.target.value)}
                className="w-full max-w-md px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Choose an assignment...</option>
                {assignmentsData?.assignments?.map((assignment: any) => (
                  <option key={assignment.id} value={assignment.id}>
                    {assignment.title}
                  </option>
                ))}
              </select>
            )}
          </div>

          {/* Simple submission count display */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-gray-700">
                Assignment Submissions
              </h3>
              <div className="text-xs text-gray-500">
                Showing {submissionsData.items.length} submissions for this assignment
              </div>
            </div>
          </div>

          {/* Bulk Mode Notice */}
          {isBulkMode && (
            <div className="mb-6 p-4 bg-orange-50 border border-orange-200 rounded-lg">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-orange-500 rounded-full mr-2"></div>
                <h3 className="text-sm font-medium text-orange-800">Bulk Mode Active</h3>
              </div>
              <p className="text-xs text-orange-700 mt-1">
                Filtering is disabled in bulk mode to ensure all submissions are available for processing.
                Showing {submissionsData.items.length} submissions for this assignment.
              </p>
            </div>
          )}

          {/* Upload Mode Toggle */}
          <div className="mb-6">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setIsBulkMode(false)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  !isBulkMode
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                Single Upload
              </button>
              <button
                onClick={() => setIsBulkMode(true)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  isBulkMode
                    ? 'bg-orange-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                🚀 Bulk Upload (up to 20 files)
              </button>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              {isBulkMode
                ? 'Upload multiple student submissions at once. Student names will be extracted from filenames.'
                : 'Upload one file at a time with manual student name entry.'
              }
            </p>
          </div>

          {/* Student Name Input - Only show in single mode */}
          {!isBulkMode && (
            <div className="mb-6">
              <label htmlFor="student-name" className="block text-sm font-medium text-gray-700 mb-2">
                Student Name
              </label>
              <input
                type="text"
                id="student-name"
                value={studentName}
                onChange={(e) => setStudentName(e.target.value)}
                placeholder="Enter student name..."
                className="w-full max-w-md px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          )}

          {/* Step Indicator */}
          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center font-medium ${
                mostRecentSubmission ? 'bg-green-500 text-white' : 'bg-blue-500 text-white'
              }`}>
                {mostRecentSubmission ? (
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                ) : (
                  '1'
                )}
              </div>
              <span className={`ml-2 font-medium ${
                mostRecentSubmission ? 'text-green-600' : 'text-blue-600'
              }`}>Student Submission</span>
            </div>
            <div className={`w-8 h-px ${mostRecentSubmission ? 'bg-green-500' : 'bg-gray-300'}`}></div>
            <div className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center font-medium ${
                mostRecentSubmission ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600'
              }`}>2</div>
              <span className={`ml-2 ${
                mostRecentSubmission ? 'text-blue-600 font-medium' : 'text-gray-500'
              }`}>Configuration</span>
            </div>
            <div className="w-8 h-px bg-gray-300"></div>
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center font-medium">3</div>
              <span className="ml-2 text-gray-500">Review & Submit</span>
            </div>
          </div>
        </div>

        {/* Assignment Info */}
        {assignmentId && assignmentsData?.assignments && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-blue-900 mb-2">
                  Current Assignment: {assignmentsData.assignments.find((a: any) => a.id === assignmentId)?.title || 'Loading...'}
                </h3>
                <p className="text-blue-700 text-sm">
                  All uploads will be associated with this assignment
                </p>
              </div>
              {submissionsData.items.length > 0 && (
                <AssignmentResetButton
                  assignmentId={assignmentId}
                  assignmentTitle={assignmentsData.assignments.find((a: any) => a.id === assignmentId)?.title || 'Assignment'}
                  submissionCount={submissionsData.items.length}
                  variant="icon"
                  onResetComplete={(clearedCount) => {
                    loadSubmissions(true); // Refresh the submissions list
                  }}
                />
              )}
            </div>
          </div>
        )}

        {/* Uploaded Files Section */}
        <div className="bg-white rounded-lg shadow-md mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-medium">
                  Uploaded Files ({submissionsData.items.length})
                </h2>
                {submissionsData.items.length > 0 && (
                  <p className="text-sm text-gray-600 mt-1">
                    Ready for assessment • Click reset to clear for new cycle
                  </p>
                )}
              </div>
              {submissionsData.items.length > 0 && assignmentId && assignmentsData?.assignments && (
                <AssignmentResetButton
                  assignmentId={assignmentId}
                  assignmentTitle={assignmentsData.assignments.find((a: any) => a.id === assignmentId)?.title || 'Assignment'}
                  submissionCount={submissionsData.items.length}
                  variant="button"
                  size="sm"
                  onResetComplete={(clearedCount) => {
                    loadSubmissions(true); // Refresh the submissions list
                  }}
                />
              )}
            </div>
          </div>

          <div className="p-6">




          {submissionsData.loading ? (
            <div className="text-center text-blue-500">
              Loading submissions...
            </div>
          ) : submissionsData.error ? (
            <div className="text-center text-red-500">
              Error: {submissionsData.error}
              <button
                onClick={() => loadSubmissions(true)}
                className="ml-2 text-blue-500 underline"
              >
                Retry
              </button>
            </div>
          ) : submissionsData.items.length === 0 ? (
            <div className="text-center text-gray-500">
              No files uploaded yet
              <button
                onClick={() => loadSubmissions(true)}
                className="mt-2 text-blue-500 underline"
              >
                Refresh
              </button>
            </div>
          ) : (
            <div>
              {/* Show all submissions in bulk mode, or just the most recent in single mode */}
              {(isBulkMode ? submissionsData.items : submissionsData.items.slice(0, 1)).map((submission: any, index: number) => (
                <div key={submission.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg mb-4">
                  <div className="flex items-center space-x-3">
                    <FileText className="w-5 h-5 text-gray-400" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        Student: {submission.student_name}
                      </div>
                      <div className="text-xs text-gray-500">
                        Status: {submission.processing_status} • Uploaded: {new Date(submission.upload_date).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={() => handleDelete(submission.id)}
                    className="text-red-600 hover:text-red-900 p-1"
                    title="Delete file"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              ))}

              {/* Show count if there are more submissions in single mode */}
              {!isBulkMode && submissionsData.items.length > 1 && (
                <div className="text-center text-sm text-gray-500 mb-4">
                  ... and {submissionsData.items.length - 1} more submission{submissionsData.items.length > 2 ? 's' : ''}
                  <button
                    onClick={() => setIsBulkMode(true)}
                    className="ml-2 text-orange-600 hover:text-orange-800 font-medium"
                  >
                    Switch to bulk mode to see all
                  </button>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-600">
                  {submissionsData.items.length} file{submissionsData.items.length !== 1 ? 's' : ''} ready for assessment
                </div>
                <div className="flex space-x-3">
                  {submissionsData.items.length === 1 && (
                    <button
                      onClick={() => router.push(`/assessment/configuration?assignment=${assignmentId}`)}
                      className="bg-accent text-white px-6 py-2 rounded-md hover:bg-[#9A7649] transition-colors flex items-center space-x-2"
                    >
                      <span>Continue to Configuration</span>
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  )}
                  {/* Single Assessment Button - Handles Both Single and Bulk */}
                  <button
                    onClick={() => router.push(`/assessment/configuration?assignment=${assignmentId}`)}
                    className="bg-blue-500 text-white px-6 py-2 rounded-md hover:bg-blue-600 transition-colors flex items-center space-x-2"
                  >
                    <span>Start Assessment ({submissionsData.items.length} file{submissionsData.items.length !== 1 ? 's' : ''})</span>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          )}
          </div>
        </div>

        {/* File Upload Area */}
        <div className="bg-white rounded-lg shadow-md mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium">
              {isBulkMode ? '🚀 Bulk Upload Files' : 'Upload Files'}
            </h2>
            {isBulkMode && (
              <p className="text-sm text-gray-600 mt-1">
                Upload up to 20 files at once. Student names will be extracted from filenames.
              </p>
            )}
          </div>

          <div className="p-6">
            {/* Single Upload Mode */}
            {!isBulkMode && (
              <div
                {...getRootProps()}
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                  !assignmentId || !studentName.trim()
                    ? 'border-gray-200 bg-gray-50 cursor-not-allowed'
                    : isDragActive
                      ? 'border-blue-400 bg-blue-50 cursor-pointer'
                      : 'border-gray-300 hover:border-gray-400 cursor-pointer'
                }`}
              >
                <input {...getInputProps()} />
                <Upload className={`w-12 h-12 mx-auto mb-4 ${!assignmentId || !studentName.trim() ? 'text-gray-300' : 'text-gray-400'}`} />
                <p className={`text-lg mb-2 ${!assignmentId || !studentName.trim() ? 'text-gray-400' : 'text-gray-600'}`}>
                  {!assignmentId
                    ? 'Please select an assignment first'
                    : !studentName.trim()
                      ? 'Please enter a student name'
                      : isDragActive
                        ? 'Drop files here'
                        : 'Drag and drop files here'
                  }
                </p>
                {assignmentId && studentName.trim() && (
                  <>
                    <p className="text-sm text-gray-500 mb-4">or click to browse</p>
                    <button className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors">
                      Choose File
                    </button>
                    <p className="text-xs text-gray-400 mt-4">
                      Supported file types: PDF, DOCX, TXT, PNG, JPG
                    </p>
                  </>
                )}
              </div>
            )}

            {/* Bulk Upload Mode */}
            {isBulkMode && (
              <div
                {...bulkDropzone.getRootProps()}
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                  !assignmentId
                    ? 'border-gray-200 bg-gray-50 cursor-not-allowed'
                    : bulkDropzone.isDragActive
                      ? 'border-orange-400 bg-orange-50 cursor-pointer'
                      : 'border-orange-300 hover:border-orange-400 cursor-pointer'
                }`}
              >
                <input {...bulkDropzone.getInputProps()} />
                <div className="flex items-center justify-center mb-4">
                  <Upload className={`w-12 h-12 ${!assignmentId ? 'text-gray-300' : 'text-orange-400'}`} />
                  <span className="text-2xl ml-2">🚀</span>
                </div>
                <p className={`text-lg mb-2 ${!assignmentId ? 'text-gray-400' : 'text-gray-600'}`}>
                  {!assignmentId
                    ? 'Please select an assignment first'
                    : bulkDropzone.isDragActive
                      ? 'Drop up to 20 files here'
                      : 'Drag and drop multiple files here'
                  }
                </p>
                {assignmentId && (
                  <>
                    <p className="text-sm text-gray-500 mb-4">or click to browse and select multiple files</p>
                    <button className="bg-orange-500 text-white px-6 py-3 rounded-md hover:bg-orange-600 transition-colors font-medium">
                      Choose Multiple Files
                    </button>
                    <p className="text-xs text-gray-400 mt-4">
                      Maximum 20 files • 50MB per file • PDF, DOCX, TXT, PNG, JPG
                    </p>
                    <p className="text-xs text-orange-600 mt-2 font-medium">
                      💡 Tip: Name files like "StudentName_Assignment.pdf" for automatic name extraction
                    </p>
                  </>
                )}
              </div>
            )}

            {/* Bulk Progress Display */}
            {isBulkMode && bulkProgress.processing && (
              <div className="mt-6 p-4 bg-orange-50 border border-orange-200 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-medium text-orange-900">🚀 Bulk Processing</h3>
                  <div className="text-sm text-orange-700">
                    {bulkProgress.completed + bulkProgress.failed} / {bulkProgress.total}
                  </div>
                </div>

                <div className="w-full bg-orange-200 rounded-full h-3 mb-3">
                  <div
                    className="bg-orange-500 h-3 rounded-full transition-all duration-300"
                    style={{
                      width: `${((bulkProgress.completed + bulkProgress.failed) / bulkProgress.total) * 100}%`
                    }}
                  ></div>
                </div>

                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div className="text-center">
                    <div className="text-lg font-bold text-green-600">{bulkProgress.completed}</div>
                    <div className="text-gray-600">Completed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-red-600">{bulkProgress.failed}</div>
                    <div className="text-gray-600">Failed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-orange-600">
                      {bulkProgress.total - bulkProgress.completed - bulkProgress.failed}
                    </div>
                    <div className="text-gray-600">Remaining</div>
                  </div>
                </div>
              </div>
            )}

            {/* Single Upload Progress */}
            {!isBulkMode && uploadProgress['batch'] !== undefined && (
              <div className="mt-4">
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600 flex-1">Uploading files...</span>
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress['batch']}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-500">{uploadProgress['batch']}%</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Student Data CSV Section */}
        <div className="bg-white rounded-lg shadow-md">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium">Student Data (Optional)</h2>
          </div>

          <div className="p-6">
            <div
              {...csvDropzone.getRootProps()}
              className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
                csvDropzone.isDragActive ? 'border-green-400 bg-green-50' : 'border-gray-300 hover:border-gray-400'
              }`}
            >
              <input {...csvDropzone.getInputProps()} />
              <FileText className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600 mb-2">Upload student data CSV</p>
              <p className="text-xs text-gray-500">
                This will help match submissions to students
              </p>
            </div>

            {csvFile && (
              <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
                <p className="text-sm text-green-800">
                  CSV uploaded: {csvFile.name}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
