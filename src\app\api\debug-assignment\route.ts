import { NextRequest, NextResponse } from 'next/server';
import { getServerApolloClient } from '@/lib/apollo-client';
import { gql } from '@apollo/client';

// Debug route to test assignment queries
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const assignmentId = searchParams.get('id');

    if (!assignmentId) {
      return NextResponse.json(
        { error: 'Missing assignment ID' },
        { status: 400 }
      );
    }



    // Use server-side Apollo client with admin secret (bypasses permissions)
    const apolloClient = getServerApolloClient();

    // Test the exact same query that's failing
    const GET_ASSIGNMENT_BY_ID = gql`
      query GetAssignmentById($id: uuid!) {
        assignments_by_pk(id: $id) {
          id
          title
          description
          status
          created_at
          user_id
        }
      }
    `;

    const result = await apolloClient.query({
      query: GET_ASSIGNMENT_BY_ID,
      variables: { id: assignmentId }
    });



    return NextResponse.json({
      success: true,
      data: result.data,
      assignmentFound: !!result.data?.assignments_by_pk,
      assignmentData: result.data?.assignments_by_pk
    });

  } catch (error) {

    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error
    }, { status: 500 });
  }
}
