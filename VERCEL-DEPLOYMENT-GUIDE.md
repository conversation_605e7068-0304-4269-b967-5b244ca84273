# 🚀 SECURE VERCEL DEPLOYMENT GUIDE

## ⚠️ CRITICAL: SECURITY FIRST DEPLOYMENT

### 🔒 STEP 1: SECURE ENVIRONMENT VARIABLES

**IMMEDIATELY BEFORE DEPLOYMENT:**

1. **Remove .env.local from repository** (if not already in .gitignore)
2. **Add all environment variables to Vercel dashboard**
3. **Regenerate sensitive secrets**

#### Required Vercel Environment Variables:

```bash
# Nhost Configuration
NEXT_PUBLIC_NHOST_SUBDOMAIN=iczrhlxcocjcririongm
NEXT_PUBLIC_NHOST_REGION=eu-central-1
NHOST_GRAPHQL_URL=https://iczrhlxcocjcririongm.graphql.eu-central-1.nhost.run/v1
NHOST_FUNCTIONS_URL=https://iczrhlxcocjcririongm.functions.eu-central-1.nhost.run/v1

# REGENERATE THESE SECRETS BEFORE DEPLOYMENT
NHOST_ADMIN_SECRET=[REGENERATE_IN_NHOST_DASHBOARD]

# AI API Keys (verify these are valid and have sufficient credits)
OPENAI_API_KEY=[YOUR_OPENAI_KEY]
ANTHROPIC_API_KEY=[YOUR_ANTHROPIC_KEY]

# Production Settings
NODE_ENV=production
```

### 🛡️ STEP 2: SECURITY CONFIGURATION

#### A. Update CORS Origins
In `src/app/api/assess-submission/route.ts`, update line 178-179:
```typescript
'https://your-actual-vercel-domain.vercel.app',
'https://your-custom-domain.com'  // If you have one
```

#### B. Verify Security Headers
Check `next.config.js` - security headers are already configured ✅

#### C. Rate Limiting Active
- Assessment API: 5 requests/minute ✅
- File uploads: 10 requests/minute ✅
- General API: 30 requests/minute ✅

### 🚀 STEP 3: DEPLOYMENT PROCESS

#### Option A: Vercel CLI (Recommended)
```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy
vercel --prod
```

#### Option B: GitHub Integration
1. Connect repository to Vercel
2. Configure environment variables in dashboard
3. Deploy automatically on push

### 🔍 STEP 4: POST-DEPLOYMENT VERIFICATION

#### Immediate Checks:
- [ ] Application loads without errors
- [ ] Authentication works (sign in/out)
- [ ] File uploads function
- [ ] AI assessment processes correctly
- [ ] Rate limiting is active (test with multiple requests)
- [ ] CORS is properly restricted

#### Test Commands:
```bash
# Test rate limiting
curl -X POST https://your-domain.vercel.app/api/assess-submission \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}' \
  # Run this 6 times quickly - should get 429 on 6th request

# Test CORS
curl -X OPTIONS https://your-domain.vercel.app/api/assess-submission \
  -H "Origin: https://malicious-site.com" \
  # Should return 'null' for Access-Control-Allow-Origin
```

### 🚨 STEP 5: SECURITY MONITORING

#### Set up monitoring for:
- [ ] Unusual API usage patterns
- [ ] Failed authentication attempts
- [ ] Large file upload attempts
- [ ] Rate limit violations
- [ ] Error rates

#### Vercel Analytics:
- Enable Vercel Analytics for traffic monitoring
- Set up alerts for unusual patterns

### 🔧 STEP 6: ONGOING SECURITY

#### Weekly Tasks:
- [ ] Review API usage and costs
- [ ] Check error logs for security issues
- [ ] Monitor file upload patterns
- [ ] Verify rate limiting effectiveness

#### Monthly Tasks:
- [ ] Rotate API keys
- [ ] Review and update CORS origins
- [ ] Security audit of new features
- [ ] Update dependencies

### 🆘 EMERGENCY PROCEDURES

#### If API Keys Compromised:
1. **IMMEDIATELY** revoke keys in OpenAI/Anthropic dashboards
2. Generate new keys
3. Update Vercel environment variables
4. Redeploy application
5. Monitor usage for unauthorized activity

#### If Database Compromised:
1. **IMMEDIATELY** regenerate Nhost admin secret
2. Review database access logs
3. Check for unauthorized data access
4. Update Vercel environment variables
5. Redeploy application

### 📞 SUPPORT CONTACTS

- **Vercel Support**: https://vercel.com/support
- **Nhost Support**: https://nhost.io/support
- **OpenAI Support**: https://help.openai.com/
- **Anthropic Support**: https://support.anthropic.com/

---

## 🎯 DEPLOYMENT CHECKLIST

- [ ] Environment variables secured in Vercel
- [ ] Sensitive secrets regenerated
- [ ] CORS origins updated for production
- [ ] Rate limiting tested and working
- [ ] Error boundaries implemented
- [ ] Security headers verified
- [ ] Monitoring configured
- [ ] Emergency procedures documented

**🚀 Ready for secure deployment!**
