'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuthenticationStatus } from '@nhost/nextjs';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { FileText, Download, ChevronDown, ChevronUp, Award, TrendingUp } from 'lucide-react';

interface CriteriaResult {
  name: string;
  score: number;
  feedback: string;
  weight: number;
}

interface StudentResult {
  id?: string;
  submissionId?: string;
  student_name?: string;
  studentName?: string;
  name?: string;
  overall_grade?: string;
  overallGrade?: string;
  grade?: string;
  overall_score?: number;
  overallScore?: number;
  score?: number;
  criteria_results?: CriteriaResult[];
  general_feedback?: string;
  feedbackSummary?: string;
  feedback?: string;
  file_path?: string;
  fileName?: string;
  filePath?: string;
  success?: boolean;
}

export default function BulkResultsPage() {
  const { isAuthenticated, isLoading } = useAuthenticationStatus();
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [results, setResults] = useState<StudentResult[]>([]);
  const [loading, setLoading] = useState(true);
  const [fetchingDetails, setFetchingDetails] = useState(false);
  const [fetchProgress, setFetchProgress] = useState({ current: 0, total: 0 });
  const [expandedStudents, setExpandedStudents] = useState<Set<string>>(new Set());
  const [sortBy, setSortBy] = useState<'name' | 'grade' | 'score'>('name');
  const [filterGrade, setFilterGrade] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');

  // Load results from localStorage and fetch detailed data
  useEffect(() => {
    const loadAndEnrichResults = async () => {
      try {
        // Try to get results from localStorage first
        const storedResults = localStorage.getItem('bulkAssessmentResults');
        if (storedResults) {
          const parsedResults = JSON.parse(storedResults);
          console.log('📊 Loaded bulk results:', parsedResults.length, 'students');
          console.log('🔍 Sample result structure:', parsedResults[0]);
          console.log('🔍 Available properties:', Object.keys(parsedResults[0] || {}));

          // Fetch detailed assessment data for each submission
          console.log('🔍 Fetching detailed assessment data...');
          setFetchingDetails(true);
          setFetchProgress({ current: 0, total: parsedResults.length });

          const enrichedResults = await Promise.all(
            parsedResults.map(async (result: any, index: number) => {
              try {
                const response = await fetch(`/api/assessment-results?submissionId=${result.submissionId}`);
                if (response.ok) {
                  const detailedData = await response.json();
                  console.log('✅ Fetched detailed data for:', result.studentName);
                  console.log('🔍 API Response structure:', Object.keys(detailedData));

                  // Update progress
                  setFetchProgress(prev => ({ ...prev, current: index + 1 }));

                  // Extract assessment results from API response
                  const assessmentResult = detailedData.assessment_results?.[0];
                  console.log('🔍 Assessment result:', assessmentResult ? 'Found' : 'Not found');

                  if (assessmentResult) {
                    console.log('🔍 Assessment properties:', Object.keys(assessmentResult));
                    console.log('🔍 Per-criteria feedback count:', assessmentResult.per_criterion_feedbacks?.length || 0);
                  }

                  return {
                    ...result,
                    general_feedback: assessmentResult?.general_comments || 'No feedback available',
                    criteria_results: assessmentResult?.per_criterion_feedbacks?.map((feedback: any) => ({
                      name: feedback.assessment_criterium?.criterion_name || 'Unknown Criterion',
                      score: feedback.score || 0,
                      feedback: feedback.feedback_text || 'No feedback',
                      weight: 0 // Weight not available in current schema
                    })) || [],
                    file_path: assessmentResult?.student_submission?.student_name || result.studentName || 'Unknown file',
                    id: result.submissionId
                  };
                } else {
                  console.warn('⚠️ Failed to fetch detailed data for:', result.studentName);
                  return {
                    ...result,
                    general_feedback: 'Detailed feedback not available',
                    criteria_results: [],
                    file_path: 'Unknown file',
                    id: result.submissionId
                  };
                }
              } catch (error) {
                console.error('❌ Error fetching detailed data for:', result.studentName, error);
                return {
                  ...result,
                  general_feedback: 'Error loading feedback',
                  criteria_results: [],
                  file_path: 'Unknown file',
                  id: result.submissionId
                };
              }
            })
          );

          console.log('✅ Enriched results with detailed data');
          setResults(enrichedResults);
          setFetchingDetails(false);
        } else {
          console.log('⚠️ No bulk results found in localStorage');
        }
      } catch (error) {
        console.error('❌ Error loading bulk results:', error);
        setFetchingDetails(false);
      } finally {
        setLoading(false);
      }
    };

    if (isAuthenticated) {
      loadAndEnrichResults();
    }
  }, [isAuthenticated]);

  // Toggle student expansion
  const toggleStudent = (studentId: string) => {
    const newExpanded = new Set(expandedStudents);
    if (newExpanded.has(studentId)) {
      newExpanded.delete(studentId);
    } else {
      newExpanded.add(studentId);
    }
    setExpandedStudents(newExpanded);
  };

  // Sort results with safe property access
  const sortedResults = [...results].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        const nameA = a.student_name || a.studentName || a.name || 'Unknown';
        const nameB = b.student_name || b.studentName || b.name || 'Unknown';
        return nameA.localeCompare(nameB);
      case 'grade':
        const gradeOrder = { 'A': 5, 'B': 4, 'C': 3, 'D': 2, 'F': 1 };
        const gradeA = a.overall_grade || a.overallGrade || a.grade || 'F';
        const gradeB = b.overall_grade || b.overallGrade || b.grade || 'F';
        return (gradeOrder[gradeB as keyof typeof gradeOrder] || 0) -
               (gradeOrder[gradeA as keyof typeof gradeOrder] || 0);
      case 'score':
        const scoreA = a.overall_score || a.overallScore || a.score || 0;
        const scoreB = b.overall_score || b.overallScore || b.score || 0;
        return scoreB - scoreA;
      default:
        return 0;
    }
  });

  // Filter by grade and search query with safe property access
  const filteredResults = sortedResults.filter(r => {
    // Grade filter
    const grade = r.overall_grade || r.overallGrade || r.grade || 'F';
    const gradeMatch = filterGrade === 'all' || grade === filterGrade;

    // Search filter
    const studentName = (r.student_name || r.studentName || r.name || '').toLowerCase();
    const fileName = (r.file_path || r.filePath || r.fileName || '').toLowerCase();
    const searchMatch = searchQuery === '' ||
      studentName.includes(searchQuery.toLowerCase()) ||
      fileName.includes(searchQuery.toLowerCase());

    return gradeMatch && searchMatch;
  });

  // Grade distribution with safe property access
  const gradeDistribution = results.reduce((acc, result) => {
    const grade = result.overall_grade || result.overallGrade || result.grade || 'F';
    acc[grade] = (acc[grade] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Average score with safe property access
  const averageScore = results.length > 0
    ? results.reduce((sum, r) => {
        const score = r.overall_score || r.overallScore || r.score || 0;
        return sum + score;
      }, 0) / results.length
    : 0;

  // Calculate criteria performance summary
  const criteriaPerformance = results.reduce((acc, result) => {
    if (result.criteria_results && result.criteria_results.length > 0) {
      result.criteria_results.forEach(criteria => {
        if (!acc[criteria.name]) {
          acc[criteria.name] = { scores: [], total: 0, count: 0 };
        }
        acc[criteria.name].scores.push(criteria.score);
        acc[criteria.name].total += criteria.score;
        acc[criteria.name].count += 1;
      });
    }
    return acc;
  }, {} as Record<string, { scores: number[], total: number, count: number }>);

  // Calculate averages for each criterion
  const criteriaAverages = Object.entries(criteriaPerformance).map(([name, data]) => ({
    name,
    average: data.count > 0 ? data.total / data.count : 0,
    count: data.count
  })).sort((a, b) => b.average - a.average);

  // Refresh detailed feedback
  const refreshFeedback = async () => {
    if (results.length === 0) return;

    console.log('🔄 Refreshing detailed feedback...');
    setFetchingDetails(true);
    setFetchProgress({ current: 0, total: results.length });

    try {
      const enrichedResults = await Promise.all(
        results.map(async (result, index) => {
          try {
            const submissionId = result.submissionId || result.id;
            const response = await fetch(`/api/assessment-results?submissionId=${submissionId}`);

            if (response.ok) {
              const detailedData = await response.json();
              console.log('✅ Refreshed detailed data for:', result.studentName || result.student_name);

              setFetchProgress(prev => ({ ...prev, current: index + 1 }));

              // Extract assessment results from API response
              const assessmentResult = detailedData.assessment_results?.[0];

              return {
                ...result,
                general_feedback: assessmentResult?.general_comments || 'No feedback available',
                criteria_results: assessmentResult?.per_criterion_feedbacks?.map((feedback: any) => ({
                  name: feedback.assessment_criterium?.criterion_name || 'Unknown Criterion',
                  score: feedback.score || 0,
                  feedback: feedback.feedback_text || 'No feedback',
                  weight: 0 // Weight not available in current schema
                })) || [],
                file_path: assessmentResult?.student_submission?.student_name || result.file_path || 'Unknown file'
              };
            } else {
              console.warn('⚠️ Failed to refresh detailed data for:', result.studentName || result.student_name);
              return result; // Keep existing data
            }
          } catch (error) {
            console.error('❌ Error refreshing detailed data:', error);
            return result; // Keep existing data
          }
        })
      );

      setResults(enrichedResults);
      console.log('✅ Feedback refresh completed');
    } catch (error) {
      console.error('❌ Error during feedback refresh:', error);
    } finally {
      setFetchingDetails(false);
    }
  };

  // Export results
  const exportResults = () => {
    const csvContent = [
      ['Student Name', 'Overall Grade', 'Overall Score', 'General Feedback'],
      ...results.map(r => [
        r.student_name || r.studentName || r.name || 'Unknown Student',
        r.overall_grade || r.overallGrade || r.grade || 'F',
        (r.overall_score || r.overallScore || r.score || 0).toString(),
        (r.general_feedback || r.feedbackSummary || r.feedback || 'No feedback available').replace(/,/g, ';') // Replace commas to avoid CSV issues
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `bulk_assessment_results_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  if (isLoading || loading) {
    return (
      <DashboardLayout>
        <div className="flex flex-col items-center justify-center min-h-[50vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-lg font-medium text-gray-900 mb-2">Loading bulk results...</p>
            {fetchingDetails && (
              <div className="w-64 mx-auto">
                <p className="text-sm text-gray-600 mb-2">
                  Fetching detailed feedback: {fetchProgress.current} / {fetchProgress.total}
                </p>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${fetchProgress.total > 0 ? (fetchProgress.current / fetchProgress.total) * 100 : 0}%`
                    }}
                  ></div>
                </div>
              </div>
            )}
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!isAuthenticated) {
    router.push('/auth/sign-in');
    return null;
  }

  if (results.length === 0) {
    return (
      <DashboardLayout>
        <div className="max-w-4xl mx-auto">
          <div className="text-center py-12">
            <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">No Bulk Results Found</h2>
            <p className="text-gray-600 mb-6">
              No bulk assessment results are available. Please run a bulk assessment first.
            </p>
            <button
              onClick={() => router.push('/student-upload')}
              className="bg-blue-500 text-white px-6 py-2 rounded-md hover:bg-blue-600 transition-colors"
            >
              Back to Upload
            </button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-semibold text-gray-900">Bulk Assessment Results</h1>
              <p className="text-gray-600 mt-1">
                Results for {results.length} student{results.length !== 1 ? 's' : ''}
              </p>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={refreshFeedback}
                disabled={fetchingDetails}
                className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg className={`w-4 h-4 ${fetchingDetails ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span>{fetchingDetails ? 'Refreshing...' : 'Refresh Feedback'}</span>
              </button>
              <button
                onClick={exportResults}
                className="flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
              >
                <Download className="w-4 h-4" />
                <span>Export CSV</span>
              </button>
              <button
                onClick={() => router.push('/student-upload')}
                className="flex items-center space-x-2 bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors"
              >
                <span>New Assessment</span>
              </button>
            </div>
          </div>

          {/* Summary Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-white p-4 rounded-lg shadow border">
              <div className="flex items-center">
                <Award className="w-8 h-8 text-blue-500 mr-3" />
                <div>
                  <p className="text-sm text-gray-600">Average Score</p>
                  <p className="text-2xl font-semibold">{averageScore.toFixed(1)}%</p>
                </div>
              </div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow border">
              <div className="flex items-center">
                <TrendingUp className="w-8 h-8 text-green-500 mr-3" />
                <div>
                  <p className="text-sm text-gray-600">A Grades</p>
                  <p className="text-2xl font-semibold">{gradeDistribution['A'] || 0}</p>
                </div>
              </div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow border">
              <div className="flex items-center">
                <FileText className="w-8 h-8 text-orange-500 mr-3" />
                <div>
                  <p className="text-sm text-gray-600">Total Students</p>
                  <p className="text-2xl font-semibold">{results.length}</p>
                </div>
              </div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow border">
              <div>
                <p className="text-sm text-gray-600 mb-2">Grade Distribution</p>
                <div className="flex space-x-1">
                  {['A', 'B', 'C', 'D', 'F'].map(grade => (
                    <div key={grade} className="text-center">
                      <div className="text-xs font-medium">{grade}</div>
                      <div className="text-sm">{gradeDistribution[grade] || 0}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Class Criteria Performance Summary */}
          {criteriaAverages.length > 0 && (
            <div className="bg-white rounded-lg shadow border p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Class Performance by Criteria</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {criteriaAverages.map((criteria, index) => (
                  <div key={index} className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900 text-sm truncate" title={criteria.name}>
                        {criteria.name}
                      </h4>
                      <span className={`text-sm font-semibold ${
                        criteria.average >= 90 ? 'text-green-600' :
                        criteria.average >= 80 ? 'text-blue-600' :
                        criteria.average >= 70 ? 'text-yellow-600' :
                        criteria.average >= 60 ? 'text-orange-600' :
                        'text-red-600'
                      }`}>
                        {criteria.average.toFixed(1)}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          criteria.average >= 90 ? 'bg-green-500' :
                          criteria.average >= 80 ? 'bg-blue-500' :
                          criteria.average >= 70 ? 'bg-yellow-500' :
                          criteria.average >= 60 ? 'bg-orange-500' :
                          'bg-red-500'
                        }`}
                        style={{ width: `${criteria.average}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-gray-600">
                      {criteria.count} of {results.length} students assessed
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}



          {/* Controls */}
          <div className="flex flex-wrap items-center gap-4 mb-6">
            <div className="flex-1 min-w-64">
              <label className="text-sm font-medium text-gray-700 mr-2">Search:</label>
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search by student name or file name..."
                className="w-full border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700 mr-2">Sort by:</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as 'name' | 'grade' | 'score')}
                className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="name">Student Name</option>
                <option value="grade">Grade (High to Low)</option>
                <option value="score">Score (High to Low)</option>
              </select>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700 mr-2">Filter by grade:</label>
              <select
                value={filterGrade}
                onChange={(e) => setFilterGrade(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Grades</option>
                <option value="A">A</option>
                <option value="B">B</option>
                <option value="C">C</option>
                <option value="D">D</option>
                <option value="F">F</option>
              </select>
            </div>
            {(searchQuery || filterGrade !== 'all') && (
              <button
                onClick={() => {
                  setSearchQuery('');
                  setFilterGrade('all');
                }}
                className="text-sm text-blue-600 hover:text-blue-800 underline"
              >
                Clear filters
              </button>
            )}
          </div>
        </div>

        {/* Results Summary */}
        {(searchQuery || filterGrade !== 'all') && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              Showing {filteredResults.length} of {results.length} results
              {searchQuery && ` matching "${searchQuery}"`}
              {filterGrade !== 'all' && ` with grade ${filterGrade}`}
            </p>
          </div>
        )}

        {/* Results List */}
        <div className="space-y-4">
          {filteredResults.map((result, index) => {
            const resultId = result.id || result.submissionId || `result-${index}`;
            return (
            <div key={resultId} className="bg-white rounded-lg shadow border">
              {/* Student Header */}
              <div
                className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={() => toggleStudent(resultId)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 flex-1">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-1">
                        {result.student_name || result.studentName || result.name || 'Unknown Student'}
                      </h3>
                      <p className="text-sm text-gray-600 mb-2">
                        {result.file_path || result.filePath || result.fileName || 'Unknown File'}
                      </p>

                      {/* Criteria Summary Bar */}
                      {result.criteria_results && result.criteria_results.length > 0 && (
                        <div className="flex flex-wrap gap-2">
                          {result.criteria_results.map((criteria, index) => (
                            <div
                              key={index}
                              className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                criteria.score >= 90 ? 'bg-green-100 text-green-800' :
                                criteria.score >= 80 ? 'bg-blue-100 text-blue-800' :
                                criteria.score >= 70 ? 'bg-yellow-100 text-yellow-800' :
                                criteria.score >= 60 ? 'bg-orange-100 text-orange-800' :
                                'bg-red-100 text-red-800'
                              }`}
                            >
                              {criteria.name}: {criteria.score}%
                            </div>
                          ))}
                        </div>
                      )}

                      {/* Fallback message when no criteria available */}
                      {(!result.criteria_results || result.criteria_results.length === 0) && (
                        <div className="text-xs text-gray-500 italic">
                          Per-criteria breakdown not available
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      {(() => {
                        const grade = result.overall_grade || result.overallGrade || result.grade || 'F';
                        const score = result.overall_score || result.overallScore || result.score || 0;
                        return (
                          <>
                            <div className={`text-2xl font-bold ${
                              grade === 'A' ? 'text-green-600' :
                              grade === 'B' ? 'text-blue-600' :
                              grade === 'C' ? 'text-yellow-600' :
                              grade === 'D' ? 'text-orange-600' :
                              'text-red-600'
                            }`}>
                              {grade}
                            </div>
                            <div className="text-sm text-gray-600">{score}%</div>
                          </>
                        );
                      })()}
                    </div>
                    {expandedStudents.has(resultId) ? (
                      <ChevronUp className="w-5 h-5 text-gray-400" />
                    ) : (
                      <ChevronDown className="w-5 h-5 text-gray-400" />
                    )}
                  </div>
                </div>
              </div>

              {/* Expanded Details */}
              {expandedStudents.has(resultId) && (
                <div className="border-t border-gray-200 p-4">
                  {/* General Feedback */}
                  <div className="mb-6">
                    <h4 className="text-sm font-semibold text-gray-900 mb-2">General Feedback</h4>
                    <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded-md">
                      {result.general_feedback || result.feedbackSummary || result.feedback || 'No feedback available'}
                    </p>
                  </div>

                  {/* Per-Criteria Results */}
                  {result.criteria_results && result.criteria_results.length > 0 && (
                    <div>
                      <h4 className="text-sm font-semibold text-gray-900 mb-3">Detailed Criteria Assessment</h4>
                      <div className="grid gap-4">
                        {result.criteria_results.map((criteria, index) => (
                          <div key={index} className="border border-gray-200 rounded-md p-3">
                            <div className="flex items-center justify-between mb-2">
                              <h5 className="font-medium text-gray-900">{criteria.name}</h5>
                              <div className="flex items-center space-x-2">
                                <span className="text-sm text-gray-600">Weight: {criteria.weight}%</span>
                                <span className={`text-sm font-semibold ${
                                  criteria.score >= 90 ? 'text-green-600' :
                                  criteria.score >= 80 ? 'text-blue-600' :
                                  criteria.score >= 70 ? 'text-yellow-600' :
                                  criteria.score >= 60 ? 'text-orange-600' :
                                  'text-red-600'
                                }`}>
                                  {criteria.score}%
                                </span>
                              </div>
                            </div>
                            <p className="text-sm text-gray-700">{criteria.feedback}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
            );
          })}
        </div>

        {filteredResults.length === 0 && (
          <div className="text-center py-8">
            <p className="text-gray-600">No results match the current filter.</p>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
