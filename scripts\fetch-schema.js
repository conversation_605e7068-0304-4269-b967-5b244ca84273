// This script fetches the GraphQL schema from your Nhost project
const fs = require('fs');
const path = require('path');
const https = require('https');
require('dotenv').config({ path: '.env.local' });

// Get the Nhost project details from environment variables
const subdomain = process.env.NEXT_PUBLIC_NHOST_SUBDOMAIN;
const region = process.env.NEXT_PUBLIC_NHOST_REGION;

if (!subdomain || !region) {
  console.error('Error: NEXT_PUBLIC_NHOST_SUBDOMAIN and NEXT_PUBLIC_NHOST_REGION must be set in .env.local');
  process.exit(1);
}

// Construct the GraphQL endpoint URL
const graphqlEndpoint = `https://${subdomain}.${region}.hasura.nhost.run/v1/graphql`;

// GraphQL introspection query to fetch the schema
const introspectionQuery = `
  query IntrospectionQuery {
    __schema {
      queryType { name }
      mutationType { name }
      subscriptionType { name }
      types {
        ...FullType
      }
      directives {
        name
        description
        locations
        args {
          ...InputValue
        }
      }
    }
  }

  fragment FullType on __Type {
    kind
    name
    description
    fields(includeDeprecated: true) {
      name
      description
      args {
        ...InputValue
      }
      type {
        ...TypeRef
      }
      isDeprecated
      deprecationReason
    }
    inputFields {
      ...InputValue
    }
    interfaces {
      ...TypeRef
    }
    enumValues(includeDeprecated: true) {
      name
      description
      isDeprecated
      deprecationReason
    }
    possibleTypes {
      ...TypeRef
    }
  }

  fragment InputValue on __InputValue {
    name
    description
    type {
      ...TypeRef
    }
    defaultValue
  }

  fragment TypeRef on __Type {
    kind
    name
    ofType {
      kind
      name
      ofType {
        kind
        name
        ofType {
          kind
          name
          ofType {
            kind
            name
            ofType {
              kind
              name
              ofType {
                kind
                name
                ofType {
                  kind
                  name
                }
              }
            }
          }
        }
      }
    }
  }
`;

// Function to make the GraphQL request
function fetchSchema() {
  return new Promise((resolve, reject) => {
    const requestOptions = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = https.request(graphqlEndpoint, requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonResponse = JSON.parse(data);
          if (jsonResponse.errors) {
            reject(new Error(`GraphQL errors: ${JSON.stringify(jsonResponse.errors)}`));
          } else {
            resolve(jsonResponse.data);
          }
        } catch (error) {
          reject(new Error(`Failed to parse response: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(new Error(`Request failed: ${error.message}`));
    });

    req.write(JSON.stringify({ query: introspectionQuery }));
    req.end();
  });
}

// Main function
async function main() {
  try {
    console.log(`Fetching schema from ${graphqlEndpoint}...`);
    const schemaData = await fetchSchema();
    
    // Create the scripts directory if it doesn't exist
    const outputDir = path.join(__dirname, '..', 'schema');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    // Write the schema to a file
    const outputPath = path.join(outputDir, 'schema.json');
    fs.writeFileSync(outputPath, JSON.stringify(schemaData, null, 2));
    
    console.log(`Schema successfully written to ${outputPath}`);
  } catch (error) {
    console.error('Error fetching schema:', error.message);
    process.exit(1);
  }
}

main();
