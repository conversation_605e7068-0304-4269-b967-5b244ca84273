interface FeatureHighlightProps {
  title: string;
  description: string;
  imageSrc?: string;
  imageAlt?: string;
  reverse?: boolean;
}

export default function FeatureHighlight({ 
  title, 
  description, 
  imageSrc, 
  imageAlt = "Feature illustration",
  reverse = false 
}: FeatureHighlightProps) {
  return (
    <section className="py-20">
      <div className="max-w-7xl mx-auto px-6">
        <div className={`flex flex-col ${reverse ? 'md:flex-row-reverse' : 'md:flex-row'} items-center gap-12`}>
          <div className="md:w-1/2">
            <div className="bg-lightGray rounded-lg p-6 h-80 flex items-center justify-center">
              {imageSrc ? (
                <img src={imageSrc} alt={imageAlt} className="max-w-full max-h-full" />
              ) : (
                <div className="w-full h-full bg-white/50 rounded flex items-center justify-center text-gray-400">
                  Feature Illustration
                </div>
              )}
            </div>
          </div>
          
          <div className="md:w-1/2">
            <h2 className="text-3xl md:text-4xl font-light mb-6">{title}</h2>
            <p className="text-lg text-gray-600">{description}</p>
          </div>
        </div>
      </div>
    </section>
  );
}
