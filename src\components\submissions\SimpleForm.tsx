'use client';

import { useState, useRef } from 'react';
import { useMutation, gql } from '@apollo/client';
import Button from '@/components/ui/Button';

const CREATE_SUBMISSION = gql`
  mutation CreateSubmission($assignment_id: uuid!, $file_ids: [submission_files_insert_input!]!) {
    insert_student_submissions_one(object: {
      assignment_id: $assignment_id,
      student_id: "X-Hasura-User-Id",
      status: "submitted",
      submitted_at: "now()",
      submission_files: {
        data: $file_ids
      }
    }) {
      id
      status
      submitted_at
    }
  }
`;

interface SimpleFormProps {
  assignmentId: string;
  assignmentTitle: string;
}

export default function SimpleForm({ assignmentId, assignmentTitle }: SimpleFormProps) {
  const [studentName, setStudentName] = useState('');
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [createSubmission] = useMutation(CREATE_SUBMISSION);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFiles(Array.from(e.target.files));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!studentName.trim()) {
      setErrorMessage('Please enter your name');
      return;
    }

    if (selectedFiles.length === 0) {
      setErrorMessage('Please select at least one file to upload');
      return;
    }

    setIsSubmitting(true);
    setErrorMessage(null);

    try {
      // For simplicity, we'll just create a submission without actual file uploads
      // This is just to test if the form is visible and working
      const result = await createSubmission({
        variables: {
          assignment_id: assignmentId,
          file_ids: selectedFiles.map((file, index) => ({
            file_id: `temp-id-${index}`,
            file_name: file.name,
            file_type: 'document'
          })),
        },
      });

      if (result.data) {
        alert('Submission created successfully!');
        // Don't redirect for now, just show success
      }
    } catch (error) {
      console.error('Error creating submission:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      setErrorMessage(`Submission failed: ${errorMessage}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6" style={{ border: '2px solid #000' }}>
      <h2 className="text-xl font-bold mb-4">Submit Assignment: {assignmentTitle}</h2>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Student Name Field */}
        <div>
          <label htmlFor="student-name" className="block text-sm font-medium text-gray-700 mb-1">
            Student Name <span className="text-red-500">*</span>
          </label>
          <input
            id="student-name"
            type="text"
            value={studentName}
            onChange={(e) => setStudentName(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-accent focus:border-accent"
            required
          />
        </div>

        {/* File Upload Field */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Upload Files <span className="text-red-500">*</span>
          </label>
          <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
            <div className="space-y-1 text-center">
              <div className="flex text-sm text-gray-600">
                <label
                  htmlFor="file-upload"
                  className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
                >
                  <span>Upload files</span>
                  <input
                    id="file-upload"
                    name="file-upload"
                    type="file"
                    multiple
                    className="sr-only"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                  />
                </label>
                <p className="pl-1">or drag and drop</p>
              </div>
              <p className="text-xs text-gray-500">
                Up to 10MB per file
              </p>
            </div>
          </div>

          {/* Selected Files */}
          {selectedFiles.length > 0 && (
            <div className="mt-4">
              <h4 className="text-sm font-medium text-gray-700">Selected Files:</h4>
              <ul className="mt-2 text-sm text-gray-600">
                {selectedFiles.map((file, index) => (
                  <li key={index} className="flex items-center">
                    {file.name} ({(file.size / 1024).toFixed(1)} KB)
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Error Message */}
          {errorMessage && (
            <p className="mt-2 text-sm text-red-600">{errorMessage}</p>
          )}
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <Button
            type="submit"
            disabled={isSubmitting}
            variant="primary"
          >
            {isSubmitting ? 'Submitting...' : 'Submit Assignment'}
          </Button>
        </div>
      </form>
    </div>
  );
}
