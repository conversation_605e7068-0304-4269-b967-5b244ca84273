'use client';

import React, { useState, useEffect } from 'react';
import { AlertCircle, CheckCircle, Eye, EyeOff } from 'lucide-react';

interface ValidationRule {
  test: (value: string) => boolean;
  message: string;
  severity: 'error' | 'warning';
}

interface SecureInputProps {
  type?: 'text' | 'email' | 'password' | 'textarea';
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  label?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  validationRules?: ValidationRule[];
  maxLength?: number;
  minLength?: number;
  sanitize?: boolean;
  showValidation?: boolean;
  rows?: number; // For textarea
}

// Common validation rules
export const commonValidationRules = {
  required: (message = 'This field is required'): ValidationRule => ({
    test: (value) => value.trim().length > 0,
    message,
    severity: 'error'
  }),

  minLength: (min: number, message?: string): ValidationRule => ({
    test: (value) => value.length >= min,
    message: message || `Must be at least ${min} characters`,
    severity: 'error'
  }),

  maxLength: (max: number, message?: string): ValidationRule => ({
    test: (value) => value.length <= max,
    message: message || `Must be no more than ${max} characters`,
    severity: 'error'
  }),

  email: (message = 'Please enter a valid email address'): ValidationRule => ({
    test: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
    message,
    severity: 'error'
  }),

  noScripts: (message = 'Script tags are not allowed'): ValidationRule => ({
    test: (value) => !/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi.test(value),
    message,
    severity: 'error'
  }),

  noHTML: (message = 'HTML tags are not allowed'): ValidationRule => ({
    test: (value) => !/<[^>]*>/g.test(value),
    message,
    severity: 'warning'
  }),

  strongPassword: (message = 'Password must contain uppercase, lowercase, number, and special character'): ValidationRule => ({
    test: (value) => /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/.test(value),
    message,
    severity: 'error'
  }),

  noSQLInjection: (message = 'Invalid characters detected'): ValidationRule => ({
    test: (value) => !/('|(\\)|(;)|(--)|(\|)|(\*)|(%)|(\+)|(=))/g.test(value),
    message,
    severity: 'error'
  })
};

// Sanitization function
function sanitizeInput(value: string): string {
  return value
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/[<>'"&]/g, (char) => { // Escape dangerous characters
      const escapeMap: Record<string, string> = {
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#x27;',
        '&': '&amp;'
      };
      return escapeMap[char] || char;
    })
    .trim();
}

export default function SecureInput({
  type = 'text',
  value,
  onChange,
  placeholder,
  label,
  required = false,
  disabled = false,
  className = '',
  validationRules = [],
  maxLength,
  minLength,
  sanitize = true,
  showValidation = true,
  rows = 3
}: SecureInputProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [validationResults, setValidationResults] = useState<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }>({ isValid: true, errors: [], warnings: [] });

  // Validate input
  useEffect(() => {
    if (!showValidation) return;

    const errors: string[] = [];
    const warnings: string[] = [];

    // Apply built-in validation rules
    const allRules = [...validationRules];

    // Add automatic rules
    if (required) {
      allRules.push(commonValidationRules.required());
    }
    if (minLength) {
      allRules.push(commonValidationRules.minLength(minLength));
    }
    if (maxLength) {
      allRules.push(commonValidationRules.maxLength(maxLength));
    }
    if (type === 'email') {
      allRules.push(commonValidationRules.email());
    }

    // Always check for security issues
    allRules.push(commonValidationRules.noScripts());

    // Run validation
    allRules.forEach(rule => {
      if (!rule.test(value)) {
        if (rule.severity === 'error') {
          errors.push(rule.message);
        } else {
          warnings.push(rule.message);
        }
      }
    });

    setValidationResults({
      isValid: errors.length === 0,
      errors,
      warnings
    });
  }, [value, validationRules, required, minLength, maxLength, type, showValidation]);

  const handleChange = (newValue: string) => {
    // Apply sanitization if enabled
    const processedValue = sanitize ? sanitizeInput(newValue) : newValue;
    onChange(processedValue);
  };

  const inputClasses = `
    w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
    ${validationResults.errors.length > 0 ? 'border-red-300 bg-red-50' : 'border-gray-300'}
    ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}
    ${className}
  `.trim();

  const renderInput = () => {
    const commonProps = {
      value,
      onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => handleChange(e.target.value),
      placeholder,
      disabled,
      className: inputClasses,
      maxLength,
      'aria-invalid': validationResults.errors.length > 0,
      'aria-describedby': showValidation ? `${label}-validation` : undefined
    };

    if (type === 'textarea') {
      return (
        <textarea
          {...commonProps}
          rows={rows}
        />
      );
    }

    if (type === 'password') {
      return (
        <div className="relative">
          <input
            {...commonProps}
            type={showPassword ? 'text' : 'password'}
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
          >
            {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
          </button>
        </div>
      );
    }

    return (
      <input
        {...commonProps}
        type={type}
      />
    );
  };

  return (
    <div className="space-y-1">
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}

      {renderInput()}

      {/* Character count */}
      {maxLength && (
        <div className="text-xs text-gray-500 text-right">
          {value.length}/{maxLength}
        </div>
      )}

      {/* Validation messages */}
      {showValidation && (validationResults.errors.length > 0 || validationResults.warnings.length > 0) && (
        <div id={`${label}-validation`} className="space-y-1">
          {validationResults.errors.map((error, index) => (
            <div key={`error-${index}`} className="flex items-center space-x-1 text-xs text-red-600">
              <AlertCircle className="w-3 h-3" />
              <span>{error}</span>
            </div>
          ))}
          {validationResults.warnings.map((warning, index) => (
            <div key={`warning-${index}`} className="flex items-center space-x-1 text-xs text-yellow-600">
              <AlertCircle className="w-3 h-3" />
              <span>{warning}</span>
            </div>
          ))}
        </div>
      )}

      {/* Success indicator */}
      {showValidation && value.length > 0 && validationResults.isValid && validationResults.warnings.length === 0 && (
        <div className="flex items-center space-x-1 text-xs text-green-600">
          <CheckCircle className="w-3 h-3" />
          <span>Valid input</span>
        </div>
      )}
    </div>
  );
}
